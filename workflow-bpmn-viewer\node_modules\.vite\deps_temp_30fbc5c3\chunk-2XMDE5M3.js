// node_modules/inherits-browser/dist/index.es.js
function e(e2, t) {
  t && (e2.super_ = t, e2.prototype = Object.create(t.prototype, { constructor: { value: e2, enumerable: false, writable: true, configurable: true } }));
}

// node_modules/min-dash/dist/index.esm.js
var nativeToString = Object.prototype.toString;
var nativeHasOwnProperty = Object.prototype.hasOwnProperty;
function isUndefined(obj) {
  return obj === void 0;
}
function isDefined(obj) {
  return obj !== void 0;
}
function isNil(obj) {
  return obj == null;
}
function isArray(obj) {
  return nativeToString.call(obj) === "[object Array]";
}
function isObject(obj) {
  return nativeToString.call(obj) === "[object Object]";
}
function isNumber(obj) {
  return nativeToString.call(obj) === "[object Number]";
}
function isFunction(obj) {
  const tag = nativeToString.call(obj);
  return tag === "[object Function]" || tag === "[object AsyncFunction]" || tag === "[object GeneratorFunction]" || tag === "[object AsyncGeneratorFunction]" || tag === "[object Proxy]";
}
function isString(obj) {
  return nativeToString.call(obj) === "[object String]";
}
function has(target, key) {
  return !isNil(target) && nativeHasOwnProperty.call(target, key);
}
function find(collection, matcher) {
  const matchFn = toMatcher(matcher);
  let match;
  forEach(collection, function(val, key) {
    if (matchFn(val, key)) {
      match = val;
      return false;
    }
  });
  return match;
}
function findIndex(collection, matcher) {
  const matchFn = toMatcher(matcher);
  let idx = isArray(collection) ? -1 : void 0;
  forEach(collection, function(val, key) {
    if (matchFn(val, key)) {
      idx = key;
      return false;
    }
  });
  return idx;
}
function filter(collection, matcher) {
  const matchFn = toMatcher(matcher);
  let result = [];
  forEach(collection, function(val, key) {
    if (matchFn(val, key)) {
      result.push(val);
    }
  });
  return result;
}
function forEach(collection, iterator) {
  let val, result;
  if (isUndefined(collection)) {
    return;
  }
  const convertKey = isArray(collection) ? toNum : identity;
  for (let key in collection) {
    if (has(collection, key)) {
      val = collection[key];
      result = iterator(val, convertKey(key));
      if (result === false) {
        return val;
      }
    }
  }
}
function reduce(collection, iterator, result) {
  forEach(collection, function(value, idx) {
    result = iterator(result, value, idx);
  });
  return result;
}
function every(collection, matcher) {
  return !!reduce(collection, function(matches2, val, key) {
    return matches2 && matcher(val, key);
  }, true);
}
function some(collection, matcher) {
  return !!find(collection, matcher);
}
function map(collection, fn) {
  let result = [];
  forEach(collection, function(val, key) {
    result.push(fn(val, key));
  });
  return result;
}
function matchPattern(pattern) {
  return function(el) {
    return every(pattern, function(val, key) {
      return el[key] === val;
    });
  };
}
function toMatcher(matcher) {
  return isFunction(matcher) ? matcher : (e2) => {
    return e2 === matcher;
  };
}
function identity(arg) {
  return arg;
}
function toNum(arg) {
  return Number(arg);
}
function debounce(fn, timeout) {
  let timer;
  let lastArgs;
  let lastThis;
  let lastNow;
  function fire(force) {
    let now = Date.now();
    let scheduledDiff = force ? 0 : lastNow + timeout - now;
    if (scheduledDiff > 0) {
      return schedule(scheduledDiff);
    }
    fn.apply(lastThis, lastArgs);
    clear3();
  }
  function schedule(timeout2) {
    timer = setTimeout(fire, timeout2);
  }
  function clear3() {
    if (timer) {
      clearTimeout(timer);
    }
    timer = lastNow = lastArgs = lastThis = void 0;
  }
  function flush() {
    if (timer) {
      fire(true);
    }
    clear3();
  }
  function callback(...args) {
    lastNow = Date.now();
    lastArgs = args;
    lastThis = this;
    if (!timer) {
      schedule(timeout);
    }
  }
  callback.flush = flush;
  callback.cancel = clear3;
  return callback;
}
function bind(fn, target) {
  return fn.bind(target);
}
function assign(target, ...others) {
  return Object.assign(target, ...others);
}
function set(target, path, value) {
  let currentTarget = target;
  forEach(path, function(key, idx) {
    if (typeof key !== "number" && typeof key !== "string") {
      throw new Error("illegal key type: " + typeof key + ". Key should be of type number or string.");
    }
    if (key === "constructor") {
      throw new Error("illegal key: constructor");
    }
    if (key === "__proto__") {
      throw new Error("illegal key: __proto__");
    }
    let nextKey = path[idx + 1];
    let nextTarget = currentTarget[key];
    if (isDefined(nextKey) && isNil(nextTarget)) {
      nextTarget = currentTarget[key] = isNaN(+nextKey) ? {} : [];
    }
    if (isUndefined(nextKey)) {
      if (isUndefined(value)) {
        delete currentTarget[key];
      } else {
        currentTarget[key] = value;
      }
    } else {
      currentTarget = nextTarget;
    }
  });
  return target;
}
function pick(target, properties) {
  let result = {};
  let obj = Object(target);
  forEach(properties, function(prop) {
    if (prop in obj) {
      result[prop] = target[prop];
    }
  });
  return result;
}
function omit(target, properties) {
  let result = {};
  let obj = Object(target);
  forEach(obj, function(prop, key) {
    if (properties.indexOf(key) === -1) {
      result[key] = prop;
    }
  });
  return result;
}

// node_modules/diagram-js/lib/draw/BaseRenderer.js
var DEFAULT_RENDER_PRIORITY = 1e3;
function BaseRenderer(eventBus, renderPriority) {
  var self = this;
  renderPriority = renderPriority || DEFAULT_RENDER_PRIORITY;
  eventBus.on(["render.shape", "render.connection"], renderPriority, function(evt, context) {
    var type = evt.type, element = context.element, visuals = context.gfx, attrs = context.attrs;
    if (self.canRender(element)) {
      if (type === "render.shape") {
        return self.drawShape(visuals, element, attrs);
      } else {
        return self.drawConnection(visuals, element, attrs);
      }
    }
  });
  eventBus.on(["render.getShapePath", "render.getConnectionPath"], renderPriority, function(evt, element) {
    if (self.canRender(element)) {
      if (evt.type === "render.getShapePath") {
        return self.getShapePath(element);
      } else {
        return self.getConnectionPath(element);
      }
    }
  });
}
BaseRenderer.prototype.canRender = function(element) {
};
BaseRenderer.prototype.drawShape = function(visuals, shape) {
};
BaseRenderer.prototype.drawConnection = function(visuals, connection) {
};
BaseRenderer.prototype.getShapePath = function(shape) {
};
BaseRenderer.prototype.getConnectionPath = function(connection) {
};

// node_modules/bpmn-js/lib/util/ModelUtil.js
function is(element, type) {
  var bo = getBusinessObject(element);
  return bo && typeof bo.$instanceOf === "function" && bo.$instanceOf(type);
}
function isAny(element, types3) {
  return some(types3, function(t) {
    return is(element, t);
  });
}
function getBusinessObject(element) {
  return element && element.businessObject || element;
}
function getDi(element) {
  return element && element.di;
}

// node_modules/bpmn-js/lib/util/DiUtil.js
function isExpanded(element, di) {
  if (is(element, "bpmn:CallActivity")) {
    return false;
  }
  if (is(element, "bpmn:SubProcess")) {
    di = di || getDi(element);
    if (di && is(di, "bpmndi:BPMNPlane")) {
      return true;
    }
    return di && !!di.isExpanded;
  }
  if (is(element, "bpmn:Participant")) {
    return !!getBusinessObject(element).processRef;
  }
  return true;
}
function isHorizontal(element) {
  if (!is(element, "bpmn:Participant") && !is(element, "bpmn:Lane")) {
    return void 0;
  }
  var isHorizontal2 = getDi(element).isHorizontal;
  if (isHorizontal2 === void 0) {
    return true;
  }
  return isHorizontal2;
}
function isEventSubProcess(element) {
  return element && !!getBusinessObject(element).triggeredByEvent;
}

// node_modules/diagram-js/lib/util/ModelUtil.js
function isConnection(value) {
  return isObject(value) && has(value, "waypoints");
}
function isLabel(value) {
  return isObject(value) && has(value, "labelTarget");
}

// node_modules/bpmn-js/lib/util/LabelUtil.js
var DEFAULT_LABEL_SIZE = {
  width: 90,
  height: 20
};
var FLOW_LABEL_INDENT = 15;
function isLabelExternal(semantic) {
  return is(semantic, "bpmn:Event") || is(semantic, "bpmn:Gateway") || is(semantic, "bpmn:DataStoreReference") || is(semantic, "bpmn:DataObjectReference") || is(semantic, "bpmn:DataInput") || is(semantic, "bpmn:DataOutput") || is(semantic, "bpmn:SequenceFlow") || is(semantic, "bpmn:MessageFlow") || is(semantic, "bpmn:Group");
}
function getFlowLabelPosition(waypoints) {
  var mid = waypoints.length / 2 - 1;
  var first = waypoints[Math.floor(mid)];
  var second = waypoints[Math.ceil(mid + 0.01)];
  var position = getWaypointsMid(waypoints);
  var angle = Math.atan((second.y - first.y) / (second.x - first.x));
  var x = position.x, y = position.y;
  if (Math.abs(angle) < Math.PI / 2) {
    y -= FLOW_LABEL_INDENT;
  } else {
    x += FLOW_LABEL_INDENT;
  }
  return { x, y };
}
function getWaypointsMid(waypoints) {
  var mid = waypoints.length / 2 - 1;
  var first = waypoints[Math.floor(mid)];
  var second = waypoints[Math.ceil(mid + 0.01)];
  return {
    x: first.x + (second.x - first.x) / 2,
    y: first.y + (second.y - first.y) / 2
  };
}
function getExternalLabelMid(element) {
  if (element.waypoints) {
    return getFlowLabelPosition(element.waypoints);
  } else if (is(element, "bpmn:Group")) {
    return {
      x: element.x + element.width / 2,
      y: element.y + DEFAULT_LABEL_SIZE.height / 2
    };
  } else {
    return {
      x: element.x + element.width / 2,
      y: element.y + element.height + DEFAULT_LABEL_SIZE.height / 2
    };
  }
}
function getExternalLabelBounds(di, element) {
  var mid, size, bounds, label = di.label;
  if (label && label.bounds) {
    bounds = label.bounds;
    size = {
      width: Math.max(DEFAULT_LABEL_SIZE.width, bounds.width),
      height: bounds.height
    };
    mid = {
      x: bounds.x + bounds.width / 2,
      y: bounds.y + bounds.height / 2
    };
  } else {
    mid = getExternalLabelMid(element);
    size = DEFAULT_LABEL_SIZE;
  }
  return assign({
    x: mid.x - size.width / 2,
    y: mid.y - size.height / 2
  }, size);
}
function getLabelAttr(semantic) {
  if (is(semantic, "bpmn:FlowElement") || is(semantic, "bpmn:Participant") || is(semantic, "bpmn:Lane") || is(semantic, "bpmn:SequenceFlow") || is(semantic, "bpmn:MessageFlow") || is(semantic, "bpmn:DataInput") || is(semantic, "bpmn:DataOutput")) {
    return "name";
  }
  if (is(semantic, "bpmn:TextAnnotation")) {
    return "text";
  }
  if (is(semantic, "bpmn:Group")) {
    return "categoryValueRef";
  }
}
function getCategoryValue(semantic) {
  var categoryValueRef = semantic["categoryValueRef"];
  if (!categoryValueRef) {
    return "";
  }
  return categoryValueRef.value || "";
}
function getLabel(element) {
  var semantic = element.businessObject, attr3 = getLabelAttr(semantic);
  if (attr3) {
    if (attr3 === "categoryValueRef") {
      return getCategoryValue(semantic);
    }
    return semantic[attr3] || "";
  }
}

// node_modules/tiny-svg/dist/index.esm.js
function ensureImported(element, target) {
  if (element.ownerDocument !== target.ownerDocument) {
    try {
      return target.ownerDocument.importNode(element, true);
    } catch (e2) {
    }
  }
  return element;
}
function appendTo(element, target) {
  return target.appendChild(ensureImported(element, target));
}
function append(target, node2) {
  appendTo(node2, target);
  return target;
}
var LENGTH_ATTR = 2;
var CSS_PROPERTIES = {
  "alignment-baseline": 1,
  "baseline-shift": 1,
  "clip": 1,
  "clip-path": 1,
  "clip-rule": 1,
  "color": 1,
  "color-interpolation": 1,
  "color-interpolation-filters": 1,
  "color-profile": 1,
  "color-rendering": 1,
  "cursor": 1,
  "direction": 1,
  "display": 1,
  "dominant-baseline": 1,
  "enable-background": 1,
  "fill": 1,
  "fill-opacity": 1,
  "fill-rule": 1,
  "filter": 1,
  "flood-color": 1,
  "flood-opacity": 1,
  "font": 1,
  "font-family": 1,
  "font-size": LENGTH_ATTR,
  "font-size-adjust": 1,
  "font-stretch": 1,
  "font-style": 1,
  "font-variant": 1,
  "font-weight": 1,
  "glyph-orientation-horizontal": 1,
  "glyph-orientation-vertical": 1,
  "image-rendering": 1,
  "kerning": 1,
  "letter-spacing": 1,
  "lighting-color": 1,
  "marker": 1,
  "marker-end": 1,
  "marker-mid": 1,
  "marker-start": 1,
  "mask": 1,
  "opacity": 1,
  "overflow": 1,
  "pointer-events": 1,
  "shape-rendering": 1,
  "stop-color": 1,
  "stop-opacity": 1,
  "stroke": 1,
  "stroke-dasharray": 1,
  "stroke-dashoffset": 1,
  "stroke-linecap": 1,
  "stroke-linejoin": 1,
  "stroke-miterlimit": 1,
  "stroke-opacity": 1,
  "stroke-width": LENGTH_ATTR,
  "text-anchor": 1,
  "text-decoration": 1,
  "text-rendering": 1,
  "unicode-bidi": 1,
  "visibility": 1,
  "word-spacing": 1,
  "writing-mode": 1
};
function getAttribute(node2, name2) {
  if (CSS_PROPERTIES[name2]) {
    return node2.style[name2];
  } else {
    return node2.getAttributeNS(null, name2);
  }
}
function setAttribute(node2, name2, value) {
  var hyphenated = name2.replace(/([a-z])([A-Z])/g, "$1-$2").toLowerCase();
  var type = CSS_PROPERTIES[hyphenated];
  if (type) {
    if (type === LENGTH_ATTR && typeof value === "number") {
      value = String(value) + "px";
    }
    node2.style[hyphenated] = value;
  } else {
    node2.setAttributeNS(null, name2, value);
  }
}
function setAttributes(node2, attrs) {
  var names = Object.keys(attrs), i, name2;
  for (i = 0, name2; name2 = names[i]; i++) {
    setAttribute(node2, name2, attrs[name2]);
  }
}
function attr(node2, name2, value) {
  if (typeof name2 === "string") {
    if (value !== void 0) {
      setAttribute(node2, name2, value);
    } else {
      return getAttribute(node2, name2);
    }
  } else {
    setAttributes(node2, name2);
  }
  return node2;
}
var toString = Object.prototype.toString;
function classes(el) {
  return new ClassList(el);
}
function ClassList(el) {
  if (!el || !el.nodeType) {
    throw new Error("A DOM element reference is required");
  }
  this.el = el;
  this.list = el.classList;
}
ClassList.prototype.add = function(name2) {
  this.list.add(name2);
  return this;
};
ClassList.prototype.remove = function(name2) {
  if ("[object RegExp]" == toString.call(name2)) {
    return this.removeMatching(name2);
  }
  this.list.remove(name2);
  return this;
};
ClassList.prototype.removeMatching = function(re) {
  const arr = this.array();
  for (let i = 0; i < arr.length; i++) {
    if (re.test(arr[i])) {
      this.remove(arr[i]);
    }
  }
  return this;
};
ClassList.prototype.toggle = function(name2, force) {
  if ("undefined" !== typeof force) {
    if (force !== this.list.toggle(name2, force)) {
      this.list.toggle(name2);
    }
  } else {
    this.list.toggle(name2);
  }
  return this;
};
ClassList.prototype.array = function() {
  return Array.from(this.list);
};
ClassList.prototype.has = ClassList.prototype.contains = function(name2) {
  return this.list.contains(name2);
};
function clear(element) {
  var child;
  while (child = element.firstChild) {
    element.removeChild(child);
  }
  return element;
}
var ns = {
  svg: "http://www.w3.org/2000/svg"
};
var SVG_START = '<svg xmlns="' + ns.svg + '"';
function parse(svg) {
  var unwrap = false;
  if (svg.substring(0, 4) === "<svg") {
    if (svg.indexOf(ns.svg) === -1) {
      svg = SVG_START + svg.substring(4);
    }
  } else {
    svg = SVG_START + ">" + svg + "</svg>";
    unwrap = true;
  }
  var parsed = parseDocument(svg);
  if (!unwrap) {
    return parsed;
  }
  var fragment = document.createDocumentFragment();
  var parent = parsed.firstChild;
  while (parent.firstChild) {
    fragment.appendChild(parent.firstChild);
  }
  return fragment;
}
function parseDocument(svg) {
  var parser;
  parser = new DOMParser();
  parser.async = false;
  return parser.parseFromString(svg, "text/xml");
}
function create(name2, attrs) {
  var element;
  name2 = name2.trim();
  if (name2.charAt(0) === "<") {
    element = parse(name2).firstChild;
    element = document.importNode(element, true);
  } else {
    element = document.createElementNS(ns.svg, name2);
  }
  if (attrs) {
    attr(element, attrs);
  }
  return element;
}
var node = null;
function getNode() {
  if (node === null) {
    node = create("svg");
  }
  return node;
}
function extend(object, props) {
  var i, k, keys = Object.keys(props);
  for (i = 0; k = keys[i]; i++) {
    object[k] = props[k];
  }
  return object;
}
function createMatrix(a, b, c, d, e2, f) {
  var matrix = getNode().createSVGMatrix();
  switch (arguments.length) {
    case 0:
      return matrix;
    case 1:
      return extend(matrix, a);
    case 6:
      return extend(matrix, {
        a,
        b,
        c,
        d,
        e: e2,
        f
      });
  }
}
function createTransform(matrix) {
  if (matrix) {
    return getNode().createSVGTransformFromMatrix(matrix);
  } else {
    return getNode().createSVGTransform();
  }
}
var TEXT_ENTITIES = /([&<>]{1})/g;
var ATTR_ENTITIES = /([&<>\n\r"]{1})/g;
var ENTITY_REPLACEMENT = {
  "&": "&amp;",
  "<": "&lt;",
  ">": "&gt;",
  '"': "'"
};
function escape(str, pattern) {
  function replaceFn(match, entity) {
    return ENTITY_REPLACEMENT[entity] || entity;
  }
  return str.replace(pattern, replaceFn);
}
function serialize(node2, output) {
  var i, len, attrMap, attrNode, childNodes;
  switch (node2.nodeType) {
    case 3:
      output.push(escape(node2.textContent, TEXT_ENTITIES));
      break;
    case 1:
      output.push("<", node2.tagName);
      if (node2.hasAttributes()) {
        attrMap = node2.attributes;
        for (i = 0, len = attrMap.length; i < len; ++i) {
          attrNode = attrMap.item(i);
          output.push(" ", attrNode.name, '="', escape(attrNode.value, ATTR_ENTITIES), '"');
        }
      }
      if (node2.hasChildNodes()) {
        output.push(">");
        childNodes = node2.childNodes;
        for (i = 0, len = childNodes.length; i < len; ++i) {
          serialize(childNodes.item(i), output);
        }
        output.push("</", node2.tagName, ">");
      } else {
        output.push("/>");
      }
      break;
    case 8:
      output.push("<!--", escape(node2.nodeValue, TEXT_ENTITIES), "-->");
      break;
    case 4:
      output.push("<![CDATA[", node2.nodeValue, "]]>");
      break;
    default:
      throw new Error("unable to handle node " + node2.nodeType);
  }
  return output;
}
function set2(element, svg) {
  var parsed = parse(svg);
  clear(element);
  if (!svg) {
    return;
  }
  if (!isFragment(parsed)) {
    parsed = parsed.documentElement;
  }
  var nodes = slice(parsed.childNodes);
  for (var i = 0; i < nodes.length; i++) {
    appendTo(nodes[i], element);
  }
}
function get(element) {
  var child = element.firstChild, output = [];
  while (child) {
    serialize(child, output);
    child = child.nextSibling;
  }
  return output.join("");
}
function isFragment(node2) {
  return node2.nodeName === "#document-fragment";
}
function innerSVG(element, svg) {
  if (svg !== void 0) {
    try {
      set2(element, svg);
    } catch (e2) {
      throw new Error("error parsing SVG: " + e2.message);
    }
    return element;
  } else {
    return get(element);
  }
}
function slice(arr) {
  return Array.prototype.slice.call(arr);
}
function remove(element) {
  var parent = element.parentNode;
  if (parent) {
    parent.removeChild(element);
  }
  return element;
}
function wrapMatrix(transformList, transform3) {
  if (transform3 instanceof SVGMatrix) {
    return transformList.createSVGTransformFromMatrix(transform3);
  }
  return transform3;
}
function setTransforms(transformList, transforms) {
  var i, t;
  transformList.clear();
  for (i = 0; t = transforms[i]; i++) {
    transformList.appendItem(wrapMatrix(transformList, t));
  }
}
function transform(node2, transforms) {
  var transformList = node2.transform.baseVal;
  if (transforms) {
    if (!Array.isArray(transforms)) {
      transforms = [transforms];
    }
    setTransforms(transformList, transforms);
  }
  return transformList.consolidate();
}

// node_modules/diagram-js/lib/util/RenderUtil.js
function componentsToPath(elements) {
  return elements.flat().join(",").replace(/,?([A-z]),?/g, "$1");
}
function move(point) {
  return ["M", point.x, point.y];
}
function lineTo(point) {
  return ["L", point.x, point.y];
}
function curveTo(p1, p2, p3) {
  return ["C", p1.x, p1.y, p2.x, p2.y, p3.x, p3.y];
}
function drawPath(waypoints, cornerRadius) {
  const pointCount = waypoints.length;
  const path = [move(waypoints[0])];
  for (let i = 1; i < pointCount; i++) {
    const pointBefore = waypoints[i - 1];
    const point = waypoints[i];
    const pointAfter = waypoints[i + 1];
    if (!pointAfter || !cornerRadius) {
      path.push(lineTo(point));
      continue;
    }
    const effectiveRadius = Math.min(
      cornerRadius,
      vectorLength(point.x - pointBefore.x, point.y - pointBefore.y),
      vectorLength(pointAfter.x - point.x, pointAfter.y - point.y)
    );
    if (!effectiveRadius) {
      path.push(lineTo(point));
      continue;
    }
    const beforePoint = getPointAtLength(point, pointBefore, effectiveRadius);
    const beforePoint2 = getPointAtLength(point, pointBefore, effectiveRadius * 0.5);
    const afterPoint = getPointAtLength(point, pointAfter, effectiveRadius);
    const afterPoint2 = getPointAtLength(point, pointAfter, effectiveRadius * 0.5);
    path.push(lineTo(beforePoint));
    path.push(curveTo(beforePoint2, afterPoint2, afterPoint));
  }
  return path;
}
function getPointAtLength(start, end, length) {
  const deltaX = end.x - start.x;
  const deltaY = end.y - start.y;
  const totalLength = vectorLength(deltaX, deltaY);
  const percent = length / totalLength;
  return {
    x: start.x + deltaX * percent,
    y: start.y + deltaY * percent
  };
}
function vectorLength(x, y) {
  return Math.sqrt(Math.pow(x, 2) + Math.pow(y, 2));
}
function createLine(points, attrs, radius) {
  if (isNumber(attrs)) {
    radius = attrs;
    attrs = null;
  }
  if (!attrs) {
    attrs = {};
  }
  const line = create("path", attrs);
  if (isNumber(radius)) {
    line.dataset.cornerRadius = String(radius);
  }
  return updateLine(line, points);
}
function updateLine(gfx, points) {
  const cornerRadius = parseInt(gfx.dataset.cornerRadius, 10) || 0;
  attr(gfx, {
    d: componentsToPath(drawPath(points, cornerRadius))
  });
  return gfx;
}

// node_modules/bpmn-js/lib/draw/BpmnRenderUtil.js
var black = "hsl(225, 10%, 15%)";
var white = "white";
function isTypedEvent(event2, eventDefinitionType) {
  return some(event2.eventDefinitions, function(definition) {
    return definition.$type === eventDefinitionType;
  });
}
function isThrowEvent(event2) {
  return event2.$type === "bpmn:IntermediateThrowEvent" || event2.$type === "bpmn:EndEvent";
}
function isCollection(element) {
  var dataObject = element.dataObjectRef;
  return element.isCollection || dataObject && dataObject.isCollection;
}
function getFillColor(element, defaultColor, overrideColor) {
  var di = getDi(element);
  return overrideColor || di.get("color:background-color") || di.get("bioc:fill") || defaultColor || white;
}
function getStrokeColor(element, defaultColor, overrideColor) {
  var di = getDi(element);
  return overrideColor || di.get("color:border-color") || di.get("bioc:stroke") || defaultColor || black;
}
function getLabelColor(element, defaultColor, defaultStrokeColor, overrideColor) {
  var di = getDi(element), label = di.get("label");
  return overrideColor || label && label.get("color:color") || defaultColor || getStrokeColor(element, defaultStrokeColor);
}
function getCirclePath(shape) {
  var cx = shape.x + shape.width / 2, cy = shape.y + shape.height / 2, radius = shape.width / 2;
  var circlePath = [
    ["M", cx, cy],
    ["m", 0, -radius],
    ["a", radius, radius, 0, 1, 1, 0, 2 * radius],
    ["a", radius, radius, 0, 1, 1, 0, -2 * radius],
    ["z"]
  ];
  return componentsToPath(circlePath);
}
function getRoundRectPath(shape, borderRadius) {
  var x = shape.x, y = shape.y, width = shape.width, height = shape.height;
  var roundRectPath = [
    ["M", x + borderRadius, y],
    ["l", width - borderRadius * 2, 0],
    ["a", borderRadius, borderRadius, 0, 0, 1, borderRadius, borderRadius],
    ["l", 0, height - borderRadius * 2],
    ["a", borderRadius, borderRadius, 0, 0, 1, -borderRadius, borderRadius],
    ["l", borderRadius * 2 - width, 0],
    ["a", borderRadius, borderRadius, 0, 0, 1, -borderRadius, -borderRadius],
    ["l", 0, borderRadius * 2 - height],
    ["a", borderRadius, borderRadius, 0, 0, 1, borderRadius, -borderRadius],
    ["z"]
  ];
  return componentsToPath(roundRectPath);
}
function getDiamondPath(shape) {
  var width = shape.width, height = shape.height, x = shape.x, y = shape.y, halfWidth = width / 2, halfHeight = height / 2;
  var diamondPath = [
    ["M", x + halfWidth, y],
    ["l", halfWidth, halfHeight],
    ["l", -halfWidth, halfHeight],
    ["l", -halfWidth, -halfHeight],
    ["z"]
  ];
  return componentsToPath(diamondPath);
}
function getRectPath(shape) {
  var x = shape.x, y = shape.y, width = shape.width, height = shape.height;
  var rectPath = [
    ["M", x, y],
    ["l", width, 0],
    ["l", 0, height],
    ["l", -width, 0],
    ["z"]
  ];
  return componentsToPath(rectPath);
}
function getBounds(bounds, overrides = {}) {
  return {
    width: getWidth(bounds, overrides),
    height: getHeight(bounds, overrides)
  };
}
function getWidth(bounds, overrides = {}) {
  return has(overrides, "width") ? overrides.width : bounds.width;
}
function getHeight(bounds, overrides = {}) {
  return has(overrides, "height") ? overrides.height : bounds.height;
}

// node_modules/min-dom/dist/index.esm.js
function _mergeNamespaces(n, m) {
  m.forEach(function(e2) {
    e2 && typeof e2 !== "string" && !Array.isArray(e2) && Object.keys(e2).forEach(function(k) {
      if (k !== "default" && !(k in n)) {
        var d = Object.getOwnPropertyDescriptor(e2, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function() {
            return e2[k];
          }
        });
      }
    });
  });
  return Object.freeze(n);
}
var nativeToString2 = Object.prototype.toString;
var nativeHasOwnProperty2 = Object.prototype.hasOwnProperty;
function isUndefined2(obj) {
  return obj === void 0;
}
function isArray2(obj) {
  return nativeToString2.call(obj) === "[object Array]";
}
function has2(target, key) {
  return nativeHasOwnProperty2.call(target, key);
}
function forEach2(collection, iterator) {
  let val, result;
  if (isUndefined2(collection)) {
    return;
  }
  const convertKey = isArray2(collection) ? toNum2 : identity2;
  for (let key in collection) {
    if (has2(collection, key)) {
      val = collection[key];
      result = iterator(val, convertKey(key));
      if (result === false) {
        return val;
      }
    }
  }
}
function identity2(arg) {
  return arg;
}
function toNum2(arg) {
  return Number(arg);
}
function assign2(element, ...styleSources) {
  const target = element.style;
  forEach2(styleSources, function(style) {
    if (!style) {
      return;
    }
    forEach2(style, function(value, key) {
      target[key] = value;
    });
  });
  return element;
}
function attr2(el, name2, val) {
  if (arguments.length == 2) {
    return el.getAttribute(name2);
  }
  if (val === null) {
    return el.removeAttribute(name2);
  }
  el.setAttribute(name2, val);
  return el;
}
var toString2 = Object.prototype.toString;
function classes2(el) {
  return new ClassList2(el);
}
function ClassList2(el) {
  if (!el || !el.nodeType) {
    throw new Error("A DOM element reference is required");
  }
  this.el = el;
  this.list = el.classList;
}
ClassList2.prototype.add = function(name2) {
  this.list.add(name2);
  return this;
};
ClassList2.prototype.remove = function(name2) {
  if ("[object RegExp]" == toString2.call(name2)) {
    return this.removeMatching(name2);
  }
  this.list.remove(name2);
  return this;
};
ClassList2.prototype.removeMatching = function(re) {
  const arr = this.array();
  for (let i = 0; i < arr.length; i++) {
    if (re.test(arr[i])) {
      this.remove(arr[i]);
    }
  }
  return this;
};
ClassList2.prototype.toggle = function(name2, force) {
  if ("undefined" !== typeof force) {
    if (force !== this.list.toggle(name2, force)) {
      this.list.toggle(name2);
    }
  } else {
    this.list.toggle(name2);
  }
  return this;
};
ClassList2.prototype.array = function() {
  return Array.from(this.list);
};
ClassList2.prototype.has = ClassList2.prototype.contains = function(name2) {
  return this.list.contains(name2);
};
function clear2(element) {
  var child;
  while (child = element.firstChild) {
    element.removeChild(child);
  }
  return element;
}
function closest(element, selector, checkYourSelf) {
  var actualElement = checkYourSelf ? element : element.parentNode;
  return actualElement && typeof actualElement.closest === "function" && actualElement.closest(selector) || null;
}
var componentEvent = {};
var bind$1;
var unbind$1;
var prefix;
function detect() {
  bind$1 = window.addEventListener ? "addEventListener" : "attachEvent";
  unbind$1 = window.removeEventListener ? "removeEventListener" : "detachEvent";
  prefix = bind$1 !== "addEventListener" ? "on" : "";
}
var bind_1 = componentEvent.bind = function(el, type, fn, capture) {
  if (!bind$1) detect();
  el[bind$1](prefix + type, fn, capture || false);
  return fn;
};
var unbind_1 = componentEvent.unbind = function(el, type, fn, capture) {
  if (!unbind$1) detect();
  el[unbind$1](prefix + type, fn, capture || false);
  return fn;
};
var event = _mergeNamespaces({
  __proto__: null,
  bind: bind_1,
  unbind: unbind_1,
  "default": componentEvent
}, [componentEvent]);
var forceCaptureEvents = ["focus", "blur"];
function bind2(el, selector, type, fn, capture) {
  if (forceCaptureEvents.indexOf(type) !== -1) {
    capture = true;
  }
  return event.bind(el, type, function(e2) {
    var target = e2.target || e2.srcElement;
    e2.delegateTarget = closest(target, selector, true);
    if (e2.delegateTarget) {
      fn.call(el, e2);
    }
  }, capture);
}
function unbind(el, type, fn, capture) {
  if (forceCaptureEvents.indexOf(type) !== -1) {
    capture = true;
  }
  return event.unbind(el, type, fn, capture);
}
var delegate = {
  bind: bind2,
  unbind
};
var domify = parse2;
var innerHTMLBug = false;
var bugTestDiv;
if (typeof document !== "undefined") {
  bugTestDiv = document.createElement("div");
  bugTestDiv.innerHTML = '  <link/><table></table><a href="/a">a</a><input type="checkbox"/>';
  innerHTMLBug = !bugTestDiv.getElementsByTagName("link").length;
  bugTestDiv = void 0;
}
var map2 = {
  legend: [1, "<fieldset>", "</fieldset>"],
  tr: [2, "<table><tbody>", "</tbody></table>"],
  col: [2, "<table><tbody></tbody><colgroup>", "</colgroup></table>"],
  // for script/link/style tags to work in IE6-8, you have to wrap
  // in a div with a non-whitespace character in front, ha!
  _default: innerHTMLBug ? [1, "X<div>", "</div>"] : [0, "", ""]
};
map2.td = map2.th = [3, "<table><tbody><tr>", "</tr></tbody></table>"];
map2.option = map2.optgroup = [1, '<select multiple="multiple">', "</select>"];
map2.thead = map2.tbody = map2.colgroup = map2.caption = map2.tfoot = [1, "<table>", "</table>"];
map2.polyline = map2.ellipse = map2.polygon = map2.circle = map2.text = map2.line = map2.path = map2.rect = map2.g = [1, '<svg xmlns="http://www.w3.org/2000/svg" version="1.1">', "</svg>"];
function parse2(html, doc) {
  if ("string" != typeof html) throw new TypeError("String expected");
  if (!doc) doc = document;
  var m = /<([\w:]+)/.exec(html);
  if (!m) return doc.createTextNode(html);
  html = html.replace(/^\s+|\s+$/g, "");
  var tag = m[1];
  if (tag == "body") {
    var el = doc.createElement("html");
    el.innerHTML = html;
    return el.removeChild(el.lastChild);
  }
  var wrap = Object.prototype.hasOwnProperty.call(map2, tag) ? map2[tag] : map2._default;
  var depth = wrap[0];
  var prefix3 = wrap[1];
  var suffix = wrap[2];
  var el = doc.createElement("div");
  el.innerHTML = prefix3 + html + suffix;
  while (depth--) el = el.lastChild;
  if (el.firstChild == el.lastChild) {
    return el.removeChild(el.firstChild);
  }
  var fragment = doc.createDocumentFragment();
  while (el.firstChild) {
    fragment.appendChild(el.removeChild(el.firstChild));
  }
  return fragment;
}
var domify$1 = domify;
function matches(element, selector) {
  return element && typeof element.matches === "function" && element.matches(selector) || false;
}
function query(selector, el) {
  el = el || document;
  return el.querySelector(selector);
}
function all(selector, el) {
  el = el || document;
  return el.querySelectorAll(selector);
}
function remove2(el) {
  el.parentNode && el.parentNode.removeChild(el);
}

// node_modules/diagram-js/lib/util/SvgTransformUtil.js
function transform2(gfx, x, y, angle, amount) {
  var translate3 = createTransform();
  translate3.setTranslate(x, y);
  var rotate2 = createTransform();
  rotate2.setRotate(angle || 0, 0, 0);
  var scale = createTransform();
  scale.setScale(amount || 1, amount || 1);
  transform(gfx, [translate3, rotate2, scale]);
}
function translate(gfx, x, y) {
  var translate3 = createTransform();
  translate3.setTranslate(x, y);
  transform(gfx, translate3);
}
function rotate(gfx, angle) {
  var rotate2 = createTransform();
  rotate2.setRotate(angle, 0, 0);
  transform(gfx, rotate2);
}

// node_modules/ids/dist/index.esm.js
function createCommonjsModule(fn, module) {
  return module = { exports: {} }, fn(module, module.exports), module.exports;
}
var hat_1 = createCommonjsModule(function(module) {
  var hat = module.exports = function(bits, base) {
    if (!base) base = 16;
    if (bits === void 0) bits = 128;
    if (bits <= 0) return "0";
    var digits = Math.log(Math.pow(2, bits)) / Math.log(base);
    for (var i = 2; digits === Infinity; i *= 2) {
      digits = Math.log(Math.pow(2, bits / i)) / Math.log(base) * i;
    }
    var rem = digits - Math.floor(digits);
    var res = "";
    for (var i = 0; i < Math.floor(digits); i++) {
      var x = Math.floor(Math.random() * base).toString(base);
      res = x + res;
    }
    if (rem) {
      var b = Math.pow(base, rem);
      var x = Math.floor(Math.random() * b).toString(base);
      res = x + res;
    }
    var parsed = parseInt(res, base);
    if (parsed !== Infinity && parsed >= Math.pow(2, bits)) {
      return hat(bits, base);
    } else return res;
  };
  hat.rack = function(bits, base, expandBy) {
    var fn = function(data) {
      var iters = 0;
      do {
        if (iters++ > 10) {
          if (expandBy) bits += expandBy;
          else throw new Error("too many ID collisions, use more bits");
        }
        var id = hat(bits, base);
      } while (Object.hasOwnProperty.call(hats, id));
      hats[id] = data;
      return id;
    };
    var hats = fn.hats = {};
    fn.get = function(id) {
      return fn.hats[id];
    };
    fn.set = function(id, value) {
      fn.hats[id] = value;
      return fn;
    };
    fn.bits = bits || 128;
    fn.base = base || 16;
    return fn;
  };
});
function Ids(seed) {
  if (!(this instanceof Ids)) {
    return new Ids(seed);
  }
  seed = seed || [128, 36, 1];
  this._seed = seed.length ? hat_1.rack(seed[0], seed[1], seed[2]) : seed;
}
Ids.prototype.next = function(element) {
  return this._seed(element || true);
};
Ids.prototype.nextPrefixed = function(prefix3, element) {
  var id;
  do {
    id = prefix3 + this.next(true);
  } while (this.assigned(id));
  this.claim(id, element);
  return id;
};
Ids.prototype.claim = function(id, element) {
  this._seed.set(id, element || true);
};
Ids.prototype.assigned = function(id) {
  return this._seed.get(id) || false;
};
Ids.prototype.unclaim = function(id) {
  delete this._seed.hats[id];
};
Ids.prototype.clear = function() {
  var hats = this._seed.hats, id;
  for (id in hats) {
    this.unclaim(id);
  }
};
var index_esm_default = Ids;

// node_modules/bpmn-js/lib/draw/BpmnRenderer.js
var markerIds = new index_esm_default();
var ELEMENT_LABEL_DISTANCE = 10;
var INNER_OUTER_DIST = 3;
var PARTICIPANT_STROKE_WIDTH = 1.5;
var TASK_BORDER_RADIUS = 10;
var DEFAULT_OPACITY = 0.95;
var FULL_OPACITY = 1;
var LOW_OPACITY = 0.25;
function BpmnRenderer(config, eventBus, styles, pathMap, canvas, textRenderer, priority) {
  BaseRenderer.call(this, eventBus, priority);
  var defaultFillColor = config && config.defaultFillColor, defaultStrokeColor = config && config.defaultStrokeColor, defaultLabelColor = config && config.defaultLabelColor;
  function shapeStyle(attrs) {
    return styles.computeStyle(attrs, {
      strokeLinecap: "round",
      strokeLinejoin: "round",
      stroke: black,
      strokeWidth: 2,
      fill: "white"
    });
  }
  function lineStyle(attrs) {
    return styles.computeStyle(attrs, ["no-fill"], {
      strokeLinecap: "round",
      strokeLinejoin: "round",
      stroke: black,
      strokeWidth: 2
    });
  }
  function addMarker(id, options) {
    var {
      ref = { x: 0, y: 0 },
      scale = 1,
      element,
      parentGfx = canvas._svg
    } = options;
    var marker2 = create("marker", {
      id,
      viewBox: "0 0 20 20",
      refX: ref.x,
      refY: ref.y,
      markerWidth: 20 * scale,
      markerHeight: 20 * scale,
      orient: "auto"
    });
    append(marker2, element);
    var defs = query(":scope > defs", parentGfx);
    if (!defs) {
      defs = create("defs");
      append(parentGfx, defs);
    }
    append(defs, marker2);
  }
  function marker(parentGfx, type, fill, stroke) {
    var id = markerIds.nextPrefixed("marker-");
    createMarker(parentGfx, id, type, fill, stroke);
    return "url(#" + id + ")";
  }
  function createMarker(parentGfx, id, type, fill, stroke) {
    if (type === "sequenceflow-end") {
      var sequenceflowEnd = create("path", {
        d: "M 1 5 L 11 10 L 1 15 Z",
        ...shapeStyle({
          fill: stroke,
          stroke,
          strokeWidth: 1
        })
      });
      addMarker(id, {
        element: sequenceflowEnd,
        ref: { x: 11, y: 10 },
        scale: 0.5,
        parentGfx
      });
    }
    if (type === "messageflow-start") {
      var messageflowStart = create("circle", {
        cx: 6,
        cy: 6,
        r: 3.5,
        ...shapeStyle({
          fill,
          stroke,
          strokeWidth: 1,
          // fix for safari / chrome / firefox bug not correctly
          // resetting stroke dash array
          strokeDasharray: [1e4, 1]
        })
      });
      addMarker(id, {
        element: messageflowStart,
        ref: { x: 6, y: 6 },
        parentGfx
      });
    }
    if (type === "messageflow-end") {
      var messageflowEnd = create("path", {
        d: "m 1 5 l 0 -3 l 7 3 l -7 3 z",
        ...shapeStyle({
          fill,
          stroke,
          strokeWidth: 1,
          // fix for safari / chrome / firefox bug not correctly
          // resetting stroke dash array
          strokeDasharray: [1e4, 1]
        })
      });
      addMarker(id, {
        element: messageflowEnd,
        ref: { x: 8.5, y: 5 },
        parentGfx
      });
    }
    if (type === "association-start") {
      var associationStart = create("path", {
        d: "M 11 5 L 1 10 L 11 15",
        ...lineStyle({
          fill: "none",
          stroke,
          strokeWidth: 1.5,
          // fix for safari / chrome / firefox bug not correctly
          // resetting stroke dash array
          strokeDasharray: [1e4, 1]
        })
      });
      addMarker(id, {
        element: associationStart,
        ref: { x: 1, y: 10 },
        scale: 0.5,
        parentGfx
      });
    }
    if (type === "association-end") {
      var associationEnd = create("path", {
        d: "M 1 5 L 11 10 L 1 15",
        ...lineStyle({
          fill: "none",
          stroke,
          strokeWidth: 1.5,
          // fix for safari / chrome / firefox bug not correctly
          // resetting stroke dash array
          strokeDasharray: [1e4, 1]
        })
      });
      addMarker(id, {
        element: associationEnd,
        ref: { x: 11, y: 10 },
        scale: 0.5,
        parentGfx
      });
    }
    if (type === "conditional-flow-marker") {
      var conditionalFlowMarker = create("path", {
        d: "M 0 10 L 8 6 L 16 10 L 8 14 Z",
        ...shapeStyle({
          fill,
          stroke
        })
      });
      addMarker(id, {
        element: conditionalFlowMarker,
        ref: { x: -1, y: 10 },
        scale: 0.5,
        parentGfx
      });
    }
    if (type === "conditional-default-flow-marker") {
      var defaultFlowMarker = create("path", {
        d: "M 6 4 L 10 16",
        ...shapeStyle({
          stroke,
          fill: "none"
        })
      });
      addMarker(id, {
        element: defaultFlowMarker,
        ref: { x: 0, y: 10 },
        scale: 0.5,
        parentGfx
      });
    }
  }
  function drawCircle(parentGfx, width, height, offset, attrs = {}) {
    if (isObject(offset)) {
      attrs = offset;
      offset = 0;
    }
    offset = offset || 0;
    attrs = shapeStyle(attrs);
    var cx = width / 2, cy = height / 2;
    var circle = create("circle", {
      cx,
      cy,
      r: Math.round((width + height) / 4 - offset),
      ...attrs
    });
    append(parentGfx, circle);
    return circle;
  }
  function drawRect(parentGfx, width, height, r, offset, attrs) {
    if (isObject(offset)) {
      attrs = offset;
      offset = 0;
    }
    offset = offset || 0;
    attrs = shapeStyle(attrs);
    var rect = create("rect", {
      x: offset,
      y: offset,
      width: width - offset * 2,
      height: height - offset * 2,
      rx: r,
      ry: r,
      ...attrs
    });
    append(parentGfx, rect);
    return rect;
  }
  function drawDiamond(parentGfx, width, height, attrs) {
    var x_2 = width / 2;
    var y_2 = height / 2;
    var points = [
      { x: x_2, y: 0 },
      { x: width, y: y_2 },
      { x: x_2, y: height },
      { x: 0, y: y_2 }
    ];
    var pointsString = points.map(function(point) {
      return point.x + "," + point.y;
    }).join(" ");
    attrs = shapeStyle(attrs);
    var polygon = create("polygon", {
      ...attrs,
      points: pointsString
    });
    append(parentGfx, polygon);
    return polygon;
  }
  function drawLine(parentGfx, waypoints, attrs, radius) {
    attrs = lineStyle(attrs);
    var line = createLine(waypoints, attrs, radius);
    append(parentGfx, line);
    return line;
  }
  function drawConnectionSegments(parentGfx, waypoints, attrs) {
    return drawLine(parentGfx, waypoints, attrs, 5);
  }
  function drawPath2(parentGfx, d, attrs) {
    attrs = lineStyle(attrs);
    var path = create("path", {
      ...attrs,
      d
    });
    append(parentGfx, path);
    return path;
  }
  function drawMarker(type, parentGfx, path, attrs) {
    return drawPath2(parentGfx, path, assign({ "data-marker": type }, attrs));
  }
  function renderer(type) {
    return handlers[type];
  }
  function as(type) {
    return function(parentGfx, element, attrs) {
      return renderer(type)(parentGfx, element, attrs);
    };
  }
  var eventIconRenderers = {
    "bpmn:MessageEventDefinition": function(parentGfx, element, attrs = {}, isThrowing) {
      var pathData = pathMap.getScaledPath("EVENT_MESSAGE", {
        xScaleFactor: 0.9,
        yScaleFactor: 0.9,
        containerWidth: element.width,
        containerHeight: element.height,
        position: {
          mx: 0.235,
          my: 0.315
        }
      });
      var fill = isThrowing ? getStrokeColor(element, defaultStrokeColor, attrs.stroke) : getFillColor(element, defaultFillColor, attrs.fill);
      var stroke = isThrowing ? getFillColor(element, defaultFillColor, attrs.fill) : getStrokeColor(element, defaultStrokeColor, attrs.stroke);
      var messagePath = drawPath2(parentGfx, pathData, {
        fill,
        stroke,
        strokeWidth: 1
      });
      return messagePath;
    },
    "bpmn:TimerEventDefinition": function(parentGfx, element, attrs = {}) {
      var circle = drawCircle(parentGfx, element.width, element.height, 0.2 * element.height, {
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 2
      });
      var pathData = pathMap.getScaledPath("EVENT_TIMER_WH", {
        xScaleFactor: 0.75,
        yScaleFactor: 0.75,
        containerWidth: element.width,
        containerHeight: element.height,
        position: {
          mx: 0.5,
          my: 0.5
        }
      });
      drawPath2(parentGfx, pathData, {
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 2
      });
      for (var i = 0; i < 12; i++) {
        var linePathData = pathMap.getScaledPath("EVENT_TIMER_LINE", {
          xScaleFactor: 0.75,
          yScaleFactor: 0.75,
          containerWidth: element.width,
          containerHeight: element.height,
          position: {
            mx: 0.5,
            my: 0.5
          }
        });
        var width = element.width / 2, height = element.height / 2;
        drawPath2(parentGfx, linePathData, {
          strokeWidth: 1,
          stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
          transform: "rotate(" + i * 30 + "," + height + "," + width + ")"
        });
      }
      return circle;
    },
    "bpmn:EscalationEventDefinition": function(parentGfx, event2, attrs = {}, isThrowing) {
      var pathData = pathMap.getScaledPath("EVENT_ESCALATION", {
        xScaleFactor: 1,
        yScaleFactor: 1,
        containerWidth: event2.width,
        containerHeight: event2.height,
        position: {
          mx: 0.5,
          my: 0.2
        }
      });
      var fill = isThrowing ? getStrokeColor(event2, defaultStrokeColor, attrs.stroke) : getFillColor(event2, defaultFillColor, attrs.fill);
      return drawPath2(parentGfx, pathData, {
        fill,
        stroke: getStrokeColor(event2, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
    },
    "bpmn:ConditionalEventDefinition": function(parentGfx, event2, attrs = {}) {
      var pathData = pathMap.getScaledPath("EVENT_CONDITIONAL", {
        xScaleFactor: 1,
        yScaleFactor: 1,
        containerWidth: event2.width,
        containerHeight: event2.height,
        position: {
          mx: 0.5,
          my: 0.222
        }
      });
      return drawPath2(parentGfx, pathData, {
        fill: getFillColor(event2, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(event2, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
    },
    "bpmn:LinkEventDefinition": function(parentGfx, event2, attrs = {}, isThrowing) {
      var pathData = pathMap.getScaledPath("EVENT_LINK", {
        xScaleFactor: 1,
        yScaleFactor: 1,
        containerWidth: event2.width,
        containerHeight: event2.height,
        position: {
          mx: 0.57,
          my: 0.263
        }
      });
      var fill = isThrowing ? getStrokeColor(event2, defaultStrokeColor, attrs.stroke) : getFillColor(event2, defaultFillColor, attrs.fill);
      return drawPath2(parentGfx, pathData, {
        fill,
        stroke: getStrokeColor(event2, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
    },
    "bpmn:ErrorEventDefinition": function(parentGfx, event2, attrs = {}, isThrowing) {
      var pathData = pathMap.getScaledPath("EVENT_ERROR", {
        xScaleFactor: 1.1,
        yScaleFactor: 1.1,
        containerWidth: event2.width,
        containerHeight: event2.height,
        position: {
          mx: 0.2,
          my: 0.722
        }
      });
      var fill = isThrowing ? getStrokeColor(event2, defaultStrokeColor, attrs.stroke) : getFillColor(event2, defaultFillColor, attrs.fill);
      return drawPath2(parentGfx, pathData, {
        fill,
        stroke: getStrokeColor(event2, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
    },
    "bpmn:CancelEventDefinition": function(parentGfx, event2, attrs = {}, isThrowing) {
      var pathData = pathMap.getScaledPath("EVENT_CANCEL_45", {
        xScaleFactor: 1,
        yScaleFactor: 1,
        containerWidth: event2.width,
        containerHeight: event2.height,
        position: {
          mx: 0.638,
          my: -0.055
        }
      });
      var fill = isThrowing ? getStrokeColor(event2, defaultStrokeColor, attrs.stroke) : "none";
      var path = drawPath2(parentGfx, pathData, {
        fill,
        stroke: getStrokeColor(event2, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
      rotate(path, 45);
      return path;
    },
    "bpmn:CompensateEventDefinition": function(parentGfx, event2, attrs = {}, isThrowing) {
      var pathData = pathMap.getScaledPath("EVENT_COMPENSATION", {
        xScaleFactor: 1,
        yScaleFactor: 1,
        containerWidth: event2.width,
        containerHeight: event2.height,
        position: {
          mx: 0.22,
          my: 0.5
        }
      });
      var fill = isThrowing ? getStrokeColor(event2, defaultStrokeColor, attrs.stroke) : getFillColor(event2, defaultFillColor, attrs.fill);
      return drawPath2(parentGfx, pathData, {
        fill,
        stroke: getStrokeColor(event2, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
    },
    "bpmn:SignalEventDefinition": function(parentGfx, event2, attrs = {}, isThrowing) {
      var pathData = pathMap.getScaledPath("EVENT_SIGNAL", {
        xScaleFactor: 0.9,
        yScaleFactor: 0.9,
        containerWidth: event2.width,
        containerHeight: event2.height,
        position: {
          mx: 0.5,
          my: 0.2
        }
      });
      var fill = isThrowing ? getStrokeColor(event2, defaultStrokeColor, attrs.stroke) : getFillColor(event2, defaultFillColor, attrs.fill);
      return drawPath2(parentGfx, pathData, {
        strokeWidth: 1,
        fill,
        stroke: getStrokeColor(event2, defaultStrokeColor, attrs.stroke)
      });
    },
    "bpmn:MultipleEventDefinition": function(parentGfx, event2, attrs = {}, isThrowing) {
      var pathData = pathMap.getScaledPath("EVENT_MULTIPLE", {
        xScaleFactor: 1.1,
        yScaleFactor: 1.1,
        containerWidth: event2.width,
        containerHeight: event2.height,
        position: {
          mx: 0.222,
          my: 0.36
        }
      });
      var fill = isThrowing ? getStrokeColor(event2, defaultStrokeColor, attrs.stroke) : getFillColor(event2, defaultFillColor, attrs.fill);
      return drawPath2(parentGfx, pathData, {
        fill,
        strokeWidth: 1
      });
    },
    "bpmn:ParallelMultipleEventDefinition": function(parentGfx, event2, attrs = {}) {
      var pathData = pathMap.getScaledPath("EVENT_PARALLEL_MULTIPLE", {
        xScaleFactor: 1.2,
        yScaleFactor: 1.2,
        containerWidth: event2.width,
        containerHeight: event2.height,
        position: {
          mx: 0.458,
          my: 0.194
        }
      });
      return drawPath2(parentGfx, pathData, {
        fill: getFillColor(event2, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(event2, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
    },
    "bpmn:TerminateEventDefinition": function(parentGfx, element, attrs = {}) {
      var circle = drawCircle(parentGfx, element.width, element.height, 8, {
        fill: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 4
      });
      return circle;
    }
  };
  function renderEventIcon(element, parentGfx, attrs = {}) {
    var semantic = getBusinessObject(element), isThrowing = isThrowEvent(semantic);
    if (semantic.get("eventDefinitions") && semantic.get("eventDefinitions").length > 1) {
      if (semantic.get("parallelMultiple")) {
        return eventIconRenderers["bpmn:ParallelMultipleEventDefinition"](parentGfx, element, attrs, isThrowing);
      } else {
        return eventIconRenderers["bpmn:MultipleEventDefinition"](parentGfx, element, attrs, isThrowing);
      }
    }
    if (isTypedEvent(semantic, "bpmn:MessageEventDefinition")) {
      return eventIconRenderers["bpmn:MessageEventDefinition"](parentGfx, element, attrs, isThrowing);
    }
    if (isTypedEvent(semantic, "bpmn:TimerEventDefinition")) {
      return eventIconRenderers["bpmn:TimerEventDefinition"](parentGfx, element, attrs, isThrowing);
    }
    if (isTypedEvent(semantic, "bpmn:ConditionalEventDefinition")) {
      return eventIconRenderers["bpmn:ConditionalEventDefinition"](parentGfx, element, attrs, isThrowing);
    }
    if (isTypedEvent(semantic, "bpmn:SignalEventDefinition")) {
      return eventIconRenderers["bpmn:SignalEventDefinition"](parentGfx, element, attrs, isThrowing);
    }
    if (isTypedEvent(semantic, "bpmn:EscalationEventDefinition")) {
      return eventIconRenderers["bpmn:EscalationEventDefinition"](parentGfx, element, attrs, isThrowing);
    }
    if (isTypedEvent(semantic, "bpmn:LinkEventDefinition")) {
      return eventIconRenderers["bpmn:LinkEventDefinition"](parentGfx, element, attrs, isThrowing);
    }
    if (isTypedEvent(semantic, "bpmn:ErrorEventDefinition")) {
      return eventIconRenderers["bpmn:ErrorEventDefinition"](parentGfx, element, attrs, isThrowing);
    }
    if (isTypedEvent(semantic, "bpmn:CancelEventDefinition")) {
      return eventIconRenderers["bpmn:CancelEventDefinition"](parentGfx, element, attrs, isThrowing);
    }
    if (isTypedEvent(semantic, "bpmn:CompensateEventDefinition")) {
      return eventIconRenderers["bpmn:CompensateEventDefinition"](parentGfx, element, attrs, isThrowing);
    }
    if (isTypedEvent(semantic, "bpmn:TerminateEventDefinition")) {
      return eventIconRenderers["bpmn:TerminateEventDefinition"](parentGfx, element, attrs, isThrowing);
    }
    return null;
  }
  var taskMarkerRenderers = {
    "ParticipantMultiplicityMarker": function(parentGfx, element, attrs = {}) {
      var width = getWidth(element, attrs), height = getHeight(element, attrs);
      var markerPath = pathMap.getScaledPath("MARKER_PARALLEL", {
        xScaleFactor: 1,
        yScaleFactor: 1,
        containerWidth: width,
        containerHeight: height,
        position: {
          mx: (width / 2 - 6) / width,
          my: (height - 15) / height
        }
      });
      drawMarker("participant-multiplicity", parentGfx, markerPath, {
        strokeWidth: 2,
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke)
      });
    },
    "SubProcessMarker": function(parentGfx, element, attrs = {}) {
      var markerRect = drawRect(parentGfx, 14, 14, 0, {
        strokeWidth: 1,
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke)
      });
      translate(markerRect, element.width / 2 - 7.5, element.height - 20);
      var markerPath = pathMap.getScaledPath("MARKER_SUB_PROCESS", {
        xScaleFactor: 1.5,
        yScaleFactor: 1.5,
        containerWidth: element.width,
        containerHeight: element.height,
        position: {
          mx: (element.width / 2 - 7.5) / element.width,
          my: (element.height - 20) / element.height
        }
      });
      drawMarker("sub-process", parentGfx, markerPath, {
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke)
      });
    },
    "ParallelMarker": function(parentGfx, element, attrs) {
      var width = getWidth(element, attrs), height = getHeight(element, attrs);
      var markerPath = pathMap.getScaledPath("MARKER_PARALLEL", {
        xScaleFactor: 1,
        yScaleFactor: 1,
        containerWidth: width,
        containerHeight: height,
        position: {
          mx: (width / 2 + attrs.parallel) / width,
          my: (height - 20) / height
        }
      });
      drawMarker("parallel", parentGfx, markerPath, {
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke)
      });
    },
    "SequentialMarker": function(parentGfx, element, attrs) {
      var markerPath = pathMap.getScaledPath("MARKER_SEQUENTIAL", {
        xScaleFactor: 1,
        yScaleFactor: 1,
        containerWidth: element.width,
        containerHeight: element.height,
        position: {
          mx: (element.width / 2 + attrs.seq) / element.width,
          my: (element.height - 19) / element.height
        }
      });
      drawMarker("sequential", parentGfx, markerPath, {
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke)
      });
    },
    "CompensationMarker": function(parentGfx, element, attrs) {
      var markerMath = pathMap.getScaledPath("MARKER_COMPENSATION", {
        xScaleFactor: 1,
        yScaleFactor: 1,
        containerWidth: element.width,
        containerHeight: element.height,
        position: {
          mx: (element.width / 2 + attrs.compensation) / element.width,
          my: (element.height - 13) / element.height
        }
      });
      drawMarker("compensation", parentGfx, markerMath, {
        strokeWidth: 1,
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke)
      });
    },
    "LoopMarker": function(parentGfx, element, attrs) {
      var width = getWidth(element, attrs), height = getHeight(element, attrs);
      var markerPath = pathMap.getScaledPath("MARKER_LOOP", {
        xScaleFactor: 1,
        yScaleFactor: 1,
        containerWidth: width,
        containerHeight: height,
        position: {
          mx: (width / 2 + attrs.loop) / width,
          my: (height - 7) / height
        }
      });
      drawMarker("loop", parentGfx, markerPath, {
        strokeWidth: 1.5,
        fill: "none",
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeMiterlimit: 0.5
      });
    },
    "AdhocMarker": function(parentGfx, element, attrs) {
      var width = getWidth(element, attrs), height = getHeight(element, attrs);
      var markerPath = pathMap.getScaledPath("MARKER_ADHOC", {
        xScaleFactor: 1,
        yScaleFactor: 1,
        containerWidth: width,
        containerHeight: height,
        position: {
          mx: (width / 2 + attrs.adhoc) / width,
          my: (height - 15) / height
        }
      });
      drawMarker("adhoc", parentGfx, markerPath, {
        strokeWidth: 1,
        fill: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke)
      });
    }
  };
  function renderTaskMarker(type, parentGfx, element, attrs) {
    taskMarkerRenderers[type](parentGfx, element, attrs);
  }
  function renderTaskMarkers(parentGfx, element, taskMarkers, attrs = {}) {
    attrs = {
      fill: attrs.fill,
      stroke: attrs.stroke,
      width: getWidth(element, attrs),
      height: getHeight(element, attrs)
    };
    var semantic = getBusinessObject(element);
    var subprocess = taskMarkers && taskMarkers.includes("SubProcessMarker");
    if (subprocess) {
      attrs = {
        ...attrs,
        seq: -21,
        parallel: -22,
        compensation: -42,
        loop: -18,
        adhoc: 10
      };
    } else {
      attrs = {
        ...attrs,
        seq: -5,
        parallel: -6,
        compensation: -27,
        loop: 0,
        adhoc: 10
      };
    }
    forEach(taskMarkers, function(marker2) {
      renderTaskMarker(marker2, parentGfx, element, attrs);
    });
    if (semantic.get("isForCompensation")) {
      renderTaskMarker("CompensationMarker", parentGfx, element, attrs);
    }
    if (is(semantic, "bpmn:AdHocSubProcess")) {
      renderTaskMarker("AdhocMarker", parentGfx, element, attrs);
    }
    var loopCharacteristics = semantic.get("loopCharacteristics"), isSequential = loopCharacteristics && loopCharacteristics.get("isSequential");
    if (loopCharacteristics) {
      if (isSequential === void 0) {
        renderTaskMarker("LoopMarker", parentGfx, element, attrs);
      }
      if (isSequential === false) {
        renderTaskMarker("ParallelMarker", parentGfx, element, attrs);
      }
      if (isSequential === true) {
        renderTaskMarker("SequentialMarker", parentGfx, element, attrs);
      }
    }
  }
  function renderLabel(parentGfx, label, attrs = {}) {
    attrs = assign({
      size: {
        width: 100
      }
    }, attrs);
    var text = textRenderer.createText(label || "", attrs);
    classes(text).add("djs-label");
    append(parentGfx, text);
    return text;
  }
  function renderEmbeddedLabel(parentGfx, element, align, attrs = {}) {
    var semantic = getBusinessObject(element);
    var box = getBounds({
      x: element.x,
      y: element.y,
      width: element.width,
      height: element.height
    }, attrs);
    return renderLabel(parentGfx, semantic.name, {
      align,
      box,
      padding: 7,
      style: {
        fill: getLabelColor(element, defaultLabelColor, defaultStrokeColor, attrs.stroke)
      }
    });
  }
  function renderExternalLabel(parentGfx, element, attrs = {}) {
    var box = {
      width: 90,
      height: 30,
      x: element.width / 2 + element.x,
      y: element.height / 2 + element.y
    };
    return renderLabel(parentGfx, getLabel(element), {
      box,
      fitBox: true,
      style: assign(
        {},
        textRenderer.getExternalStyle(),
        {
          fill: getLabelColor(element, defaultLabelColor, defaultStrokeColor, attrs.stroke)
        }
      )
    });
  }
  function renderLaneLabel(parentGfx, text, element, attrs = {}) {
    var isHorizontalLane = isHorizontal(element);
    var textBox = renderLabel(parentGfx, text, {
      box: {
        height: 30,
        width: isHorizontalLane ? getHeight(element, attrs) : getWidth(element, attrs)
      },
      align: "center-middle",
      style: {
        fill: getLabelColor(element, defaultLabelColor, defaultStrokeColor, attrs.stroke)
      }
    });
    if (isHorizontalLane) {
      var top = -1 * getHeight(element, attrs);
      transform2(textBox, 0, -top, 270);
    }
  }
  function renderActivity(parentGfx, element, attrs = {}) {
    var {
      width,
      height
    } = getBounds(element, attrs);
    return drawRect(parentGfx, width, height, TASK_BORDER_RADIUS, {
      ...attrs,
      fill: getFillColor(element, defaultFillColor, attrs.fill),
      fillOpacity: DEFAULT_OPACITY,
      stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke)
    });
  }
  function renderAssociation(parentGfx, element, attrs = {}) {
    var semantic = getBusinessObject(element);
    var fill = getFillColor(element, defaultFillColor, attrs.fill), stroke = getStrokeColor(element, defaultStrokeColor, attrs.stroke);
    if (semantic.get("associationDirection") === "One" || semantic.get("associationDirection") === "Both") {
      attrs.markerEnd = marker(parentGfx, "association-end", fill, stroke);
    }
    if (semantic.get("associationDirection") === "Both") {
      attrs.markerStart = marker(parentGfx, "association-start", fill, stroke);
    }
    attrs = pickAttrs(attrs, [
      "markerStart",
      "markerEnd"
    ]);
    return drawConnectionSegments(parentGfx, element.waypoints, {
      ...attrs,
      stroke,
      strokeDasharray: "0, 5"
    });
  }
  function renderDataObject(parentGfx, element, attrs = {}) {
    var fill = getFillColor(element, defaultFillColor, attrs.fill), stroke = getStrokeColor(element, defaultStrokeColor, attrs.stroke);
    var pathData = pathMap.getScaledPath("DATA_OBJECT_PATH", {
      xScaleFactor: 1,
      yScaleFactor: 1,
      containerWidth: element.width,
      containerHeight: element.height,
      position: {
        mx: 0.474,
        my: 0.296
      }
    });
    var dataObject = drawPath2(parentGfx, pathData, {
      fill,
      fillOpacity: DEFAULT_OPACITY,
      stroke
    });
    var semantic = getBusinessObject(element);
    if (isCollection(semantic)) {
      var collectionPathData = pathMap.getScaledPath("DATA_OBJECT_COLLECTION_PATH", {
        xScaleFactor: 1,
        yScaleFactor: 1,
        containerWidth: element.width,
        containerHeight: element.height,
        position: {
          mx: 0.33,
          my: (element.height - 18) / element.height
        }
      });
      drawPath2(parentGfx, collectionPathData, {
        strokeWidth: 2,
        fill,
        stroke
      });
    }
    return dataObject;
  }
  function renderEvent(parentGfx, element, attrs = {}) {
    return drawCircle(parentGfx, element.width, element.height, {
      fillOpacity: DEFAULT_OPACITY,
      ...attrs,
      fill: getFillColor(element, defaultFillColor, attrs.fill),
      stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke)
    });
  }
  function renderGateway(parentGfx, element, attrs = {}) {
    return drawDiamond(parentGfx, element.width, element.height, {
      fill: getFillColor(element, defaultFillColor, attrs.fill),
      fillOpacity: DEFAULT_OPACITY,
      stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke)
    });
  }
  function renderLane(parentGfx, element, attrs = {}) {
    var lane = drawRect(parentGfx, getWidth(element, attrs), getHeight(element, attrs), 0, {
      fill: getFillColor(element, defaultFillColor, attrs.fill),
      fillOpacity: attrs.fillOpacity || DEFAULT_OPACITY,
      stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
      strokeWidth: 1.5
    });
    var semantic = getBusinessObject(element);
    if (is(semantic, "bpmn:Lane")) {
      var text = semantic.get("name");
      renderLaneLabel(parentGfx, text, element, attrs);
    }
    return lane;
  }
  function renderSubProcess(parentGfx, element, attrs = {}) {
    var activity = renderActivity(parentGfx, element, attrs);
    if (isEventSubProcess(element)) {
      attr(activity, {
        strokeDasharray: "0, 5.5",
        strokeWidth: 2.5
      });
    }
    var expanded = isExpanded(element);
    renderEmbeddedLabel(parentGfx, element, expanded ? "center-top" : "center-middle", attrs);
    if (expanded) {
      renderTaskMarkers(parentGfx, element, void 0, attrs);
    } else {
      renderTaskMarkers(parentGfx, element, ["SubProcessMarker"], attrs);
    }
    return activity;
  }
  function renderTask(parentGfx, element, attrs = {}) {
    var activity = renderActivity(parentGfx, element, attrs);
    renderEmbeddedLabel(parentGfx, element, "center-middle", attrs);
    renderTaskMarkers(parentGfx, element, void 0, attrs);
    return activity;
  }
  var handlers = this.handlers = {
    "bpmn:AdHocSubProcess": function(parentGfx, element, attrs = {}) {
      if (isExpanded(element)) {
        attrs = pickAttrs(attrs, [
          "fill",
          "stroke",
          "width",
          "height"
        ]);
      } else {
        attrs = pickAttrs(attrs, [
          "fill",
          "stroke"
        ]);
      }
      return renderSubProcess(parentGfx, element, attrs);
    },
    "bpmn:Association": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      return renderAssociation(parentGfx, element, attrs);
    },
    "bpmn:BoundaryEvent": function(parentGfx, element, attrs = {}) {
      var { renderIcon = true } = attrs;
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var semantic = getBusinessObject(element), cancelActivity = semantic.get("cancelActivity");
      attrs = {
        strokeWidth: 1.5,
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        fillOpacity: FULL_OPACITY,
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke)
      };
      if (!cancelActivity) {
        attrs.strokeDasharray = "6";
      }
      var event2 = renderEvent(parentGfx, element, attrs);
      drawCircle(parentGfx, element.width, element.height, INNER_OUTER_DIST, {
        ...attrs,
        fill: "none"
      });
      if (renderIcon) {
        renderEventIcon(element, parentGfx, attrs);
      }
      return event2;
    },
    "bpmn:BusinessRuleTask": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var task = renderTask(parentGfx, element, attrs);
      var headerData = pathMap.getScaledPath("TASK_TYPE_BUSINESS_RULE_MAIN", {
        abspos: {
          x: 8,
          y: 8
        }
      });
      var businessPath = drawPath2(parentGfx, headerData);
      attr(businessPath, {
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
      var headerPathData = pathMap.getScaledPath("TASK_TYPE_BUSINESS_RULE_HEADER", {
        abspos: {
          x: 8,
          y: 8
        }
      });
      var businessHeaderPath = drawPath2(parentGfx, headerPathData);
      attr(businessHeaderPath, {
        fill: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
      return task;
    },
    "bpmn:CallActivity": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      return renderSubProcess(parentGfx, element, {
        strokeWidth: 5,
        ...attrs
      });
    },
    "bpmn:ComplexGateway": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var gateway = renderGateway(parentGfx, element, attrs);
      var pathData = pathMap.getScaledPath("GATEWAY_COMPLEX", {
        xScaleFactor: 0.5,
        yScaleFactor: 0.5,
        containerWidth: element.width,
        containerHeight: element.height,
        position: {
          mx: 0.46,
          my: 0.26
        }
      });
      drawPath2(parentGfx, pathData, {
        fill: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
      return gateway;
    },
    "bpmn:DataInput": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var arrowPathData = pathMap.getRawPath("DATA_ARROW");
      var dataObject = renderDataObject(parentGfx, element, attrs);
      drawPath2(parentGfx, arrowPathData, {
        fill: "none",
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
      return dataObject;
    },
    "bpmn:DataInputAssociation": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      return renderAssociation(parentGfx, element, {
        ...attrs,
        markerEnd: marker(parentGfx, "association-end", getFillColor(element, defaultFillColor, attrs.fill), getStrokeColor(element, defaultStrokeColor, attrs.stroke))
      });
    },
    "bpmn:DataObject": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      return renderDataObject(parentGfx, element, attrs);
    },
    "bpmn:DataObjectReference": as("bpmn:DataObject"),
    "bpmn:DataOutput": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var arrowPathData = pathMap.getRawPath("DATA_ARROW");
      var dataObject = renderDataObject(parentGfx, element, attrs);
      drawPath2(parentGfx, arrowPathData, {
        strokeWidth: 1,
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke)
      });
      return dataObject;
    },
    "bpmn:DataOutputAssociation": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      return renderAssociation(parentGfx, element, {
        ...attrs,
        markerEnd: marker(parentGfx, "association-end", getFillColor(element, defaultFillColor, attrs.fill), getStrokeColor(element, defaultStrokeColor, attrs.stroke))
      });
    },
    "bpmn:DataStoreReference": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var dataStorePath = pathMap.getScaledPath("DATA_STORE", {
        xScaleFactor: 1,
        yScaleFactor: 1,
        containerWidth: element.width,
        containerHeight: element.height,
        position: {
          mx: 0,
          my: 0.133
        }
      });
      return drawPath2(parentGfx, dataStorePath, {
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        fillOpacity: DEFAULT_OPACITY,
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 2
      });
    },
    "bpmn:EndEvent": function(parentGfx, element, attrs = {}) {
      var { renderIcon = true } = attrs;
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var event2 = renderEvent(parentGfx, element, {
        ...attrs,
        strokeWidth: 4
      });
      if (renderIcon) {
        renderEventIcon(element, parentGfx, attrs);
      }
      return event2;
    },
    "bpmn:EventBasedGateway": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var semantic = getBusinessObject(element);
      var diamond = renderGateway(parentGfx, element, attrs);
      drawCircle(parentGfx, element.width, element.height, element.height * 0.2, {
        fill: getFillColor(element, "none", attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
      var type = semantic.get("eventGatewayType"), instantiate = !!semantic.get("instantiate");
      function drawEvent() {
        var pathData2 = pathMap.getScaledPath("GATEWAY_EVENT_BASED", {
          xScaleFactor: 0.18,
          yScaleFactor: 0.18,
          containerWidth: element.width,
          containerHeight: element.height,
          position: {
            mx: 0.36,
            my: 0.44
          }
        });
        drawPath2(parentGfx, pathData2, {
          fill: "none",
          stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
          strokeWidth: 2
        });
      }
      if (type === "Parallel") {
        var pathData = pathMap.getScaledPath("GATEWAY_PARALLEL", {
          xScaleFactor: 0.4,
          yScaleFactor: 0.4,
          containerWidth: element.width,
          containerHeight: element.height,
          position: {
            mx: 0.474,
            my: 0.296
          }
        });
        drawPath2(parentGfx, pathData, {
          fill: "none",
          stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
          strokeWidth: 1
        });
      } else if (type === "Exclusive") {
        if (!instantiate) {
          drawCircle(parentGfx, element.width, element.height, element.height * 0.26, {
            fill: "none",
            stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
            strokeWidth: 1
          });
        }
        drawEvent();
      }
      return diamond;
    },
    "bpmn:ExclusiveGateway": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var gateway = renderGateway(parentGfx, element, attrs);
      var pathData = pathMap.getScaledPath("GATEWAY_EXCLUSIVE", {
        xScaleFactor: 0.4,
        yScaleFactor: 0.4,
        containerWidth: element.width,
        containerHeight: element.height,
        position: {
          mx: 0.32,
          my: 0.3
        }
      });
      var di = getDi(element);
      if (di.get("isMarkerVisible")) {
        drawPath2(parentGfx, pathData, {
          fill: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
          stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
          strokeWidth: 1
        });
      }
      return gateway;
    },
    "bpmn:Gateway": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      return renderGateway(parentGfx, element, attrs);
    },
    "bpmn:Group": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke",
        "width",
        "height"
      ]);
      return drawRect(parentGfx, element.width, element.height, TASK_BORDER_RADIUS, {
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1.5,
        strokeDasharray: "10, 6, 0, 6",
        fill: "none",
        pointerEvents: "none",
        width: getWidth(element, attrs),
        height: getHeight(element, attrs)
      });
    },
    "bpmn:InclusiveGateway": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var gateway = renderGateway(parentGfx, element, attrs);
      drawCircle(parentGfx, element.width, element.height, element.height * 0.24, {
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 2.5
      });
      return gateway;
    },
    "bpmn:IntermediateEvent": function(parentGfx, element, attrs = {}) {
      var { renderIcon = true } = attrs;
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var outer = renderEvent(parentGfx, element, {
        ...attrs,
        strokeWidth: 1.5
      });
      drawCircle(parentGfx, element.width, element.height, INNER_OUTER_DIST, {
        fill: "none",
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1.5
      });
      if (renderIcon) {
        renderEventIcon(element, parentGfx, attrs);
      }
      return outer;
    },
    "bpmn:IntermediateCatchEvent": as("bpmn:IntermediateEvent"),
    "bpmn:IntermediateThrowEvent": as("bpmn:IntermediateEvent"),
    "bpmn:Lane": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke",
        "width",
        "height"
      ]);
      return renderLane(parentGfx, element, {
        ...attrs,
        fillOpacity: LOW_OPACITY
      });
    },
    "bpmn:ManualTask": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var task = renderTask(parentGfx, element, attrs);
      var pathData = pathMap.getScaledPath("TASK_TYPE_MANUAL", {
        abspos: {
          x: 17,
          y: 15
        }
      });
      drawPath2(parentGfx, pathData, {
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 0.5
      });
      return task;
    },
    "bpmn:MessageFlow": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var semantic = getBusinessObject(element), di = getDi(element);
      var fill = getFillColor(element, defaultFillColor, attrs.fill), stroke = getStrokeColor(element, defaultStrokeColor, attrs.stroke);
      var path = drawConnectionSegments(parentGfx, element.waypoints, {
        markerEnd: marker(parentGfx, "messageflow-end", fill, stroke),
        markerStart: marker(parentGfx, "messageflow-start", fill, stroke),
        stroke,
        strokeDasharray: "10, 11",
        strokeWidth: 1.5
      });
      if (semantic.get("messageRef")) {
        var midPoint = path.getPointAtLength(path.getTotalLength() / 2);
        var markerPathData = pathMap.getScaledPath("MESSAGE_FLOW_MARKER", {
          abspos: {
            x: midPoint.x,
            y: midPoint.y
          }
        });
        var messageAttrs = {
          strokeWidth: 1
        };
        if (di.get("messageVisibleKind") === "initiating") {
          messageAttrs.fill = fill;
          messageAttrs.stroke = stroke;
        } else {
          messageAttrs.fill = stroke;
          messageAttrs.stroke = fill;
        }
        var message = drawPath2(parentGfx, markerPathData, messageAttrs);
        var messageRef = semantic.get("messageRef"), name2 = messageRef.get("name");
        var label = renderLabel(parentGfx, name2, {
          align: "center-top",
          fitBox: true,
          style: {
            fill: stroke
          }
        });
        var messageBounds = message.getBBox(), labelBounds = label.getBBox();
        var translateX = midPoint.x - labelBounds.width / 2, translateY = midPoint.y + messageBounds.height / 2 + ELEMENT_LABEL_DISTANCE;
        transform2(label, translateX, translateY, 0);
      }
      return path;
    },
    "bpmn:ParallelGateway": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var diamond = renderGateway(parentGfx, element, attrs);
      var pathData = pathMap.getScaledPath("GATEWAY_PARALLEL", {
        xScaleFactor: 0.6,
        yScaleFactor: 0.6,
        containerWidth: element.width,
        containerHeight: element.height,
        position: {
          mx: 0.46,
          my: 0.2
        }
      });
      drawPath2(parentGfx, pathData, {
        fill: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
      return diamond;
    },
    "bpmn:Participant": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke",
        "width",
        "height"
      ]);
      var participant = renderLane(parentGfx, element, attrs);
      var expandedParticipant = isExpanded(element);
      var horizontalParticipant = isHorizontal(element);
      var semantic = getBusinessObject(element), name2 = semantic.get("name");
      if (expandedParticipant) {
        var waypoints = horizontalParticipant ? [
          {
            x: 30,
            y: 0
          },
          {
            x: 30,
            y: getHeight(element, attrs)
          }
        ] : [
          {
            x: 0,
            y: 30
          },
          {
            x: getWidth(element, attrs),
            y: 30
          }
        ];
        drawLine(parentGfx, waypoints, {
          stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
          strokeWidth: PARTICIPANT_STROKE_WIDTH
        });
        renderLaneLabel(parentGfx, name2, element, attrs);
      } else {
        var bounds = getBounds(element, attrs);
        if (!horizontalParticipant) {
          bounds.height = getWidth(element, attrs);
          bounds.width = getHeight(element, attrs);
        }
        var textBox = renderLabel(parentGfx, name2, {
          box: bounds,
          align: "center-middle",
          style: {
            fill: getLabelColor(element, defaultLabelColor, defaultStrokeColor, attrs.stroke)
          }
        });
        if (!horizontalParticipant) {
          var top = -1 * getHeight(element, attrs);
          transform2(textBox, 0, -top, 270);
        }
      }
      if (semantic.get("participantMultiplicity")) {
        renderTaskMarker("ParticipantMultiplicityMarker", parentGfx, element, attrs);
      }
      return participant;
    },
    "bpmn:ReceiveTask": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var semantic = getBusinessObject(element);
      var task = renderTask(parentGfx, element, attrs);
      var pathData;
      if (semantic.get("instantiate")) {
        drawCircle(parentGfx, 28, 28, 20 * 0.22, {
          fill: getFillColor(element, defaultFillColor, attrs.fill),
          stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
          strokeWidth: 1
        });
        pathData = pathMap.getScaledPath("TASK_TYPE_INSTANTIATING_SEND", {
          abspos: {
            x: 7.77,
            y: 9.52
          }
        });
      } else {
        pathData = pathMap.getScaledPath("TASK_TYPE_SEND", {
          xScaleFactor: 0.9,
          yScaleFactor: 0.9,
          containerWidth: 21,
          containerHeight: 14,
          position: {
            mx: 0.3,
            my: 0.4
          }
        });
      }
      drawPath2(parentGfx, pathData, {
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
      return task;
    },
    "bpmn:ScriptTask": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var task = renderTask(parentGfx, element, attrs);
      var pathData = pathMap.getScaledPath("TASK_TYPE_SCRIPT", {
        abspos: {
          x: 15,
          y: 20
        }
      });
      drawPath2(parentGfx, pathData, {
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
      return task;
    },
    "bpmn:SendTask": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var task = renderTask(parentGfx, element, attrs);
      var pathData = pathMap.getScaledPath("TASK_TYPE_SEND", {
        xScaleFactor: 1,
        yScaleFactor: 1,
        containerWidth: 21,
        containerHeight: 14,
        position: {
          mx: 0.285,
          my: 0.357
        }
      });
      drawPath2(parentGfx, pathData, {
        fill: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        stroke: getFillColor(element, defaultFillColor, attrs.fill),
        strokeWidth: 1
      });
      return task;
    },
    "bpmn:SequenceFlow": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var fill = getFillColor(element, defaultFillColor, attrs.fill), stroke = getStrokeColor(element, defaultStrokeColor, attrs.stroke);
      var connection = drawConnectionSegments(parentGfx, element.waypoints, {
        markerEnd: marker(parentGfx, "sequenceflow-end", fill, stroke),
        stroke
      });
      var semantic = getBusinessObject(element);
      var { source } = element;
      if (source) {
        var sourceSemantic = getBusinessObject(source);
        if (semantic.get("conditionExpression") && is(sourceSemantic, "bpmn:Activity")) {
          attr(connection, {
            markerStart: marker(parentGfx, "conditional-flow-marker", fill, stroke)
          });
        }
        if (sourceSemantic.get("default") && (is(sourceSemantic, "bpmn:Gateway") || is(sourceSemantic, "bpmn:Activity")) && sourceSemantic.get("default") === semantic) {
          attr(connection, {
            markerStart: marker(parentGfx, "conditional-default-flow-marker", fill, stroke)
          });
        }
      }
      return connection;
    },
    "bpmn:ServiceTask": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var task = renderTask(parentGfx, element, attrs);
      drawCircle(parentGfx, 10, 10, {
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: "none",
        transform: "translate(6, 6)"
      });
      var pathDataService1 = pathMap.getScaledPath("TASK_TYPE_SERVICE", {
        abspos: {
          x: 12,
          y: 18
        }
      });
      drawPath2(parentGfx, pathDataService1, {
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
      drawCircle(parentGfx, 10, 10, {
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: "none",
        transform: "translate(11, 10)"
      });
      var pathDataService2 = pathMap.getScaledPath("TASK_TYPE_SERVICE", {
        abspos: {
          x: 17,
          y: 22
        }
      });
      drawPath2(parentGfx, pathDataService2, {
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1
      });
      return task;
    },
    "bpmn:StartEvent": function(parentGfx, element, attrs = {}) {
      var { renderIcon = true } = attrs;
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var semantic = getBusinessObject(element);
      if (!semantic.get("isInterrupting")) {
        attrs = {
          ...attrs,
          strokeDasharray: "6"
        };
      }
      var event2 = renderEvent(parentGfx, element, attrs);
      if (renderIcon) {
        renderEventIcon(element, parentGfx, attrs);
      }
      return event2;
    },
    "bpmn:SubProcess": function(parentGfx, element, attrs = {}) {
      if (isExpanded(element)) {
        attrs = pickAttrs(attrs, [
          "fill",
          "stroke",
          "width",
          "height"
        ]);
      } else {
        attrs = pickAttrs(attrs, [
          "fill",
          "stroke"
        ]);
      }
      return renderSubProcess(parentGfx, element, attrs);
    },
    "bpmn:Task": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      return renderTask(parentGfx, element, attrs);
    },
    "bpmn:TextAnnotation": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke",
        "width",
        "height"
      ]);
      var {
        width,
        height
      } = getBounds(element, attrs);
      var textElement = drawRect(parentGfx, width, height, 0, 0, {
        fill: "none",
        stroke: "none"
      });
      var textPathData = pathMap.getScaledPath("TEXT_ANNOTATION", {
        xScaleFactor: 1,
        yScaleFactor: 1,
        containerWidth: width,
        containerHeight: height,
        position: {
          mx: 0,
          my: 0
        }
      });
      drawPath2(parentGfx, textPathData, {
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke)
      });
      var semantic = getBusinessObject(element), text = semantic.get("text") || "";
      renderLabel(parentGfx, text, {
        align: "left-top",
        box: getBounds(element, attrs),
        padding: 7,
        style: {
          fill: getLabelColor(element, defaultLabelColor, defaultStrokeColor, attrs.stroke)
        }
      });
      return textElement;
    },
    "bpmn:Transaction": function(parentGfx, element, attrs = {}) {
      if (isExpanded(element)) {
        attrs = pickAttrs(attrs, [
          "fill",
          "stroke",
          "width",
          "height"
        ]);
      } else {
        attrs = pickAttrs(attrs, [
          "fill",
          "stroke"
        ]);
      }
      var outer = renderSubProcess(parentGfx, element, {
        strokeWidth: 1.5,
        ...attrs
      });
      var innerAttrs = styles.style(["no-fill", "no-events"], {
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 1.5
      });
      var expanded = isExpanded(element);
      if (!expanded) {
        attrs = {};
      }
      drawRect(
        parentGfx,
        getWidth(element, attrs),
        getHeight(element, attrs),
        TASK_BORDER_RADIUS - INNER_OUTER_DIST,
        INNER_OUTER_DIST,
        innerAttrs
      );
      return outer;
    },
    "bpmn:UserTask": function(parentGfx, element, attrs = {}) {
      attrs = pickAttrs(attrs, [
        "fill",
        "stroke"
      ]);
      var task = renderTask(parentGfx, element, attrs);
      var x = 15;
      var y = 12;
      var pathDataUser1 = pathMap.getScaledPath("TASK_TYPE_USER_1", {
        abspos: {
          x,
          y
        }
      });
      drawPath2(parentGfx, pathDataUser1, {
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 0.5
      });
      var pathDataUser2 = pathMap.getScaledPath("TASK_TYPE_USER_2", {
        abspos: {
          x,
          y
        }
      });
      drawPath2(parentGfx, pathDataUser2, {
        fill: getFillColor(element, defaultFillColor, attrs.fill),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 0.5
      });
      var pathDataUser3 = pathMap.getScaledPath("TASK_TYPE_USER_3", {
        abspos: {
          x,
          y
        }
      });
      drawPath2(parentGfx, pathDataUser3, {
        fill: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        stroke: getStrokeColor(element, defaultStrokeColor, attrs.stroke),
        strokeWidth: 0.5
      });
      return task;
    },
    "label": function(parentGfx, element, attrs = {}) {
      return renderExternalLabel(parentGfx, element, attrs);
    }
  };
  this._drawPath = drawPath2;
  this._renderer = renderer;
}
e(BpmnRenderer, BaseRenderer);
BpmnRenderer.$inject = [
  "config.bpmnRenderer",
  "eventBus",
  "styles",
  "pathMap",
  "canvas",
  "textRenderer"
];
BpmnRenderer.prototype.canRender = function(element) {
  return is(element, "bpmn:BaseElement");
};
BpmnRenderer.prototype.drawShape = function(parentGfx, shape, attrs = {}) {
  var { type } = shape;
  var handler = this._renderer(type);
  return handler(parentGfx, shape, attrs);
};
BpmnRenderer.prototype.drawConnection = function(parentGfx, connection, attrs = {}) {
  var { type } = connection;
  var handler = this._renderer(type);
  return handler(parentGfx, connection, attrs);
};
BpmnRenderer.prototype.getShapePath = function(shape) {
  if (is(shape, "bpmn:Event")) {
    return getCirclePath(shape);
  }
  if (is(shape, "bpmn:Activity")) {
    return getRoundRectPath(shape, TASK_BORDER_RADIUS);
  }
  if (is(shape, "bpmn:Gateway")) {
    return getDiamondPath(shape);
  }
  return getRectPath(shape);
};
function pickAttrs(attrs, keys = []) {
  return keys.reduce((pickedAttrs, key) => {
    if (attrs[key]) {
      pickedAttrs[key] = attrs[key];
    }
    return pickedAttrs;
  }, {});
}

// node_modules/diagram-js/lib/util/Text.js
var DEFAULT_BOX_PADDING = 0;
var DEFAULT_LABEL_SIZE2 = {
  width: 150,
  height: 50
};
function parseAlign(align) {
  var parts = align.split("-");
  return {
    horizontal: parts[0] || "center",
    vertical: parts[1] || "top"
  };
}
function parsePadding(padding) {
  if (isObject(padding)) {
    return assign({ top: 0, left: 0, right: 0, bottom: 0 }, padding);
  } else {
    return {
      top: padding,
      left: padding,
      right: padding,
      bottom: padding
    };
  }
}
function getTextBBox(text, fakeText) {
  fakeText.textContent = text;
  var textBBox;
  try {
    var bbox, emptyLine = text === "";
    fakeText.textContent = emptyLine ? "dummy" : text;
    textBBox = fakeText.getBBox();
    bbox = {
      width: textBBox.width + textBBox.x * 2,
      height: textBBox.height
    };
    if (emptyLine) {
      bbox.width = 0;
    }
    return bbox;
  } catch (e2) {
    return { width: 0, height: 0 };
  }
}
function layoutNext(lines, maxWidth, fakeText) {
  var originalLine = lines.shift(), fitLine = originalLine;
  var textBBox;
  for (; ; ) {
    textBBox = getTextBBox(fitLine, fakeText);
    textBBox.width = fitLine ? textBBox.width : 0;
    if (fitLine === " " || fitLine === "" || textBBox.width < Math.round(maxWidth) || fitLine.length < 2) {
      return fit(lines, fitLine, originalLine, textBBox);
    }
    fitLine = shortenLine(fitLine, textBBox.width, maxWidth);
  }
}
function fit(lines, fitLine, originalLine, textBBox) {
  if (fitLine.length < originalLine.length) {
    var remainder = originalLine.slice(fitLine.length).trim();
    lines.unshift(remainder);
  }
  return {
    width: textBBox.width,
    height: textBBox.height,
    text: fitLine
  };
}
var SOFT_BREAK = "­";
function semanticShorten(line, maxLength) {
  var parts = line.split(/(\s|-|\u00AD)/g), part, shortenedParts = [], length = 0;
  if (parts.length > 1) {
    while (part = parts.shift()) {
      if (part.length + length < maxLength) {
        shortenedParts.push(part);
        length += part.length;
      } else {
        if (part === "-" || part === SOFT_BREAK) {
          shortenedParts.pop();
        }
        break;
      }
    }
  }
  var last = shortenedParts[shortenedParts.length - 1];
  if (last && last === SOFT_BREAK) {
    shortenedParts[shortenedParts.length - 1] = "-";
  }
  return shortenedParts.join("");
}
function shortenLine(line, width, maxWidth) {
  var length = Math.max(line.length * (maxWidth / width), 1);
  var shortenedLine = semanticShorten(line, length);
  if (!shortenedLine) {
    shortenedLine = line.slice(0, Math.max(Math.round(length - 1), 1));
  }
  return shortenedLine;
}
function getHelperSvg() {
  var helperSvg = document.getElementById("helper-svg");
  if (!helperSvg) {
    helperSvg = create("svg");
    attr(helperSvg, {
      id: "helper-svg"
    });
    assign2(helperSvg, {
      visibility: "hidden",
      position: "fixed",
      width: 0,
      height: 0
    });
    document.body.appendChild(helperSvg);
  }
  return helperSvg;
}
function Text(config) {
  this._config = assign({}, {
    size: DEFAULT_LABEL_SIZE2,
    padding: DEFAULT_BOX_PADDING,
    style: {},
    align: "center-top"
  }, config || {});
}
Text.prototype.createText = function(text, options) {
  return this.layoutText(text, options).element;
};
Text.prototype.getDimensions = function(text, options) {
  return this.layoutText(text, options).dimensions;
};
Text.prototype.layoutText = function(text, options) {
  var box = assign({}, this._config.size, options.box), style = assign({}, this._config.style, options.style), align = parseAlign(options.align || this._config.align), padding = parsePadding(options.padding !== void 0 ? options.padding : this._config.padding), fitBox = options.fitBox || false;
  var lineHeight = getLineHeight(style);
  var lines = text.split(/\u00AD?\r?\n/), layouted = [];
  var maxWidth = box.width - padding.left - padding.right;
  var helperText = create("text");
  attr(helperText, { x: 0, y: 0 });
  attr(helperText, style);
  var helperSvg = getHelperSvg();
  append(helperSvg, helperText);
  while (lines.length) {
    layouted.push(layoutNext(lines, maxWidth, helperText));
  }
  if (align.vertical === "middle") {
    padding.top = padding.bottom = 0;
  }
  var totalHeight = reduce(layouted, function(sum, line, idx) {
    return sum + (lineHeight || line.height);
  }, 0) + padding.top + padding.bottom;
  var maxLineWidth = reduce(layouted, function(sum, line, idx) {
    return line.width > sum ? line.width : sum;
  }, 0);
  var y = padding.top;
  if (align.vertical === "middle") {
    y += (box.height - totalHeight) / 2;
  }
  y -= (lineHeight || layouted[0].height) / 4;
  var textElement = create("text");
  attr(textElement, style);
  forEach(layouted, function(line) {
    var x;
    y += lineHeight || line.height;
    switch (align.horizontal) {
      case "left":
        x = padding.left;
        break;
      case "right":
        x = (fitBox ? maxLineWidth : maxWidth) - padding.right - line.width;
        break;
      default:
        x = Math.max(((fitBox ? maxLineWidth : maxWidth) - line.width) / 2 + padding.left, 0);
    }
    var tspan = create("tspan");
    attr(tspan, { x, y });
    tspan.textContent = line.text;
    append(textElement, tspan);
  });
  remove(helperText);
  var dimensions = {
    width: maxLineWidth,
    height: totalHeight
  };
  return {
    dimensions,
    element: textElement
  };
};
function getLineHeight(style) {
  if ("fontSize" in style && "lineHeight" in style) {
    return style.lineHeight * parseInt(style.fontSize, 10);
  }
}

// node_modules/bpmn-js/lib/draw/TextRenderer.js
var DEFAULT_FONT_SIZE = 12;
var LINE_HEIGHT_RATIO = 1.2;
var MIN_TEXT_ANNOTATION_HEIGHT = 30;
function TextRenderer(config) {
  var defaultStyle = assign({
    fontFamily: "Arial, sans-serif",
    fontSize: DEFAULT_FONT_SIZE,
    fontWeight: "normal",
    lineHeight: LINE_HEIGHT_RATIO
  }, config && config.defaultStyle || {});
  var fontSize = parseInt(defaultStyle.fontSize, 10) - 1;
  var externalStyle = assign({}, defaultStyle, {
    fontSize
  }, config && config.externalStyle || {});
  var textUtil = new Text({
    style: defaultStyle
  });
  this.getExternalLabelBounds = function(bounds, text) {
    var layoutedDimensions = textUtil.getDimensions(text, {
      box: {
        width: 90,
        height: 30
      },
      style: externalStyle
    });
    return {
      x: Math.round(bounds.x + bounds.width / 2 - layoutedDimensions.width / 2),
      y: Math.round(bounds.y),
      width: Math.ceil(layoutedDimensions.width),
      height: Math.ceil(layoutedDimensions.height)
    };
  };
  this.getTextAnnotationBounds = function(bounds, text) {
    var layoutedDimensions = textUtil.getDimensions(text, {
      box: bounds,
      style: defaultStyle,
      align: "left-top",
      padding: 5
    });
    return {
      x: bounds.x,
      y: bounds.y,
      width: bounds.width,
      height: Math.max(MIN_TEXT_ANNOTATION_HEIGHT, Math.round(layoutedDimensions.height))
    };
  };
  this.createText = function(text, options) {
    return textUtil.createText(text, options || {});
  };
  this.getDefaultStyle = function() {
    return defaultStyle;
  };
  this.getExternalStyle = function() {
    return externalStyle;
  };
}
TextRenderer.$inject = [
  "config.textRenderer"
];

// node_modules/bpmn-js/lib/draw/PathMap.js
function PathMap() {
  this.pathMap = {
    "EVENT_MESSAGE": {
      d: "m {mx},{my} l 0,{e.y1} l {e.x1},0 l 0,-{e.y1} z l {e.x0},{e.y0} l {e.x0},-{e.y0}",
      height: 36,
      width: 36,
      heightElements: [6, 14],
      widthElements: [10.5, 21]
    },
    "EVENT_SIGNAL": {
      d: "M {mx},{my} l {e.x0},{e.y0} l -{e.x1},0 Z",
      height: 36,
      width: 36,
      heightElements: [18],
      widthElements: [10, 20]
    },
    "EVENT_ESCALATION": {
      d: "M {mx},{my} l {e.x0},{e.y0} l -{e.x0},-{e.y1} l -{e.x0},{e.y1} Z",
      height: 36,
      width: 36,
      heightElements: [20, 7],
      widthElements: [8]
    },
    "EVENT_CONDITIONAL": {
      d: "M {e.x0},{e.y0} l {e.x1},0 l 0,{e.y2} l -{e.x1},0 Z M {e.x2},{e.y3} l {e.x0},0 M {e.x2},{e.y4} l {e.x0},0 M {e.x2},{e.y5} l {e.x0},0 M {e.x2},{e.y6} l {e.x0},0 M {e.x2},{e.y7} l {e.x0},0 M {e.x2},{e.y8} l {e.x0},0 ",
      height: 36,
      width: 36,
      heightElements: [8.5, 14.5, 18, 11.5, 14.5, 17.5, 20.5, 23.5, 26.5],
      widthElements: [10.5, 14.5, 12.5]
    },
    "EVENT_LINK": {
      d: "m {mx},{my} 0,{e.y0} -{e.x1},0 0,{e.y1} {e.x1},0 0,{e.y0} {e.x0},-{e.y2} -{e.x0},-{e.y2} z",
      height: 36,
      width: 36,
      heightElements: [4.4375, 6.75, 7.8125],
      widthElements: [9.84375, 13.5]
    },
    "EVENT_ERROR": {
      d: "m {mx},{my} {e.x0},-{e.y0} {e.x1},-{e.y1} {e.x2},{e.y2} {e.x3},-{e.y3} -{e.x4},{e.y4} -{e.x5},-{e.y5} z",
      height: 36,
      width: 36,
      heightElements: [0.023, 8.737, 8.151, 16.564, 10.591, 8.714],
      widthElements: [0.085, 6.672, 6.97, 4.273, 5.337, 6.636]
    },
    "EVENT_CANCEL_45": {
      d: "m {mx},{my} -{e.x1},0 0,{e.x0} {e.x1},0 0,{e.y1} {e.x0},0 0,-{e.y1} {e.x1},0 0,-{e.y0} -{e.x1},0 0,-{e.y1} -{e.x0},0 z",
      height: 36,
      width: 36,
      heightElements: [4.75, 8.5],
      widthElements: [4.75, 8.5]
    },
    "EVENT_COMPENSATION": {
      d: "m {mx},{my} {e.x0},-{e.y0} 0,{e.y1} z m {e.x1},-{e.y2} {e.x2},-{e.y3} 0,{e.y1} -{e.x2},-{e.y3} z",
      height: 36,
      width: 36,
      heightElements: [6.5, 13, 0.4, 6.1],
      widthElements: [9, 9.3, 8.7]
    },
    "EVENT_TIMER_WH": {
      d: "M {mx},{my} l {e.x0},-{e.y0} m -{e.x0},{e.y0} l {e.x1},{e.y1} ",
      height: 36,
      width: 36,
      heightElements: [10, 2],
      widthElements: [3, 7]
    },
    "EVENT_TIMER_LINE": {
      d: "M {mx},{my} m {e.x0},{e.y0} l -{e.x1},{e.y1} ",
      height: 36,
      width: 36,
      heightElements: [10, 3],
      widthElements: [0, 0]
    },
    "EVENT_MULTIPLE": {
      d: "m {mx},{my} {e.x1},-{e.y0} {e.x1},{e.y0} -{e.x0},{e.y1} -{e.x2},0 z",
      height: 36,
      width: 36,
      heightElements: [6.28099, 12.56199],
      widthElements: [3.1405, 9.42149, 12.56198]
    },
    "EVENT_PARALLEL_MULTIPLE": {
      d: "m {mx},{my} {e.x0},0 0,{e.y1} {e.x1},0 0,{e.y0} -{e.x1},0 0,{e.y1} -{e.x0},0 0,-{e.y1} -{e.x1},0 0,-{e.y0} {e.x1},0 z",
      height: 36,
      width: 36,
      heightElements: [2.56228, 7.68683],
      widthElements: [2.56228, 7.68683]
    },
    "GATEWAY_EXCLUSIVE": {
      d: "m {mx},{my} {e.x0},{e.y0} {e.x1},{e.y0} {e.x2},0 {e.x4},{e.y2} {e.x4},{e.y1} {e.x2},0 {e.x1},{e.y3} {e.x0},{e.y3} {e.x3},0 {e.x5},{e.y1} {e.x5},{e.y2} {e.x3},0 z",
      height: 17.5,
      width: 17.5,
      heightElements: [8.5, 6.5312, -6.5312, -8.5],
      widthElements: [6.5, -6.5, 3, -3, 5, -5]
    },
    "GATEWAY_PARALLEL": {
      d: "m {mx},{my} 0,{e.y1} -{e.x1},0 0,{e.y0} {e.x1},0 0,{e.y1} {e.x0},0 0,-{e.y1} {e.x1},0 0,-{e.y0} -{e.x1},0 0,-{e.y1} -{e.x0},0 z",
      height: 30,
      width: 30,
      heightElements: [5, 12.5],
      widthElements: [5, 12.5]
    },
    "GATEWAY_EVENT_BASED": {
      d: "m {mx},{my} {e.x0},{e.y0} {e.x0},{e.y1} {e.x1},{e.y2} {e.x2},0 z",
      height: 11,
      width: 11,
      heightElements: [-6, 6, 12, -12],
      widthElements: [9, -3, -12]
    },
    "GATEWAY_COMPLEX": {
      d: "m {mx},{my} 0,{e.y0} -{e.x0},-{e.y1} -{e.x1},{e.y2} {e.x0},{e.y1} -{e.x2},0 0,{e.y3} {e.x2},0  -{e.x0},{e.y1} l {e.x1},{e.y2} {e.x0},-{e.y1} 0,{e.y0} {e.x3},0 0,-{e.y0} {e.x0},{e.y1} {e.x1},-{e.y2} -{e.x0},-{e.y1} {e.x2},0 0,-{e.y3} -{e.x2},0 {e.x0},-{e.y1} -{e.x1},-{e.y2} -{e.x0},{e.y1} 0,-{e.y0} -{e.x3},0 z",
      height: 17.125,
      width: 17.125,
      heightElements: [4.875, 3.4375, 2.125, 3],
      widthElements: [3.4375, 2.125, 4.875, 3]
    },
    "DATA_OBJECT_PATH": {
      d: "m 0,0 {e.x1},0 {e.x0},{e.y0} 0,{e.y1} -{e.x2},0 0,-{e.y2} {e.x1},0 0,{e.y0} {e.x0},0",
      height: 61,
      width: 51,
      heightElements: [10, 50, 60],
      widthElements: [10, 40, 50, 60]
    },
    "DATA_OBJECT_COLLECTION_PATH": {
      d: "m{mx},{my} m 3,2 l 0,10 m 3,-10 l 0,10 m 3,-10 l 0,10",
      height: 10,
      width: 10,
      heightElements: [],
      widthElements: []
    },
    "DATA_ARROW": {
      d: "m 5,9 9,0 0,-3 5,5 -5,5 0,-3 -9,0 z",
      height: 61,
      width: 51,
      heightElements: [],
      widthElements: []
    },
    "DATA_STORE": {
      d: "m  {mx},{my} l  0,{e.y2} c  {e.x0},{e.y1} {e.x1},{e.y1}  {e.x2},0 l  0,-{e.y2} c -{e.x0},-{e.y1} -{e.x1},-{e.y1} -{e.x2},0c  {e.x0},{e.y1} {e.x1},{e.y1}  {e.x2},0 m  -{e.x2},{e.y0}c  {e.x0},{e.y1} {e.x1},{e.y1} {e.x2},0m  -{e.x2},{e.y0}c  {e.x0},{e.y1} {e.x1},{e.y1}  {e.x2},0",
      height: 61,
      width: 61,
      heightElements: [7, 10, 45],
      widthElements: [2, 58, 60]
    },
    "TEXT_ANNOTATION": {
      d: "m {mx}, {my} m 10,0 l -10,0 l 0,{e.y0} l 10,0",
      height: 30,
      width: 10,
      heightElements: [30],
      widthElements: [10]
    },
    "MARKER_SUB_PROCESS": {
      d: "m{mx},{my} m 7,2 l 0,10 m -5,-5 l 10,0",
      height: 10,
      width: 10,
      heightElements: [],
      widthElements: []
    },
    "MARKER_PARALLEL": {
      d: "m{mx},{my} m 3,2 l 0,10 m 3,-10 l 0,10 m 3,-10 l 0,10",
      height: 10,
      width: 10,
      heightElements: [],
      widthElements: []
    },
    "MARKER_SEQUENTIAL": {
      d: "m{mx},{my} m 0,3 l 10,0 m -10,3 l 10,0 m -10,3 l 10,0",
      height: 10,
      width: 10,
      heightElements: [],
      widthElements: []
    },
    "MARKER_COMPENSATION": {
      d: "m {mx},{my} 7,-5 0,10 z m 7.1,-0.3 6.9,-4.7 0,10 -6.9,-4.7 z",
      height: 10,
      width: 21,
      heightElements: [],
      widthElements: []
    },
    "MARKER_LOOP": {
      d: "m {mx},{my} c 3.526979,0 6.386161,-2.829858 6.386161,-6.320661 0,-3.490806 -2.859182,-6.320661 -6.386161,-6.320661 -3.526978,0 -6.38616,2.829855 -6.38616,6.320661 0,1.745402 0.714797,3.325567 1.870463,4.469381 0.577834,0.571908 1.265885,1.034728 2.029916,1.35457 l -0.718163,-3.909793 m 0.718163,3.909793 -3.885211,0.802902",
      height: 13.9,
      width: 13.7,
      heightElements: [],
      widthElements: []
    },
    "MARKER_ADHOC": {
      d: "m {mx},{my} m 0.84461,2.64411 c 1.05533,-1.23780996 2.64337,-2.07882 4.29653,-1.97997996 2.05163,0.0805 3.85579,1.15803 5.76082,1.79107 1.06385,0.34139996 2.24454,0.1438 3.18759,-0.43767 0.61743,-0.33642 1.2775,-0.64078 1.7542,-1.17511 0,0.56023 0,1.12046 0,1.6807 -0.98706,0.96237996 -2.29792,1.62393996 -3.6918,1.66181996 -1.24459,0.0927 -2.46671,-0.2491 -3.59505,-0.74812 -1.35789,-0.55965 -2.75133,-1.33436996 -4.27027,-1.18121996 -1.37741,0.14601 -2.41842,1.13685996 -3.44288,1.96782996 z",
      height: 4,
      width: 15,
      heightElements: [],
      widthElements: []
    },
    "TASK_TYPE_SEND": {
      d: "m {mx},{my} l 0,{e.y1} l {e.x1},0 l 0,-{e.y1} z l {e.x0},{e.y0} l {e.x0},-{e.y0}",
      height: 14,
      width: 21,
      heightElements: [6, 14],
      widthElements: [10.5, 21]
    },
    "TASK_TYPE_SCRIPT": {
      d: "m {mx},{my} c 9.966553,-6.27276 -8.000926,-7.91932 2.968968,-14.938 l -8.802728,0 c -10.969894,7.01868 6.997585,8.66524 -2.968967,14.938 z m -7,-12 l 5,0 m -4.5,3 l 4.5,0 m -3,3 l 5,0m -4,3 l 5,0",
      height: 15,
      width: 12.6,
      heightElements: [6, 14],
      widthElements: [10.5, 21]
    },
    "TASK_TYPE_USER_1": {
      d: "m {mx},{my} c 0.909,-0.845 1.594,-2.049 1.594,-3.385 0,-2.554 -1.805,-4.62199999 -4.357,-4.62199999 -2.55199998,0 -4.28799998,2.06799999 -4.28799998,4.62199999 0,1.348 0.974,2.562 1.89599998,3.405 -0.52899998,0.187 -5.669,2.097 -5.794,4.7560005 v 6.718 h 17 v -6.718 c 0,-2.2980005 -5.5279996,-4.5950005 -6.0509996,-4.7760005 zm -8,6 l 0,5.5 m 11,0 l 0,-5"
    },
    "TASK_TYPE_USER_2": {
      d: "m {mx},{my} m 2.162,1.009 c 0,2.4470005 -2.158,4.4310005 -4.821,4.4310005 -2.66499998,0 -4.822,-1.981 -4.822,-4.4310005 "
    },
    "TASK_TYPE_USER_3": {
      d: "m {mx},{my} m -6.9,-3.80 c 0,0 2.25099998,-2.358 4.27399998,-1.177 2.024,1.181 4.221,1.537 4.124,0.965 -0.098,-0.57 -0.117,-3.79099999 -4.191,-4.13599999 -3.57499998,0.001 -4.20799998,3.36699999 -4.20699998,4.34799999 z"
    },
    "TASK_TYPE_MANUAL": {
      d: "m {mx},{my} c 0.234,-0.01 5.604,0.008 8.029,0.004 0.808,0 1.271,-0.172 1.417,-0.752 0.227,-0.898 -0.334,-1.314 -1.338,-1.316 -2.467,-0.01 -7.886,-0.004 -8.108,-0.004 -0.014,-0.079 0.016,-0.533 0,-0.61 0.195,-0.042 8.507,0.006 9.616,0.002 0.877,-0.007 1.35,-0.438 1.353,-1.208 0.003,-0.768 -0.479,-1.09 -1.35,-1.091 -2.968,-0.002 -9.619,-0.013 -9.619,-0.013 v -0.591 c 0,0 5.052,-0.016 7.225,-0.016 0.888,-0.002 1.354,-0.416 1.351,-1.193 -0.006,-0.761 -0.492,-1.196 -1.361,-1.196 -3.473,-0.005 -10.86,-0.003 -11.0829995,-0.003 -0.022,-0.047 -0.045,-0.094 -0.069,-0.139 0.3939995,-0.319 2.0409995,-1.626 2.4149995,-2.017 0.469,-0.4870005 0.519,-1.1650005 0.162,-1.6040005 -0.414,-0.511 -0.973,-0.5 -1.48,-0.236 -1.4609995,0.764 -6.5999995,3.6430005 -7.7329995,4.2710005 -0.9,0.499 -1.516,1.253 -1.882,2.19 -0.37000002,0.95 -0.17,2.01 -0.166,2.979 0.004,0.718 -0.27300002,1.345 -0.055,2.063 0.629,2.087 2.425,3.312 4.859,3.318 4.6179995,0.014 9.2379995,-0.139 13.8569995,-0.158 0.755,-0.004 1.171,-0.301 1.182,-1.033 0.012,-0.754 -0.423,-0.969 -1.183,-0.973 -1.778,-0.01 -5.824,-0.004 -6.04,-0.004 10e-4,-0.084 0.003,-0.586 10e-4,-0.67 z"
    },
    "TASK_TYPE_INSTANTIATING_SEND": {
      d: "m {mx},{my} l 0,8.4 l 12.6,0 l 0,-8.4 z l 6.3,3.6 l 6.3,-3.6"
    },
    "TASK_TYPE_SERVICE": {
      d: "m {mx},{my} v -1.71335 c 0.352326,-0.0705 0.703932,-0.17838 1.047628,-0.32133 0.344416,-0.14465 0.665822,-0.32133 0.966377,-0.52145 l 1.19431,1.18005 1.567487,-1.57688 -1.195028,-1.18014 c 0.403376,-0.61394 0.683079,-1.29908 0.825447,-2.01824 l 1.622133,-0.01 v -2.2196 l -1.636514,0.01 c -0.07333,-0.35153 -0.178319,-0.70024 -0.323564,-1.04372 -0.145244,-0.34406 -0.321407,-0.6644 -0.522735,-0.96217 l 1.131035,-1.13631 -1.583305,-1.56293 -1.129598,1.13589 c -0.614052,-0.40108 -1.302883,-0.68093 -2.022633,-0.82247 l 0.0093,-1.61852 h -2.241173 l 0.0042,1.63124 c -0.353763,0.0736 -0.705369,0.17977 -1.049785,0.32371 -0.344415,0.14437 -0.665102,0.32092 -0.9635006,0.52046 l -1.1698628,-1.15823 -1.5667691,1.5792 1.1684265,1.15669 c -0.4026573,0.61283 -0.68308,1.29797 -0.8247287,2.01713 l -1.6588041,0.003 v 2.22174 l 1.6724648,-0.006 c 0.073327,0.35077 0.1797598,0.70243 0.3242851,1.04472 0.1452428,0.34448 0.3214064,0.6644 0.5227339,0.96066 l -1.1993431,1.19723 1.5840256,1.56011 1.1964668,-1.19348 c 0.6140517,0.40346 1.3028827,0.68232 2.0233517,0.82331 l 7.19e-4,1.69892 h 2.226848 z m 0.221462,-3.9957 c -1.788948,0.7502 -3.8576,-0.0928 -4.6097055,-1.87438 -0.7521065,-1.78321 0.090598,-3.84627 1.8802645,-4.59604 1.78823,-0.74936 3.856881,0.0929 4.608987,1.87437 0.752106,1.78165 -0.0906,3.84612 -1.879546,4.59605 z"
    },
    "TASK_TYPE_SERVICE_FILL": {
      d: "m {mx},{my} c -1.788948,0.7502 -3.8576,-0.0928 -4.6097055,-1.87438 -0.7521065,-1.78321 0.090598,-3.84627 1.8802645,-4.59604 1.78823,-0.74936 3.856881,0.0929 4.608987,1.87437 0.752106,1.78165 -0.0906,3.84612 -1.879546,4.59605 z"
    },
    "TASK_TYPE_BUSINESS_RULE_HEADER": {
      d: "m {mx},{my} 0,4 20,0 0,-4 z"
    },
    "TASK_TYPE_BUSINESS_RULE_MAIN": {
      d: "m {mx},{my} 0,12 20,0 0,-12 zm 0,8 l 20,0 m -13,-4 l 0,8"
    },
    "MESSAGE_FLOW_MARKER": {
      d: "m {mx},{my} m -10.5 ,-7 l 0,14 l 21,0 l 0,-14 z l 10.5,6 l 10.5,-6"
    }
  };
  this.getRawPath = function getRawPath(pathId) {
    return this.pathMap[pathId].d;
  };
  this.getScaledPath = function getScaledPath(pathId, param) {
    var rawPath = this.pathMap[pathId];
    var mx, my;
    if (param.abspos) {
      mx = param.abspos.x;
      my = param.abspos.y;
    } else {
      mx = param.containerWidth * param.position.mx;
      my = param.containerHeight * param.position.my;
    }
    var coordinates = {};
    if (param.position) {
      var heightRatio = param.containerHeight / rawPath.height * param.yScaleFactor;
      var widthRatio = param.containerWidth / rawPath.width * param.xScaleFactor;
      for (var heightIndex = 0; heightIndex < rawPath.heightElements.length; heightIndex++) {
        coordinates["y" + heightIndex] = rawPath.heightElements[heightIndex] * heightRatio;
      }
      for (var widthIndex = 0; widthIndex < rawPath.widthElements.length; widthIndex++) {
        coordinates["x" + widthIndex] = rawPath.widthElements[widthIndex] * widthRatio;
      }
    }
    var path = format(
      rawPath.d,
      {
        mx,
        my,
        e: coordinates
      }
    );
    return path;
  };
}
var tokenRegex = /\{([^{}]+)\}/g;
var objNotationRegex = /(?:(?:^|\.)(.+?)(?=\[|\.|$|\()|\[('|")(.+?)\2\])(\(\))?/g;
function replacer(all2, key, obj) {
  var res = obj;
  key.replace(objNotationRegex, function(all3, name2, quote, quotedName, isFunc) {
    name2 = name2 || quotedName;
    if (res) {
      if (name2 in res) {
        res = res[name2];
      }
      typeof res == "function" && isFunc && (res = res());
    }
  });
  res = (res == null || res == obj ? all2 : res) + "";
  return res;
}
function format(str, obj) {
  return String(str).replace(tokenRegex, function(all2, key) {
    return replacer(all2, key, obj);
  });
}

// node_modules/bpmn-js/lib/draw/index.js
var draw_default = {
  __init__: ["bpmnRenderer"],
  bpmnRenderer: ["type", BpmnRenderer],
  textRenderer: ["type", TextRenderer],
  pathMap: ["type", PathMap]
};

// node_modules/diagram-js/lib/i18n/translate/translate.js
function translate2(template, replacements) {
  replacements = replacements || {};
  return template.replace(/{([^}]+)}/g, function(_, key) {
    return replacements[key] || "{" + key + "}";
  });
}

// node_modules/diagram-js/lib/i18n/translate/index.js
var translate_default = {
  translate: ["value", translate2]
};

// node_modules/path-intersection/intersect.js
var math = Math;
var PI = math.PI;
var mmin = math.min;
var mmax = math.max;
var pow = math.pow;
var abs = math.abs;
var isArray3 = Array.isArray || function(o) {
  return o instanceof Array;
};

// node_modules/diagram-js/lib/layout/LayoutUtil.js
function roundPoint(point) {
  return {
    x: Math.round(point.x),
    y: Math.round(point.y)
  };
}
function asTRBL(bounds) {
  return {
    top: bounds.y,
    right: bounds.x + (bounds.width || 0),
    bottom: bounds.y + (bounds.height || 0),
    left: bounds.x
  };
}
function asBounds(trbl) {
  return {
    x: trbl.left,
    y: trbl.top,
    width: trbl.right - trbl.left,
    height: trbl.bottom - trbl.top
  };
}
function getBoundsMid(bounds) {
  return roundPoint({
    x: bounds.x + (bounds.width || 0) / 2,
    y: bounds.y + (bounds.height || 0) / 2
  });
}
function getConnectionMid(connection) {
  var waypoints = connection.waypoints;
  var parts = waypoints.reduce(function(parts2, point, index) {
    var lastPoint = waypoints[index - 1];
    if (lastPoint) {
      var lastPart = parts2[parts2.length - 1];
      var startLength = lastPart && lastPart.endLength || 0;
      var length = distance(lastPoint, point);
      parts2.push({
        start: lastPoint,
        end: point,
        startLength,
        endLength: startLength + length,
        length
      });
    }
    return parts2;
  }, []);
  var totalLength = parts.reduce(function(length, part) {
    return length + part.length;
  }, 0);
  var midLength = totalLength / 2;
  var i = 0;
  var midSegment = parts[i];
  while (midSegment.endLength < midLength) {
    midSegment = parts[++i];
  }
  var segmentProgress = (midLength - midSegment.startLength) / midSegment.length;
  var midPoint = {
    x: midSegment.start.x + (midSegment.end.x - midSegment.start.x) * segmentProgress,
    y: midSegment.start.y + (midSegment.end.y - midSegment.start.y) * segmentProgress
  };
  return midPoint;
}
function getMid(element) {
  if (isConnection(element)) {
    return getConnectionMid(element);
  }
  return getBoundsMid(element);
}
function distance(a, b) {
  return Math.sqrt(Math.pow(a.x - b.x, 2) + Math.pow(a.y - b.y, 2));
}

// node_modules/bpmn-js/lib/import/Util.js
function elementToString(e2) {
  if (!e2) {
    return "<null>";
  }
  return "<" + e2.$type + (e2.id ? ' id="' + e2.id : "") + '" />';
}

// node_modules/bpmn-js/lib/import/BpmnImporter.js
function elementData(semantic, di, attrs) {
  return assign({
    id: semantic.id,
    type: semantic.$type,
    businessObject: semantic,
    di
  }, attrs);
}
function getWaypoints(di, source, target) {
  var waypoints = di.waypoint;
  if (!waypoints || waypoints.length < 2) {
    return [getMid(source), getMid(target)];
  }
  return waypoints.map(function(p) {
    return { x: p.x, y: p.y };
  });
}
function notYetDrawn(semantic, refSemantic, property) {
  return new Error(
    `element ${elementToString(refSemantic)} referenced by ${elementToString(semantic)}#${property} not yet drawn`
  );
}
function BpmnImporter(eventBus, canvas, elementFactory, elementRegistry, textRenderer) {
  this._eventBus = eventBus;
  this._canvas = canvas;
  this._elementFactory = elementFactory;
  this._elementRegistry = elementRegistry;
  this._textRenderer = textRenderer;
}
BpmnImporter.$inject = [
  "eventBus",
  "canvas",
  "elementFactory",
  "elementRegistry",
  "textRenderer"
];
BpmnImporter.prototype.add = function(semantic, di, parentElement) {
  var element, hidden;
  var parentIndex;
  if (is(di, "bpmndi:BPMNPlane")) {
    var attrs = is(semantic, "bpmn:SubProcess") ? { id: semantic.id + "_plane" } : {};
    element = this._elementFactory.createRoot(elementData(semantic, di, attrs));
    this._canvas.addRootElement(element);
  } else if (is(di, "bpmndi:BPMNShape")) {
    var collapsed = !isExpanded(semantic, di), isFrame = isFrameElement(semantic);
    hidden = parentElement && (parentElement.hidden || parentElement.collapsed);
    var bounds = di.bounds;
    element = this._elementFactory.createShape(elementData(semantic, di, {
      collapsed,
      hidden,
      x: Math.round(bounds.x),
      y: Math.round(bounds.y),
      width: Math.round(bounds.width),
      height: Math.round(bounds.height),
      isFrame
    }));
    if (is(semantic, "bpmn:BoundaryEvent")) {
      this._attachBoundary(semantic, element);
    }
    if (is(semantic, "bpmn:Lane")) {
      parentIndex = 0;
    }
    if (is(semantic, "bpmn:DataStoreReference")) {
      if (!isPointInsideBBox(parentElement, getMid(bounds))) {
        parentElement = this._canvas.findRoot(parentElement);
      }
    }
    this._canvas.addShape(element, parentElement, parentIndex);
  } else if (is(di, "bpmndi:BPMNEdge")) {
    var source = this._getSource(semantic), target = this._getTarget(semantic);
    hidden = parentElement && (parentElement.hidden || parentElement.collapsed);
    element = this._elementFactory.createConnection(elementData(semantic, di, {
      hidden,
      source,
      target,
      waypoints: getWaypoints(di, source, target)
    }));
    if (is(semantic, "bpmn:DataAssociation")) {
      parentElement = this._canvas.findRoot(parentElement);
    }
    this._canvas.addConnection(element, parentElement, parentIndex);
  } else {
    throw new Error(
      `unknown di ${elementToString(di)} for element ${elementToString(semantic)}`
    );
  }
  if (isLabelExternal(semantic) && getLabel(element)) {
    this.addLabel(semantic, di, element);
  }
  this._eventBus.fire("bpmnElement.added", { element });
  return element;
};
BpmnImporter.prototype._attachBoundary = function(boundarySemantic, boundaryElement) {
  var hostSemantic = boundarySemantic.attachedToRef;
  if (!hostSemantic) {
    throw new Error(
      `missing ${elementToString(boundarySemantic)}#attachedToRef`
    );
  }
  var host = this._elementRegistry.get(hostSemantic.id), attachers = host && host.attachers;
  if (!host) {
    throw notYetDrawn(boundarySemantic, hostSemantic, "attachedToRef");
  }
  boundaryElement.host = host;
  if (!attachers) {
    host.attachers = attachers = [];
  }
  if (attachers.indexOf(boundaryElement) === -1) {
    attachers.push(boundaryElement);
  }
};
BpmnImporter.prototype.addLabel = function(semantic, di, element) {
  var bounds, text, label;
  bounds = getExternalLabelBounds(di, element);
  text = getLabel(element);
  if (text) {
    bounds = this._textRenderer.getExternalLabelBounds(bounds, text);
  }
  label = this._elementFactory.createLabel(elementData(semantic, di, {
    id: semantic.id + "_label",
    labelTarget: element,
    type: "label",
    hidden: element.hidden || !getLabel(element),
    x: Math.round(bounds.x),
    y: Math.round(bounds.y),
    width: Math.round(bounds.width),
    height: Math.round(bounds.height)
  }));
  return this._canvas.addShape(label, element.parent);
};
BpmnImporter.prototype._getConnectedElement = function(semantic, side) {
  var element, refSemantic, type = semantic.$type;
  refSemantic = semantic[side + "Ref"];
  if (side === "source" && type === "bpmn:DataInputAssociation") {
    refSemantic = refSemantic && refSemantic[0];
  }
  if (side === "source" && type === "bpmn:DataOutputAssociation" || side === "target" && type === "bpmn:DataInputAssociation") {
    refSemantic = semantic.$parent;
  }
  element = refSemantic && this._getElement(refSemantic);
  if (element) {
    return element;
  }
  if (refSemantic) {
    throw notYetDrawn(semantic, refSemantic, side + "Ref");
  } else {
    throw new Error(
      `${elementToString(semantic)}#${side} Ref not specified`
    );
  }
};
BpmnImporter.prototype._getSource = function(semantic) {
  return this._getConnectedElement(semantic, "source");
};
BpmnImporter.prototype._getTarget = function(semantic) {
  return this._getConnectedElement(semantic, "target");
};
BpmnImporter.prototype._getElement = function(semantic) {
  return this._elementRegistry.get(semantic.id);
};
function isPointInsideBBox(bbox, point) {
  var x = point.x, y = point.y;
  return x >= bbox.x && x <= bbox.x + bbox.width && y >= bbox.y && y <= bbox.y + bbox.height;
}
function isFrameElement(semantic) {
  return is(semantic, "bpmn:Group");
}

// node_modules/bpmn-js/lib/import/index.js
var import_default = {
  __depends__: [
    translate_default
  ],
  bpmnImporter: ["type", BpmnImporter]
};

// node_modules/bpmn-js/lib/core/index.js
var core_default = {
  __depends__: [
    draw_default,
    import_default
  ]
};

// node_modules/diagram-js/lib/util/Elements.js
function getBBox(elements, stopRecursion) {
  stopRecursion = !!stopRecursion;
  if (!isArray(elements)) {
    elements = [elements];
  }
  var minX, minY, maxX, maxY;
  forEach(elements, function(element) {
    var bbox = element;
    if (element.waypoints && !stopRecursion) {
      bbox = getBBox(element.waypoints, true);
    }
    var x = bbox.x, y = bbox.y, height = bbox.height || 0, width = bbox.width || 0;
    if (x < minX || minX === void 0) {
      minX = x;
    }
    if (y < minY || minY === void 0) {
      minY = y;
    }
    if (x + width > maxX || maxX === void 0) {
      maxX = x + width;
    }
    if (y + height > maxY || maxY === void 0) {
      maxY = y + height;
    }
  });
  return {
    x: minX,
    y: minY,
    height: maxY - minY,
    width: maxX - minX
  };
}
function getType(element) {
  if ("waypoints" in element) {
    return "connection";
  }
  if ("x" in element) {
    return "shape";
  }
  return "root";
}
function isFrameElement2(element) {
  return !!(element && element.isFrame);
}

// node_modules/diagram-js/lib/util/IdGenerator.js
function IdGenerator(prefix3) {
  this._counter = 0;
  this._prefix = (prefix3 ? prefix3 + "-" : "") + Math.floor(Math.random() * 1e9) + "-";
}
IdGenerator.prototype.next = function() {
  return this._prefix + ++this._counter;
};

// node_modules/diagram-js/lib/features/overlays/Overlays.js
var ids = new IdGenerator("ov");
var LOW_PRIORITY = 500;
function Overlays(config, eventBus, canvas, elementRegistry) {
  this._eventBus = eventBus;
  this._canvas = canvas;
  this._elementRegistry = elementRegistry;
  this._ids = ids;
  this._overlayDefaults = assign({
    // no show constraints
    show: null,
    // always scale
    scale: true
  }, config && config.defaults);
  this._overlays = {};
  this._overlayContainers = [];
  this._overlayRoot = createRoot(canvas.getContainer());
  this._init();
}
Overlays.$inject = [
  "config.overlays",
  "eventBus",
  "canvas",
  "elementRegistry"
];
Overlays.prototype.get = function(search) {
  if (isString(search)) {
    search = { id: search };
  }
  if (isString(search.element)) {
    search.element = this._elementRegistry.get(search.element);
  }
  if (search.element) {
    var container = this._getOverlayContainer(search.element, true);
    if (container) {
      return search.type ? filter(container.overlays, matchPattern({ type: search.type })) : container.overlays.slice();
    } else {
      return [];
    }
  } else if (search.type) {
    return filter(this._overlays, matchPattern({ type: search.type }));
  } else {
    return search.id ? this._overlays[search.id] : null;
  }
};
Overlays.prototype.add = function(element, type, overlay) {
  if (isObject(type)) {
    overlay = type;
    type = null;
  }
  if (!element.id) {
    element = this._elementRegistry.get(element);
  }
  if (!overlay.position) {
    throw new Error("must specifiy overlay position");
  }
  if (!overlay.html) {
    throw new Error("must specifiy overlay html");
  }
  if (!element) {
    throw new Error("invalid element specified");
  }
  var id = this._ids.next();
  overlay = assign({}, this._overlayDefaults, overlay, {
    id,
    type,
    element,
    html: overlay.html
  });
  this._addOverlay(overlay);
  return id;
};
Overlays.prototype.remove = function(filter2) {
  var overlays = this.get(filter2) || [];
  if (!isArray(overlays)) {
    overlays = [overlays];
  }
  var self = this;
  forEach(overlays, function(overlay) {
    var container = self._getOverlayContainer(overlay.element, true);
    if (overlay) {
      remove2(overlay.html);
      remove2(overlay.htmlContainer);
      delete overlay.htmlContainer;
      delete overlay.element;
      delete self._overlays[overlay.id];
    }
    if (container) {
      var idx = container.overlays.indexOf(overlay);
      if (idx !== -1) {
        container.overlays.splice(idx, 1);
      }
    }
  });
};
Overlays.prototype.isShown = function() {
  return this._overlayRoot.style.display !== "none";
};
Overlays.prototype.show = function() {
  setVisible(this._overlayRoot);
};
Overlays.prototype.hide = function() {
  setVisible(this._overlayRoot, false);
};
Overlays.prototype.clear = function() {
  this._overlays = {};
  this._overlayContainers = [];
  clear2(this._overlayRoot);
};
Overlays.prototype._updateOverlayContainer = function(container) {
  var element = container.element, html = container.html;
  var x = element.x, y = element.y;
  if (element.waypoints) {
    var bbox = getBBox(element);
    x = bbox.x;
    y = bbox.y;
  }
  setPosition(html, x, y);
  attr2(container.html, "data-container-id", element.id);
};
Overlays.prototype._updateOverlay = function(overlay) {
  var position = overlay.position, htmlContainer = overlay.htmlContainer, element = overlay.element;
  var left = position.left, top = position.top;
  if (position.right !== void 0) {
    var width;
    if (element.waypoints) {
      width = getBBox(element).width;
    } else {
      width = element.width;
    }
    left = position.right * -1 + width;
  }
  if (position.bottom !== void 0) {
    var height;
    if (element.waypoints) {
      height = getBBox(element).height;
    } else {
      height = element.height;
    }
    top = position.bottom * -1 + height;
  }
  setPosition(htmlContainer, left || 0, top || 0);
  this._updateOverlayVisibilty(overlay, this._canvas.viewbox());
};
Overlays.prototype._createOverlayContainer = function(element) {
  var html = domify$1('<div class="djs-overlays" />');
  assign2(html, { position: "absolute" });
  this._overlayRoot.appendChild(html);
  var container = {
    html,
    element,
    overlays: []
  };
  this._updateOverlayContainer(container);
  this._overlayContainers.push(container);
  return container;
};
Overlays.prototype._updateRoot = function(viewbox) {
  var scale = viewbox.scale || 1;
  var matrix = "matrix(" + [
    scale,
    0,
    0,
    scale,
    -1 * viewbox.x * scale,
    -1 * viewbox.y * scale
  ].join(",") + ")";
  setTransform(this._overlayRoot, matrix);
};
Overlays.prototype._getOverlayContainer = function(element, raw) {
  var container = find(this._overlayContainers, function(c) {
    return c.element === element;
  });
  if (!container && !raw) {
    return this._createOverlayContainer(element);
  }
  return container;
};
Overlays.prototype._addOverlay = function(overlay) {
  var id = overlay.id, element = overlay.element, html = overlay.html, htmlContainer, overlayContainer;
  if (html.get && html.constructor.prototype.jquery) {
    html = html.get(0);
  }
  if (isString(html)) {
    html = domify$1(html);
  }
  overlayContainer = this._getOverlayContainer(element);
  htmlContainer = domify$1('<div class="djs-overlay" data-overlay-id="' + id + '">');
  assign2(htmlContainer, { position: "absolute" });
  htmlContainer.appendChild(html);
  if (overlay.type) {
    classes2(htmlContainer).add("djs-overlay-" + overlay.type);
  }
  var elementRoot = this._canvas.findRoot(element);
  var activeRoot = this._canvas.getRootElement();
  setVisible(htmlContainer, elementRoot === activeRoot);
  overlay.htmlContainer = htmlContainer;
  overlayContainer.overlays.push(overlay);
  overlayContainer.html.appendChild(htmlContainer);
  this._overlays[id] = overlay;
  this._updateOverlay(overlay);
  this._updateOverlayVisibilty(overlay, this._canvas.viewbox());
};
Overlays.prototype._updateOverlayVisibilty = function(overlay, viewbox) {
  var show = overlay.show, rootElement = this._canvas.findRoot(overlay.element), minZoom = show && show.minZoom, maxZoom = show && show.maxZoom, htmlContainer = overlay.htmlContainer, activeRootElement = this._canvas.getRootElement(), visible = true;
  if (rootElement !== activeRootElement) {
    visible = false;
  } else if (show) {
    if (isDefined(minZoom) && minZoom > viewbox.scale || isDefined(maxZoom) && maxZoom < viewbox.scale) {
      visible = false;
    }
  }
  setVisible(htmlContainer, visible);
  this._updateOverlayScale(overlay, viewbox);
};
Overlays.prototype._updateOverlayScale = function(overlay, viewbox) {
  var shouldScale = overlay.scale, minScale, maxScale, htmlContainer = overlay.htmlContainer;
  var scale, transform3 = "";
  if (shouldScale !== true) {
    if (shouldScale === false) {
      minScale = 1;
      maxScale = 1;
    } else {
      minScale = shouldScale.min;
      maxScale = shouldScale.max;
    }
    if (isDefined(minScale) && viewbox.scale < minScale) {
      scale = (1 / viewbox.scale || 1) * minScale;
    }
    if (isDefined(maxScale) && viewbox.scale > maxScale) {
      scale = (1 / viewbox.scale || 1) * maxScale;
    }
  }
  if (isDefined(scale)) {
    transform3 = "scale(" + scale + "," + scale + ")";
  }
  setTransform(htmlContainer, transform3);
};
Overlays.prototype._updateOverlaysVisibilty = function(viewbox) {
  var self = this;
  forEach(this._overlays, function(overlay) {
    self._updateOverlayVisibilty(overlay, viewbox);
  });
};
Overlays.prototype._init = function() {
  var eventBus = this._eventBus;
  var self = this;
  function updateViewbox(viewbox) {
    self._updateRoot(viewbox);
    self._updateOverlaysVisibilty(viewbox);
    self.show();
  }
  eventBus.on("canvas.viewbox.changing", function(event2) {
    self.hide();
  });
  eventBus.on("canvas.viewbox.changed", function(event2) {
    updateViewbox(event2.viewbox);
  });
  eventBus.on(["shape.remove", "connection.remove"], function(e2) {
    var element = e2.element;
    var overlays = self.get({ element });
    forEach(overlays, function(o) {
      self.remove(o.id);
    });
    var container = self._getOverlayContainer(element);
    if (container) {
      remove2(container.html);
      var i = self._overlayContainers.indexOf(container);
      if (i !== -1) {
        self._overlayContainers.splice(i, 1);
      }
    }
  });
  eventBus.on("element.changed", LOW_PRIORITY, function(e2) {
    var element = e2.element;
    var container = self._getOverlayContainer(element, true);
    if (container) {
      forEach(container.overlays, function(overlay) {
        self._updateOverlay(overlay);
      });
      self._updateOverlayContainer(container);
    }
  });
  eventBus.on("element.marker.update", function(e2) {
    var container = self._getOverlayContainer(e2.element, true);
    if (container) {
      classes2(container.html)[e2.add ? "add" : "remove"](e2.marker);
    }
  });
  eventBus.on("root.set", function() {
    self._updateOverlaysVisibilty(self._canvas.viewbox());
  });
  eventBus.on("diagram.clear", this.clear, this);
};
function createRoot(parentNode) {
  var root = domify$1(
    '<div class="djs-overlay-container" />'
  );
  assign2(root, {
    position: "absolute",
    width: 0,
    height: 0
  });
  parentNode.insertBefore(root, parentNode.firstChild);
  return root;
}
function setPosition(el, x, y) {
  assign2(el, { left: x + "px", top: y + "px" });
}
function setVisible(el, visible) {
  el.style.display = visible === false ? "none" : "";
}
function setTransform(el, transform3) {
  el.style["transform-origin"] = "top left";
  ["", "-ms-", "-webkit-"].forEach(function(prefix3) {
    el.style[prefix3 + "transform"] = transform3;
  });
}

// node_modules/diagram-js/lib/features/overlays/index.js
var overlays_default = {
  __init__: ["overlays"],
  overlays: ["type", Overlays]
};

// node_modules/diagram-js/lib/features/change-support/ChangeSupport.js
function ChangeSupport(eventBus, canvas, elementRegistry, graphicsFactory) {
  eventBus.on("element.changed", function(event2) {
    var element = event2.element;
    if (element.parent || element === canvas.getRootElement()) {
      event2.gfx = elementRegistry.getGraphics(element);
    }
    if (!event2.gfx) {
      return;
    }
    eventBus.fire(getType(element) + ".changed", event2);
  });
  eventBus.on("elements.changed", function(event2) {
    var elements = event2.elements;
    elements.forEach(function(e2) {
      eventBus.fire("element.changed", { element: e2 });
    });
    graphicsFactory.updateContainments(elements);
  });
  eventBus.on("shape.changed", function(event2) {
    graphicsFactory.update("shape", event2.element, event2.gfx);
  });
  eventBus.on("connection.changed", function(event2) {
    graphicsFactory.update("connection", event2.element, event2.gfx);
  });
}
ChangeSupport.$inject = [
  "eventBus",
  "canvas",
  "elementRegistry",
  "graphicsFactory"
];

// node_modules/diagram-js/lib/features/change-support/index.js
var change_support_default = {
  __init__: ["changeSupport"],
  changeSupport: ["type", ChangeSupport]
};

// node_modules/diagram-js/lib/command/CommandInterceptor.js
var DEFAULT_PRIORITY = 1e3;
function CommandInterceptor(eventBus) {
  this._eventBus = eventBus;
}
CommandInterceptor.$inject = ["eventBus"];
function unwrapEvent(fn, that) {
  return function(event2) {
    return fn.call(that || null, event2.context, event2.command, event2);
  };
}
CommandInterceptor.prototype.on = function(events, hook, priority, handlerFn, unwrap, that) {
  if (isFunction(hook) || isNumber(hook)) {
    that = unwrap;
    unwrap = handlerFn;
    handlerFn = priority;
    priority = hook;
    hook = null;
  }
  if (isFunction(priority)) {
    that = unwrap;
    unwrap = handlerFn;
    handlerFn = priority;
    priority = DEFAULT_PRIORITY;
  }
  if (isObject(unwrap)) {
    that = unwrap;
    unwrap = false;
  }
  if (!isFunction(handlerFn)) {
    throw new Error("handlerFn must be a function");
  }
  if (!isArray(events)) {
    events = [events];
  }
  var eventBus = this._eventBus;
  forEach(events, function(event2) {
    var fullEvent = ["commandStack", event2, hook].filter(function(e2) {
      return e2;
    }).join(".");
    eventBus.on(fullEvent, priority, unwrap ? unwrapEvent(handlerFn, that) : handlerFn, that);
  });
};
CommandInterceptor.prototype.canExecute = createHook("canExecute");
CommandInterceptor.prototype.preExecute = createHook("preExecute");
CommandInterceptor.prototype.preExecuted = createHook("preExecuted");
CommandInterceptor.prototype.execute = createHook("execute");
CommandInterceptor.prototype.executed = createHook("executed");
CommandInterceptor.prototype.postExecute = createHook("postExecute");
CommandInterceptor.prototype.postExecuted = createHook("postExecuted");
CommandInterceptor.prototype.revert = createHook("revert");
CommandInterceptor.prototype.reverted = createHook("reverted");
function createHook(hook) {
  const hookFn = function(events, priority, handlerFn, unwrap, that) {
    if (isFunction(events) || isNumber(events)) {
      that = unwrap;
      unwrap = handlerFn;
      handlerFn = priority;
      priority = events;
      events = null;
    }
    this.on(events, hook, priority, handlerFn, unwrap, that);
  };
  return hookFn;
}

// node_modules/diagram-js/lib/features/root-elements/RootElementsBehavior.js
function RootElementsBehavior(canvas, injector) {
  injector.invoke(CommandInterceptor, this);
  this.executed(function(event2) {
    var context = event2.context;
    if (context.rootElement) {
      canvas.setRootElement(context.rootElement);
    } else {
      context.rootElement = canvas.getRootElement();
    }
  });
  this.revert(function(event2) {
    var context = event2.context;
    if (context.rootElement) {
      canvas.setRootElement(context.rootElement);
    }
  });
}
e(RootElementsBehavior, CommandInterceptor);
RootElementsBehavior.$inject = ["canvas", "injector"];

// node_modules/diagram-js/lib/features/root-elements/index.js
var root_elements_default = {
  __init__: ["rootElementsBehavior"],
  rootElementsBehavior: ["type", RootElementsBehavior]
};

// node_modules/diagram-js/lib/util/EscapeUtil.js
var HTML_ESCAPE_MAP = {
  "&": "&amp;",
  "<": "&lt;",
  ">": "&gt;",
  '"': "&quot;",
  "'": "&#39;"
};
function escapeHTML(str) {
  str = "" + str;
  return str && str.replace(/[&<>"']/g, function(match) {
    return HTML_ESCAPE_MAP[match];
  });
}

// node_modules/bpmn-js/lib/util/DrilldownUtil.js
var planeSuffix = "_plane";
function getPlaneIdFromShape(element) {
  var id = element.id;
  if (is(element, "bpmn:SubProcess")) {
    return addPlaneSuffix(id);
  }
  return id;
}
function addPlaneSuffix(id) {
  return id + planeSuffix;
}

// node_modules/bpmn-js/lib/features/drilldown/DrilldownBreadcrumbs.js
var OPEN_CLASS = "bjs-breadcrumbs-shown";
function DrilldownBreadcrumbs(eventBus, elementRegistry, canvas) {
  var breadcrumbs = domify$1('<ul class="bjs-breadcrumbs"></ul>');
  var container = canvas.getContainer();
  var containerClasses = classes2(container);
  container.appendChild(breadcrumbs);
  var businessObjectParents = [];
  eventBus.on("element.changed", function(event2) {
    var shape = event2.element, businessObject = getBusinessObject(shape);
    var isPresent = find(businessObjectParents, function(element) {
      return element === businessObject;
    });
    if (!isPresent) {
      return;
    }
    updateBreadcrumbs();
  });
  function updateBreadcrumbs(element) {
    if (element) {
      businessObjectParents = getBusinessObjectParentChain(element);
    }
    var path = businessObjectParents.flatMap(function(parent) {
      var parentPlane = canvas.findRoot(getPlaneIdFromShape(parent)) || canvas.findRoot(parent.id);
      if (!parentPlane && is(parent, "bpmn:Process")) {
        var participant = elementRegistry.find(function(element2) {
          var businessObject = getBusinessObject(element2);
          return businessObject && businessObject.get("processRef") === parent;
        });
        parentPlane = participant && canvas.findRoot(participant.id);
      }
      if (!parentPlane) {
        return [];
      }
      var title = escapeHTML(parent.name || parent.id);
      var link = domify$1('<li><span class="bjs-crumb"><a title="' + title + '">' + title + "</a></span></li>");
      link.addEventListener("click", function() {
        canvas.setRootElement(parentPlane);
      });
      return link;
    });
    breadcrumbs.innerHTML = "";
    var visible = path.length > 1;
    containerClasses.toggle(OPEN_CLASS, visible);
    path.forEach(function(element2) {
      breadcrumbs.appendChild(element2);
    });
  }
  eventBus.on("root.set", function(event2) {
    updateBreadcrumbs(event2.element);
  });
}
DrilldownBreadcrumbs.$inject = ["eventBus", "elementRegistry", "canvas"];
function getBusinessObjectParentChain(child) {
  var businessObject = getBusinessObject(child);
  var parents = [];
  for (var element = businessObject; element; element = element.$parent) {
    if (is(element, "bpmn:SubProcess") || is(element, "bpmn:Process")) {
      parents.push(element);
    }
  }
  return parents.reverse();
}

// node_modules/bpmn-js/lib/features/drilldown/DrilldownCentering.js
function DrilldownCentering(eventBus, canvas) {
  var currentRoot = null;
  var positionMap = new Map();
  eventBus.on("root.set", function(event2) {
    var newRoot = event2.element;
    var currentViewbox = canvas.viewbox();
    var storedViewbox = positionMap.get(newRoot);
    positionMap.set(currentRoot, {
      x: currentViewbox.x,
      y: currentViewbox.y,
      zoom: currentViewbox.scale
    });
    currentRoot = newRoot;
    if (!is(newRoot, "bpmn:SubProcess") && !storedViewbox) {
      return;
    }
    storedViewbox = storedViewbox || { x: 0, y: 0, zoom: 1 };
    var dx = (currentViewbox.x - storedViewbox.x) * currentViewbox.scale, dy = (currentViewbox.y - storedViewbox.y) * currentViewbox.scale;
    if (dx !== 0 || dy !== 0) {
      canvas.scroll({
        dx,
        dy
      });
    }
    if (storedViewbox.zoom !== currentViewbox.scale) {
      canvas.zoom(storedViewbox.zoom, { x: 0, y: 0 });
    }
  });
  eventBus.on("diagram.clear", function() {
    positionMap.clear();
    currentRoot = null;
  });
}
DrilldownCentering.$inject = ["eventBus", "canvas"];
function Map() {
  this._entries = [];
  this.set = function(key, value) {
    var found = false;
    for (var k in this._entries) {
      if (this._entries[k][0] === key) {
        this._entries[k][1] = value;
        found = true;
        break;
      }
    }
    if (!found) {
      this._entries.push([key, value]);
    }
  };
  this.get = function(key) {
    for (var k in this._entries) {
      if (this._entries[k][0] === key) {
        return this._entries[k][1];
      }
    }
    return null;
  };
  this.clear = function() {
    this._entries.length = 0;
  };
  this.remove = function(key) {
    var idx = -1;
    for (var k in this._entries) {
      if (this._entries[k][0] === key) {
        idx = k;
        break;
      }
    }
    if (idx !== -1) {
      this._entries.splice(idx, 1);
    }
  };
}

// node_modules/bpmn-js/lib/features/drilldown/SubprocessCompatibility.js
var DEFAULT_POSITION = {
  x: 180,
  y: 160
};
function SubprocessCompatibility(eventBus, moddle) {
  this._eventBus = eventBus;
  this._moddle = moddle;
  var self = this;
  eventBus.on("import.render.start", 1500, function(e2, context) {
    self._handleImport(context.definitions);
  });
}
SubprocessCompatibility.prototype._handleImport = function(definitions) {
  if (!definitions.diagrams) {
    return;
  }
  var self = this;
  this._definitions = definitions;
  this._processToDiagramMap = {};
  definitions.diagrams.forEach(function(diagram) {
    if (!diagram.plane || !diagram.plane.bpmnElement) {
      return;
    }
    self._processToDiagramMap[diagram.plane.bpmnElement.id] = diagram;
  });
  var newDiagrams = definitions.diagrams.filter((diagram) => diagram.plane).flatMap((diagram) => self._createNewDiagrams(diagram.plane));
  newDiagrams.forEach(function(diagram) {
    self._movePlaneElementsToOrigin(diagram.plane);
  });
};
SubprocessCompatibility.prototype._createNewDiagrams = function(plane) {
  var self = this;
  var collapsedElements = [];
  var elementsToMove = [];
  plane.get("planeElement").forEach(function(diElement) {
    var businessObject = diElement.bpmnElement;
    if (!businessObject) {
      return;
    }
    var parent = businessObject.$parent;
    if (is(businessObject, "bpmn:SubProcess") && !diElement.isExpanded) {
      collapsedElements.push(businessObject);
    }
    if (shouldMoveToPlane(businessObject, plane)) {
      elementsToMove.push({ diElement, parent });
    }
  });
  var newDiagrams = [];
  collapsedElements.forEach(function(element) {
    if (!self._processToDiagramMap[element.id]) {
      var diagram = self._createDiagram(element);
      self._processToDiagramMap[element.id] = diagram;
      newDiagrams.push(diagram);
    }
  });
  elementsToMove.forEach(function(element) {
    var diElement = element.diElement;
    var parent = element.parent;
    while (parent && collapsedElements.indexOf(parent) === -1) {
      parent = parent.$parent;
    }
    if (!parent) {
      return;
    }
    var diagram = self._processToDiagramMap[parent.id];
    self._moveToDiPlane(diElement, diagram.plane);
  });
  return newDiagrams;
};
SubprocessCompatibility.prototype._movePlaneElementsToOrigin = function(plane) {
  var elements = plane.get("planeElement");
  var planeBounds = getPlaneBounds(plane);
  var offset = {
    x: planeBounds.x - DEFAULT_POSITION.x,
    y: planeBounds.y - DEFAULT_POSITION.y
  };
  elements.forEach(function(diElement) {
    if (diElement.waypoint) {
      diElement.waypoint.forEach(function(waypoint) {
        waypoint.x = waypoint.x - offset.x;
        waypoint.y = waypoint.y - offset.y;
      });
    } else if (diElement.bounds) {
      diElement.bounds.x = diElement.bounds.x - offset.x;
      diElement.bounds.y = diElement.bounds.y - offset.y;
    }
  });
};
SubprocessCompatibility.prototype._moveToDiPlane = function(diElement, newPlane) {
  var containingDiagram = findRootDiagram(diElement);
  var parentPlaneElement = containingDiagram.plane.get("planeElement");
  parentPlaneElement.splice(parentPlaneElement.indexOf(diElement), 1);
  newPlane.get("planeElement").push(diElement);
};
SubprocessCompatibility.prototype._createDiagram = function(businessObject) {
  var plane = this._moddle.create("bpmndi:BPMNPlane", {
    bpmnElement: businessObject
  });
  var diagram = this._moddle.create("bpmndi:BPMNDiagram", {
    plane
  });
  plane.$parent = diagram;
  plane.bpmnElement = businessObject;
  diagram.$parent = this._definitions;
  this._definitions.diagrams.push(diagram);
  return diagram;
};
SubprocessCompatibility.$inject = ["eventBus", "moddle"];
function findRootDiagram(element) {
  if (is(element, "bpmndi:BPMNDiagram")) {
    return element;
  } else {
    return findRootDiagram(element.$parent);
  }
}
function getPlaneBounds(plane) {
  var planeTrbl = {
    top: Infinity,
    right: -Infinity,
    bottom: -Infinity,
    left: Infinity
  };
  plane.planeElement.forEach(function(element) {
    if (!element.bounds) {
      return;
    }
    var trbl = asTRBL(element.bounds);
    planeTrbl.top = Math.min(trbl.top, planeTrbl.top);
    planeTrbl.left = Math.min(trbl.left, planeTrbl.left);
  });
  return asBounds(planeTrbl);
}
function shouldMoveToPlane(businessObject, plane) {
  var parent = businessObject.$parent;
  if (!is(parent, "bpmn:SubProcess") || parent === plane.bpmnElement) {
    return false;
  }
  if (isAny(businessObject, ["bpmn:DataInputAssociation", "bpmn:DataOutputAssociation"])) {
    return false;
  }
  return true;
}

// node_modules/bpmn-js/lib/features/drilldown/DrilldownOverlayBehavior.js
var LOW_PRIORITY2 = 250;
var ARROW_DOWN_SVG = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M4.81801948,3.50735931 L10.4996894,9.1896894 L10.5,4 L12,4 L12,12 L4,12 L4,10.5 L9.6896894,10.4996894 L3.75735931,4.56801948 C3.46446609,4.27512627 3.46446609,3.80025253 3.75735931,3.50735931 C4.05025253,3.21446609 4.52512627,3.21446609 4.81801948,3.50735931 Z"/></svg>';
var EMPTY_MARKER = "bjs-drilldown-empty";
function DrilldownOverlayBehavior(canvas, eventBus, elementRegistry, overlays, translate3) {
  CommandInterceptor.call(this, eventBus);
  this._canvas = canvas;
  this._eventBus = eventBus;
  this._elementRegistry = elementRegistry;
  this._overlays = overlays;
  this._translate = translate3;
  var self = this;
  this.executed("shape.toggleCollapse", LOW_PRIORITY2, function(context) {
    var shape = context.shape;
    if (self._canDrillDown(shape)) {
      self._addOverlay(shape);
    } else {
      self._removeOverlay(shape);
    }
  }, true);
  this.reverted("shape.toggleCollapse", LOW_PRIORITY2, function(context) {
    var shape = context.shape;
    if (self._canDrillDown(shape)) {
      self._addOverlay(shape);
    } else {
      self._removeOverlay(shape);
    }
  }, true);
  this.executed(
    ["shape.create", "shape.move", "shape.delete"],
    LOW_PRIORITY2,
    function(context) {
      var oldParent = context.oldParent, newParent = context.newParent || context.parent, shape = context.shape;
      if (self._canDrillDown(shape)) {
        self._addOverlay(shape);
      }
      self._updateDrilldownOverlay(oldParent);
      self._updateDrilldownOverlay(newParent);
      self._updateDrilldownOverlay(shape);
    },
    true
  );
  this.reverted(
    ["shape.create", "shape.move", "shape.delete"],
    LOW_PRIORITY2,
    function(context) {
      var oldParent = context.oldParent, newParent = context.newParent || context.parent, shape = context.shape;
      if (self._canDrillDown(shape)) {
        self._addOverlay(shape);
      }
      self._updateDrilldownOverlay(oldParent);
      self._updateDrilldownOverlay(newParent);
      self._updateDrilldownOverlay(shape);
    },
    true
  );
  eventBus.on("import.render.complete", function() {
    elementRegistry.filter(function(e2) {
      return self._canDrillDown(e2);
    }).map(function(el) {
      self._addOverlay(el);
    });
  });
}
e(DrilldownOverlayBehavior, CommandInterceptor);
DrilldownOverlayBehavior.prototype._updateDrilldownOverlay = function(shape) {
  var canvas = this._canvas;
  if (!shape) {
    return;
  }
  var root = canvas.findRoot(shape);
  if (root) {
    this._updateOverlayVisibility(root);
  }
};
DrilldownOverlayBehavior.prototype._canDrillDown = function(element) {
  var canvas = this._canvas;
  return is(element, "bpmn:SubProcess") && canvas.findRoot(getPlaneIdFromShape(element));
};
DrilldownOverlayBehavior.prototype._updateOverlayVisibility = function(element) {
  var overlays = this._overlays;
  var businessObject = getBusinessObject(element);
  var overlay = overlays.get({ element: businessObject.id, type: "drilldown" })[0];
  if (!overlay) {
    return;
  }
  var hasFlowElements = businessObject && businessObject.get("flowElements") && businessObject.get("flowElements").length;
  classes2(overlay.html).toggle(EMPTY_MARKER, !hasFlowElements);
};
DrilldownOverlayBehavior.prototype._addOverlay = function(element) {
  var canvas = this._canvas, overlays = this._overlays, bo = getBusinessObject(element);
  var existingOverlays = overlays.get({ element, type: "drilldown" });
  if (existingOverlays.length) {
    this._removeOverlay(element);
  }
  var button = domify$1('<button type="button" class="bjs-drilldown">' + ARROW_DOWN_SVG + "</button>"), elementName = bo.get("name") || bo.get("id"), title = this._translate("Open {element}", { element: elementName });
  button.setAttribute("title", title);
  button.addEventListener("click", function() {
    canvas.setRootElement(canvas.findRoot(getPlaneIdFromShape(element)));
  });
  overlays.add(element, "drilldown", {
    position: {
      bottom: -7,
      right: -8
    },
    html: button
  });
  this._updateOverlayVisibility(element);
};
DrilldownOverlayBehavior.prototype._removeOverlay = function(element) {
  var overlays = this._overlays;
  overlays.remove({
    element,
    type: "drilldown"
  });
};
DrilldownOverlayBehavior.$inject = [
  "canvas",
  "eventBus",
  "elementRegistry",
  "overlays",
  "translate"
];

// node_modules/bpmn-js/lib/features/drilldown/index.js
var drilldown_default = {
  __depends__: [overlays_default, change_support_default, root_elements_default],
  __init__: ["drilldownBreadcrumbs", "drilldownOverlayBehavior", "drilldownCentering", "subprocessCompatibility"],
  drilldownBreadcrumbs: ["type", DrilldownBreadcrumbs],
  drilldownCentering: ["type", DrilldownCentering],
  drilldownOverlayBehavior: ["type", DrilldownOverlayBehavior],
  subprocessCompatibility: ["type", SubprocessCompatibility]
};

// node_modules/diagram-js/lib/features/outline/Outline.js
var LOW_PRIORITY3 = 500;
var DEFAULT_PRIORITY2 = 1e3;
function Outline(eventBus, styles) {
  this._eventBus = eventBus;
  this.offset = 5;
  var OUTLINE_STYLE = styles.cls("djs-outline", ["no-fill"]);
  var self = this;
  function createOutline(gfx) {
    var outline = create("rect");
    attr(outline, assign({
      x: 0,
      y: 0,
      rx: 4,
      width: 100,
      height: 100
    }, OUTLINE_STYLE));
    return outline;
  }
  eventBus.on(["shape.added", "shape.changed"], LOW_PRIORITY3, function(event2) {
    var element = event2.element, gfx = event2.gfx;
    var outline = query(".djs-outline", gfx);
    if (!outline) {
      outline = self.getOutline(element) || createOutline(gfx);
      append(gfx, outline);
    }
    self.updateShapeOutline(outline, element);
  });
  eventBus.on(["connection.added", "connection.changed"], function(event2) {
    var element = event2.element, gfx = event2.gfx;
    var outline = query(".djs-outline", gfx);
    if (!outline) {
      outline = createOutline(gfx);
      append(gfx, outline);
    }
    self.updateConnectionOutline(outline, element);
  });
}
Outline.prototype.updateShapeOutline = function(outline, element) {
  var updated = false;
  var providers = this._getProviders();
  if (providers.length) {
    forEach(providers, function(provider) {
      updated = updated || provider.updateOutline(element, outline);
    });
  }
  if (!updated) {
    attr(outline, {
      x: -this.offset,
      y: -this.offset,
      width: element.width + this.offset * 2,
      height: element.height + this.offset * 2
    });
  }
};
Outline.prototype.updateConnectionOutline = function(outline, connection) {
  var bbox = getBBox(connection);
  attr(outline, {
    x: bbox.x - this.offset,
    y: bbox.y - this.offset,
    width: bbox.width + this.offset * 2,
    height: bbox.height + this.offset * 2
  });
};
Outline.prototype.registerProvider = function(priority, provider) {
  if (!provider) {
    provider = priority;
    priority = DEFAULT_PRIORITY2;
  }
  this._eventBus.on("outline.getProviders", priority, function(event2) {
    event2.providers.push(provider);
  });
};
Outline.prototype._getProviders = function() {
  var event2 = this._eventBus.createEvent({
    type: "outline.getProviders",
    providers: []
  });
  this._eventBus.fire(event2);
  return event2.providers;
};
Outline.prototype.getOutline = function(element) {
  var outline;
  var providers = this._getProviders();
  forEach(providers, function(provider) {
    if (!isFunction(provider.getOutline)) {
      return;
    }
    outline = outline || provider.getOutline(element);
  });
  return outline;
};
Outline.$inject = ["eventBus", "styles", "elementRegistry"];

// node_modules/diagram-js/lib/features/outline/index.js
var outline_default = {
  __init__: ["outline"],
  outline: ["type", Outline]
};

// node_modules/bpmn-js/lib/features/outline/OutlineUtil.js
var DATA_OBJECT_REFERENCE_OUTLINE_PATH = "M44.7648 11.3263L36.9892 2.64074C36.0451 1.58628 34.5651 0.988708 33.1904 0.988708H5.98667C3.22688 0.988708 0.989624 3.34892 0.989624 6.26039V55.0235C0.989624 57.9349 3.22688 60.2952 5.98667 60.2952H40.966C43.7257 60.2952 45.963 57.9349 45.963 55.0235V14.9459C45.963 13.5998 45.6407 12.3048 44.7648 11.3263Z";
var DATA_STORE_REFERENCE_OUTLINE_PATH = "M1.03845 48.1347C1.03845 49.3511 1.07295 50.758 1.38342 52.064C1.69949 53.3938 2.32428 54.7154 3.56383 55.6428C6.02533 57.4841 10.1161 58.7685 14.8212 59.6067C19.5772 60.4538 25.1388 60.8738 30.6831 60.8738C36.2276 60.8738 41.7891 60.4538 46.545 59.6067C51.2504 58.7687 55.3412 57.4842 57.8028 55.6429C59.0424 54.7156 59.6673 53.3938 59.9834 52.064C60.2938 50.7579 60.3285 49.351 60.3285 48.1344V13.8415C60.3285 12.6249 60.2938 11.218 59.9834 9.91171C59.6673 8.58194 59.0423 7.2602 57.8027 6.33294C55.341 4.49168 51.2503 3.20723 46.545 2.36914C41.7891 1.522 36.2276 1.10204 30.6831 1.10205C25.1388 1.10206 19.5772 1.52206 14.8213 2.36923C10.1162 3.20734 6.02543 4.49183 3.5639 6.33314C2.32433 7.26038 1.69951 8.58206 1.38343 9.91181C1.07295 11.2179 1.03845 12.6247 1.03845 13.8411V48.1347Z";
var DATA_OBJECT_REFERENCE_STANDARD_SIZE = { width: 36, height: 50 };
var DATA_STORE_REFERENCE_STANDARD_SIZE = { width: 50, height: 50 };
function createPath(path, attrs, OUTLINE_STYLE) {
  return create("path", {
    d: path,
    strokeWidth: 2,
    transform: `translate(${attrs.x}, ${attrs.y})`,
    ...OUTLINE_STYLE
  });
}

// node_modules/bpmn-js/lib/features/outline/OutlineProvider.js
var DEFAULT_OFFSET = 5;
function OutlineProvider(outline, styles) {
  this._styles = styles;
  outline.registerProvider(this);
}
OutlineProvider.$inject = [
  "outline",
  "styles"
];
OutlineProvider.prototype.getOutline = function(element) {
  const OUTLINE_STYLE = this._styles.cls("djs-outline", ["no-fill"]);
  var outline;
  if (isLabel(element)) {
    return;
  }
  if (is(element, "bpmn:Gateway")) {
    outline = create("rect");
    assign(outline.style, {
      "transform-box": "fill-box",
      "transform": "rotate(45deg)",
      "transform-origin": "center"
    });
    attr(outline, assign({
      x: 2,
      y: 2,
      rx: 4,
      width: element.width - 4,
      height: element.height - 4
    }, OUTLINE_STYLE));
  } else if (isAny(element, ["bpmn:Task", "bpmn:SubProcess", "bpmn:Group", "bpmn:CallActivity"])) {
    outline = create("rect");
    attr(outline, assign({
      x: -DEFAULT_OFFSET,
      y: -DEFAULT_OFFSET,
      rx: 14,
      width: element.width + DEFAULT_OFFSET * 2,
      height: element.height + DEFAULT_OFFSET * 2
    }, OUTLINE_STYLE));
  } else if (is(element, "bpmn:EndEvent")) {
    outline = create("circle");
    attr(outline, assign({
      cx: element.width / 2,
      cy: element.height / 2,
      r: element.width / 2 + DEFAULT_OFFSET + 1
    }, OUTLINE_STYLE));
  } else if (is(element, "bpmn:Event")) {
    outline = create("circle");
    attr(outline, assign({
      cx: element.width / 2,
      cy: element.height / 2,
      r: element.width / 2 + DEFAULT_OFFSET
    }, OUTLINE_STYLE));
  } else if (is(element, "bpmn:DataObjectReference") && isStandardSize(element, "bpmn:DataObjectReference")) {
    outline = createPath(
      DATA_OBJECT_REFERENCE_OUTLINE_PATH,
      { x: -6, y: -6 },
      OUTLINE_STYLE
    );
  } else if (is(element, "bpmn:DataStoreReference") && isStandardSize(element, "bpmn:DataStoreReference")) {
    outline = createPath(
      DATA_STORE_REFERENCE_OUTLINE_PATH,
      { x: -6, y: -6 },
      OUTLINE_STYLE
    );
  }
  return outline;
};
OutlineProvider.prototype.updateOutline = function(element, outline) {
  if (isLabel(element)) {
    return;
  }
  if (isAny(element, ["bpmn:SubProcess", "bpmn:Group"])) {
    attr(outline, {
      width: element.width + DEFAULT_OFFSET * 2,
      height: element.height + DEFAULT_OFFSET * 2
    });
    return true;
  } else if (isAny(element, [
    "bpmn:Event",
    "bpmn:Gateway",
    "bpmn:DataStoreReference",
    "bpmn:DataObjectReference"
  ])) {
    return true;
  }
  return false;
};
function isStandardSize(element, type) {
  var standardSize;
  if (type === "bpmn:DataObjectReference") {
    standardSize = DATA_OBJECT_REFERENCE_STANDARD_SIZE;
  } else if (type === "bpmn:DataStoreReference") {
    standardSize = DATA_STORE_REFERENCE_STANDARD_SIZE;
  }
  return element.width === standardSize.width && element.height === standardSize.height;
}

// node_modules/bpmn-js/lib/features/outline/index.js
var outline_default2 = {
  __depends__: [
    outline_default
  ],
  __init__: ["outlineProvider"],
  outlineProvider: ["type", OutlineProvider]
};

// node_modules/diagram-js/lib/util/Event.js
function getOriginal(event2) {
  return event2.originalEvent || event2.srcEvent;
}
function toPoint(event2) {
  if (event2.pointers && event2.pointers.length) {
    event2 = event2.pointers[0];
  }
  if (event2.touches && event2.touches.length) {
    event2 = event2.touches[0];
  }
  return event2 ? {
    x: event2.clientX,
    y: event2.clientY
  } : null;
}

// node_modules/diagram-js/lib/util/Platform.js
function isMac() {
  return /mac/i.test(navigator.platform);
}

// node_modules/diagram-js/lib/util/Mouse.js
function isButton(event2, button) {
  return (getOriginal(event2) || event2).button === button;
}
function isPrimaryButton(event2) {
  return isButton(event2, 0);
}
function isAuxiliaryButton(event2) {
  return isButton(event2, 1);
}
function hasSecondaryModifier(event2) {
  var originalEvent = getOriginal(event2) || event2;
  return isPrimaryButton(event2) && originalEvent.shiftKey;
}

// node_modules/diagram-js/lib/features/interaction-events/InteractionEvents.js
function allowAll(event2) {
  return true;
}
function allowPrimaryAndAuxiliary(event2) {
  return isPrimaryButton(event2) || isAuxiliaryButton(event2);
}
var LOW_PRIORITY4 = 500;
function InteractionEvents(eventBus, elementRegistry, styles) {
  var self = this;
  function fire(type, event2, element) {
    if (isIgnored(type, event2)) {
      return;
    }
    var target, gfx, returnValue;
    if (!element) {
      target = event2.delegateTarget || event2.target;
      if (target) {
        gfx = target;
        element = elementRegistry.get(gfx);
      }
    } else {
      gfx = elementRegistry.getGraphics(element);
    }
    if (!gfx || !element) {
      return;
    }
    returnValue = eventBus.fire(type, {
      element,
      gfx,
      originalEvent: event2
    });
    if (returnValue === false) {
      event2.stopPropagation();
      event2.preventDefault();
    }
  }
  var handlers = {};
  function mouseHandler(localEventName) {
    return handlers[localEventName];
  }
  function isIgnored(localEventName, event2) {
    var filter2 = ignoredFilters[localEventName] || isPrimaryButton;
    return !filter2(event2);
  }
  var bindings = {
    click: "element.click",
    contextmenu: "element.contextmenu",
    dblclick: "element.dblclick",
    mousedown: "element.mousedown",
    mousemove: "element.mousemove",
    mouseover: "element.hover",
    mouseout: "element.out",
    mouseup: "element.mouseup"
  };
  var ignoredFilters = {
    "element.contextmenu": allowAll,
    "element.mousedown": allowPrimaryAndAuxiliary,
    "element.mouseup": allowPrimaryAndAuxiliary,
    "element.click": allowPrimaryAndAuxiliary,
    "element.dblclick": allowPrimaryAndAuxiliary
  };
  function triggerMouseEvent(eventName, event2, targetElement) {
    var localEventName = bindings[eventName];
    if (!localEventName) {
      throw new Error("unmapped DOM event name <" + eventName + ">");
    }
    return fire(localEventName, event2, targetElement);
  }
  var ELEMENT_SELECTOR = "svg, .djs-element";
  function registerEvent(node2, event2, localEvent, ignoredFilter) {
    var handler = handlers[localEvent] = function(event3) {
      fire(localEvent, event3);
    };
    if (ignoredFilter) {
      ignoredFilters[localEvent] = ignoredFilter;
    }
    handler.$delegate = delegate.bind(node2, ELEMENT_SELECTOR, event2, handler);
  }
  function unregisterEvent(node2, event2, localEvent) {
    var handler = mouseHandler(localEvent);
    if (!handler) {
      return;
    }
    delegate.unbind(node2, event2, handler.$delegate);
  }
  function registerEvents(svg) {
    forEach(bindings, function(val, key) {
      registerEvent(svg, key, val);
    });
  }
  function unregisterEvents(svg) {
    forEach(bindings, function(val, key) {
      unregisterEvent(svg, key, val);
    });
  }
  eventBus.on("canvas.destroy", function(event2) {
    unregisterEvents(event2.svg);
  });
  eventBus.on("canvas.init", function(event2) {
    registerEvents(event2.svg);
  });
  eventBus.on(["shape.added", "connection.added"], function(event2) {
    var element = event2.element, gfx = event2.gfx;
    eventBus.fire("interactionEvents.createHit", { element, gfx });
  });
  eventBus.on([
    "shape.changed",
    "connection.changed"
  ], LOW_PRIORITY4, function(event2) {
    var element = event2.element, gfx = event2.gfx;
    eventBus.fire("interactionEvents.updateHit", { element, gfx });
  });
  eventBus.on("interactionEvents.createHit", LOW_PRIORITY4, function(event2) {
    var element = event2.element, gfx = event2.gfx;
    self.createDefaultHit(element, gfx);
  });
  eventBus.on("interactionEvents.updateHit", function(event2) {
    var element = event2.element, gfx = event2.gfx;
    self.updateDefaultHit(element, gfx);
  });
  var STROKE_HIT_STYLE = createHitStyle("djs-hit djs-hit-stroke");
  var CLICK_STROKE_HIT_STYLE = createHitStyle("djs-hit djs-hit-click-stroke");
  var ALL_HIT_STYLE = createHitStyle("djs-hit djs-hit-all");
  var NO_MOVE_HIT_STYLE = createHitStyle("djs-hit djs-hit-no-move");
  var HIT_TYPES = {
    "all": ALL_HIT_STYLE,
    "click-stroke": CLICK_STROKE_HIT_STYLE,
    "stroke": STROKE_HIT_STYLE,
    "no-move": NO_MOVE_HIT_STYLE
  };
  function createHitStyle(classNames, attrs) {
    attrs = assign({
      stroke: "white",
      strokeWidth: 15
    }, attrs || {});
    return styles.cls(classNames, ["no-fill", "no-border"], attrs);
  }
  function applyStyle(hit, type) {
    var attrs = HIT_TYPES[type];
    if (!attrs) {
      throw new Error("invalid hit type <" + type + ">");
    }
    attr(hit, attrs);
    return hit;
  }
  function appendHit(gfx, hit) {
    append(gfx, hit);
  }
  this.removeHits = function(gfx) {
    var hits = all(".djs-hit", gfx);
    forEach(hits, remove);
  };
  this.createDefaultHit = function(element, gfx) {
    var waypoints = element.waypoints, isFrame = element.isFrame, boxType;
    if (waypoints) {
      return this.createWaypointsHit(gfx, waypoints);
    } else {
      boxType = isFrame ? "stroke" : "all";
      return this.createBoxHit(gfx, boxType, {
        width: element.width,
        height: element.height
      });
    }
  };
  this.createWaypointsHit = function(gfx, waypoints) {
    var hit = createLine(waypoints);
    applyStyle(hit, "stroke");
    appendHit(gfx, hit);
    return hit;
  };
  this.createBoxHit = function(gfx, type, attrs) {
    attrs = assign({
      x: 0,
      y: 0
    }, attrs);
    var hit = create("rect");
    applyStyle(hit, type);
    attr(hit, attrs);
    appendHit(gfx, hit);
    return hit;
  };
  this.updateDefaultHit = function(element, gfx) {
    var hit = query(".djs-hit", gfx);
    if (!hit) {
      return;
    }
    if (element.waypoints) {
      updateLine(hit, element.waypoints);
    } else {
      attr(hit, {
        width: element.width,
        height: element.height
      });
    }
    return hit;
  };
  this.fire = fire;
  this.triggerMouseEvent = triggerMouseEvent;
  this.mouseHandler = mouseHandler;
  this.registerEvent = registerEvent;
  this.unregisterEvent = unregisterEvent;
}
InteractionEvents.$inject = [
  "eventBus",
  "elementRegistry",
  "styles"
];

// node_modules/diagram-js/lib/features/interaction-events/index.js
var interaction_events_default = {
  __init__: ["interactionEvents"],
  interactionEvents: ["type", InteractionEvents]
};

// node_modules/diagram-js/lib/features/selection/Selection.js
function Selection(eventBus, canvas) {
  this._eventBus = eventBus;
  this._canvas = canvas;
  this._selectedElements = [];
  var self = this;
  eventBus.on(["shape.remove", "connection.remove"], function(e2) {
    var element = e2.element;
    self.deselect(element);
  });
  eventBus.on(["diagram.clear", "root.set"], function(e2) {
    self.select(null);
  });
}
Selection.$inject = ["eventBus", "canvas"];
Selection.prototype.deselect = function(element) {
  var selectedElements = this._selectedElements;
  var idx = selectedElements.indexOf(element);
  if (idx !== -1) {
    var oldSelection = selectedElements.slice();
    selectedElements.splice(idx, 1);
    this._eventBus.fire("selection.changed", { oldSelection, newSelection: selectedElements });
  }
};
Selection.prototype.get = function() {
  return this._selectedElements;
};
Selection.prototype.isSelected = function(element) {
  return this._selectedElements.indexOf(element) !== -1;
};
Selection.prototype.select = function(elements, add2) {
  var selectedElements = this._selectedElements, oldSelection = selectedElements.slice();
  if (!isArray(elements)) {
    elements = elements ? [elements] : [];
  }
  var canvas = this._canvas;
  var rootElement = canvas.getRootElement();
  elements = elements.filter(function(element) {
    var elementRoot = canvas.findRoot(element);
    return rootElement === elementRoot;
  });
  if (add2) {
    forEach(elements, function(element) {
      if (selectedElements.indexOf(element) !== -1) {
        return;
      } else {
        selectedElements.push(element);
      }
    });
  } else {
    this._selectedElements = selectedElements = elements.slice();
  }
  this._eventBus.fire("selection.changed", { oldSelection, newSelection: selectedElements });
};

// node_modules/diagram-js/lib/features/selection/SelectionVisuals.js
var MARKER_HOVER = "hover";
var MARKER_SELECTED = "selected";
var SELECTION_OUTLINE_PADDING = 6;
function SelectionVisuals(canvas, eventBus, selection) {
  this._canvas = canvas;
  var self = this;
  this._multiSelectionBox = null;
  function addMarker(e2, cls) {
    canvas.addMarker(e2, cls);
  }
  function removeMarker(e2, cls) {
    canvas.removeMarker(e2, cls);
  }
  eventBus.on("element.hover", function(event2) {
    addMarker(event2.element, MARKER_HOVER);
  });
  eventBus.on("element.out", function(event2) {
    removeMarker(event2.element, MARKER_HOVER);
  });
  eventBus.on("selection.changed", function(event2) {
    function deselect(s) {
      removeMarker(s, MARKER_SELECTED);
    }
    function select(s) {
      addMarker(s, MARKER_SELECTED);
    }
    var oldSelection = event2.oldSelection, newSelection = event2.newSelection;
    forEach(oldSelection, function(e2) {
      if (newSelection.indexOf(e2) === -1) {
        deselect(e2);
      }
    });
    forEach(newSelection, function(e2) {
      if (oldSelection.indexOf(e2) === -1) {
        select(e2);
      }
    });
    self._updateSelectionOutline(newSelection);
  });
  eventBus.on("element.changed", function(event2) {
    if (selection.isSelected(event2.element)) {
      self._updateSelectionOutline(selection.get());
    }
  });
}
SelectionVisuals.$inject = [
  "canvas",
  "eventBus",
  "selection"
];
SelectionVisuals.prototype._updateSelectionOutline = function(selection) {
  var layer = this._canvas.getLayer("selectionOutline");
  clear(layer);
  var enabled = selection.length > 1;
  var container = this._canvas.getContainer();
  classes(container)[enabled ? "add" : "remove"]("djs-multi-select");
  if (!enabled) {
    return;
  }
  var bBox = addSelectionOutlinePadding(getBBox(selection));
  var rect = create("rect");
  attr(rect, assign({
    rx: 3
  }, bBox));
  classes(rect).add("djs-selection-outline");
  append(layer, rect);
};
function addSelectionOutlinePadding(bBox) {
  return {
    x: bBox.x - SELECTION_OUTLINE_PADDING,
    y: bBox.y - SELECTION_OUTLINE_PADDING,
    width: bBox.width + SELECTION_OUTLINE_PADDING * 2,
    height: bBox.height + SELECTION_OUTLINE_PADDING * 2
  };
}

// node_modules/diagram-js/lib/features/selection/SelectionBehavior.js
function SelectionBehavior(eventBus, selection, canvas, elementRegistry) {
  eventBus.on("create.end", 500, function(event2) {
    var context = event2.context, canExecute = context.canExecute, elements = context.elements, hints = context.hints || {}, autoSelect = hints.autoSelect;
    if (canExecute) {
      if (autoSelect === false) {
        return;
      }
      if (isArray(autoSelect)) {
        selection.select(autoSelect);
      } else {
        selection.select(elements.filter(isShown));
      }
    }
  });
  eventBus.on("connect.end", 500, function(event2) {
    var context = event2.context, connection = context.connection;
    if (connection) {
      selection.select(connection);
    }
  });
  eventBus.on("shape.move.end", 500, function(event2) {
    var previousSelection = event2.previousSelection || [];
    var shape = elementRegistry.get(event2.context.shape.id);
    var isSelected = find(previousSelection, function(selectedShape) {
      return shape.id === selectedShape.id;
    });
    if (!isSelected) {
      selection.select(shape);
    }
  });
  eventBus.on("element.click", function(event2) {
    if (!isPrimaryButton(event2)) {
      return;
    }
    var element = event2.element;
    if (element === canvas.getRootElement()) {
      element = null;
    }
    var isSelected = selection.isSelected(element), isMultiSelect = selection.get().length > 1;
    var add2 = hasSecondaryModifier(event2);
    if (isSelected && isMultiSelect) {
      if (add2) {
        return selection.deselect(element);
      } else {
        return selection.select(element);
      }
    } else if (!isSelected) {
      selection.select(element, add2);
    } else {
      selection.deselect(element);
    }
  });
}
SelectionBehavior.$inject = [
  "eventBus",
  "selection",
  "canvas",
  "elementRegistry"
];
function isShown(element) {
  return !element.hidden;
}

// node_modules/diagram-js/lib/features/selection/index.js
var selection_default = {
  __init__: ["selectionVisuals", "selectionBehavior"],
  __depends__: [
    interaction_events_default,
    outline_default
  ],
  selection: ["type", Selection],
  selectionVisuals: ["type", SelectionVisuals],
  selectionBehavior: ["type", SelectionBehavior]
};

// node_modules/didi/dist/index.js
var CLASS_PATTERN = /^class[ {]/;
function isClass(fn) {
  return CLASS_PATTERN.test(fn.toString());
}
function isArray4(obj) {
  return Array.isArray(obj);
}
function hasOwnProp(obj, prop) {
  return Object.prototype.hasOwnProperty.call(obj, prop);
}
function annotate(...args) {
  if (args.length === 1 && isArray4(args[0])) {
    args = args[0];
  }
  args = [...args];
  const fn = args.pop();
  fn.$inject = args;
  return fn;
}
var CONSTRUCTOR_ARGS = /constructor\s*[^(]*\(\s*([^)]*)\)/m;
var FN_ARGS = /^(?:async\s+)?(?:function\s*[^(]*)?(?:\(\s*([^)]*)\)|(\w+))/m;
var FN_ARG = /\/\*([^*]*)\*\//m;
function parseAnnotations(fn) {
  if (typeof fn !== "function") {
    throw new Error(`Cannot annotate "${fn}". Expected a function!`);
  }
  const match = fn.toString().match(isClass(fn) ? CONSTRUCTOR_ARGS : FN_ARGS);
  if (!match) {
    return [];
  }
  const args = match[1] || match[2];
  return args && args.split(",").map((arg) => {
    const argMatch = arg.match(FN_ARG);
    return (argMatch && argMatch[1] || arg).trim();
  }) || [];
}
function Injector(modules, _parent) {
  const parent = _parent || /** @type InjectorContext */
  {
    get: function(name2, strict) {
      currentlyResolving.push(name2);
      if (strict === false) {
        return null;
      } else {
        throw error3(`No provider for "${name2}"!`);
      }
    }
  };
  const currentlyResolving = [];
  const providers = this._providers = Object.create(parent._providers || null);
  const instances = this._instances = /* @__PURE__ */ Object.create(null);
  const self = instances.injector = this;
  const error3 = function(msg) {
    const stack = currentlyResolving.join(" -> ");
    currentlyResolving.length = 0;
    return new Error(stack ? `${msg} (Resolving: ${stack})` : msg);
  };
  function get2(name2, strict) {
    if (!providers[name2] && name2.includes(".")) {
      const parts = name2.split(".");
      let pivot = get2(
        /** @type { string } */
        parts.shift()
      );
      while (parts.length) {
        pivot = pivot[
          /** @type { string } */
          parts.shift()
        ];
      }
      return pivot;
    }
    if (hasOwnProp(instances, name2)) {
      return instances[name2];
    }
    if (hasOwnProp(providers, name2)) {
      if (currentlyResolving.indexOf(name2) !== -1) {
        currentlyResolving.push(name2);
        throw error3("Cannot resolve circular dependency!");
      }
      currentlyResolving.push(name2);
      instances[name2] = providers[name2][0](providers[name2][1]);
      currentlyResolving.pop();
      return instances[name2];
    }
    return parent.get(name2, strict);
  }
  function fnDef(fn, locals) {
    if (typeof locals === "undefined") {
      locals = {};
    }
    if (typeof fn !== "function") {
      if (isArray4(fn)) {
        fn = annotate(fn.slice());
      } else {
        throw error3(`Cannot invoke "${fn}". Expected a function!`);
      }
    }
    const inject = fn.$inject || parseAnnotations(fn);
    const dependencies = inject.map((dep) => {
      if (hasOwnProp(locals, dep)) {
        return locals[dep];
      } else {
        return get2(dep);
      }
    });
    return {
      fn,
      dependencies
    };
  }
  function instantiate(type) {
    const {
      fn,
      dependencies
    } = fnDef(type);
    const Constructor = Function.prototype.bind.call(fn, null, ...dependencies);
    return new Constructor();
  }
  function invoke(func, context, locals) {
    const {
      fn,
      dependencies
    } = fnDef(func, locals);
    return fn.apply(context, dependencies);
  }
  function createPrivateInjectorFactory(childInjector) {
    return annotate((key) => childInjector.get(key));
  }
  function createChild(modules2, forceNewInstances) {
    if (forceNewInstances && forceNewInstances.length) {
      const fromParentModule = /* @__PURE__ */ Object.create(null);
      const matchedScopes = /* @__PURE__ */ Object.create(null);
      const privateInjectorsCache = [];
      const privateChildInjectors = [];
      const privateChildFactories = [];
      let provider;
      let cacheIdx;
      let privateChildInjector;
      let privateChildInjectorFactory;
      for (let name2 in providers) {
        provider = providers[name2];
        if (forceNewInstances.indexOf(name2) !== -1) {
          if (provider[2] === "private") {
            cacheIdx = privateInjectorsCache.indexOf(provider[3]);
            if (cacheIdx === -1) {
              privateChildInjector = provider[3].createChild([], forceNewInstances);
              privateChildInjectorFactory = createPrivateInjectorFactory(privateChildInjector);
              privateInjectorsCache.push(provider[3]);
              privateChildInjectors.push(privateChildInjector);
              privateChildFactories.push(privateChildInjectorFactory);
              fromParentModule[name2] = [privateChildInjectorFactory, name2, "private", privateChildInjector];
            } else {
              fromParentModule[name2] = [privateChildFactories[cacheIdx], name2, "private", privateChildInjectors[cacheIdx]];
            }
          } else {
            fromParentModule[name2] = [provider[2], provider[1]];
          }
          matchedScopes[name2] = true;
        }
        if ((provider[2] === "factory" || provider[2] === "type") && provider[1].$scope) {
          forceNewInstances.forEach((scope) => {
            if (provider[1].$scope.indexOf(scope) !== -1) {
              fromParentModule[name2] = [provider[2], provider[1]];
              matchedScopes[scope] = true;
            }
          });
        }
      }
      forceNewInstances.forEach((scope) => {
        if (!matchedScopes[scope]) {
          throw new Error('No provider for "' + scope + '". Cannot use provider from the parent!');
        }
      });
      modules2.unshift(fromParentModule);
    }
    return new Injector(modules2, self);
  }
  const factoryMap = {
    factory: invoke,
    type: instantiate,
    value: function(value) {
      return value;
    }
  };
  function createInitializer(moduleDefinition, injector) {
    const initializers = moduleDefinition.__init__ || [];
    return function() {
      initializers.forEach((initializer) => {
        if (typeof initializer === "string") {
          injector.get(initializer);
        } else {
          injector.invoke(initializer);
        }
      });
    };
  }
  function loadModule(moduleDefinition) {
    const moduleExports = moduleDefinition.__exports__;
    if (moduleExports) {
      const nestedModules = moduleDefinition.__modules__;
      const clonedModule = Object.keys(moduleDefinition).reduce((clonedModule2, key) => {
        if (key !== "__exports__" && key !== "__modules__" && key !== "__init__" && key !== "__depends__") {
          clonedModule2[key] = moduleDefinition[key];
        }
        return clonedModule2;
      }, /* @__PURE__ */ Object.create(null));
      const childModules = (nestedModules || []).concat(clonedModule);
      const privateInjector = createChild(childModules);
      const getFromPrivateInjector = annotate(function(key) {
        return privateInjector.get(key);
      });
      moduleExports.forEach(function(key) {
        providers[key] = [getFromPrivateInjector, key, "private", privateInjector];
      });
      const initializers = (moduleDefinition.__init__ || []).slice();
      initializers.unshift(function() {
        privateInjector.init();
      });
      moduleDefinition = Object.assign({}, moduleDefinition, {
        __init__: initializers
      });
      return createInitializer(moduleDefinition, privateInjector);
    }
    Object.keys(moduleDefinition).forEach(function(key) {
      if (key === "__init__" || key === "__depends__") {
        return;
      }
      const typeDeclaration = (
        /** @type { TypedDeclaration } */
        moduleDefinition[key]
      );
      if (typeDeclaration[2] === "private") {
        providers[key] = typeDeclaration;
        return;
      }
      const type = typeDeclaration[0];
      const value = typeDeclaration[1];
      providers[key] = [factoryMap[type], arrayUnwrap(type, value), type];
    });
    return createInitializer(moduleDefinition, self);
  }
  function resolveDependencies(moduleDefinitions, moduleDefinition) {
    if (moduleDefinitions.indexOf(moduleDefinition) !== -1) {
      return moduleDefinitions;
    }
    moduleDefinitions = (moduleDefinition.__depends__ || []).reduce(resolveDependencies, moduleDefinitions);
    if (moduleDefinitions.indexOf(moduleDefinition) !== -1) {
      return moduleDefinitions;
    }
    return moduleDefinitions.concat(moduleDefinition);
  }
  function bootstrap2(moduleDefinitions) {
    const initializers = moduleDefinitions.reduce(resolveDependencies, []).map(loadModule);
    let initialized = false;
    return function() {
      if (initialized) {
        return;
      }
      initialized = true;
      initializers.forEach((initializer) => initializer());
    };
  }
  this.get = get2;
  this.invoke = invoke;
  this.instantiate = instantiate;
  this.createChild = createChild;
  this.init = bootstrap2(modules);
}
function arrayUnwrap(type, value) {
  if (type !== "value" && isArray4(value)) {
    value = annotate(value.slice());
  }
  return value;
}

// node_modules/diagram-js/lib/draw/DefaultRenderer.js
var DEFAULT_RENDER_PRIORITY2 = 1;
function DefaultRenderer(eventBus, styles) {
  BaseRenderer.call(this, eventBus, DEFAULT_RENDER_PRIORITY2);
  this.CONNECTION_STYLE = styles.style(["no-fill"], { strokeWidth: 5, stroke: "fuchsia" });
  this.SHAPE_STYLE = styles.style({ fill: "white", stroke: "fuchsia", strokeWidth: 2 });
  this.FRAME_STYLE = styles.style(["no-fill"], { stroke: "fuchsia", strokeDasharray: 4, strokeWidth: 2 });
}
e(DefaultRenderer, BaseRenderer);
DefaultRenderer.prototype.canRender = function() {
  return true;
};
DefaultRenderer.prototype.drawShape = function drawShape(visuals, element, attrs) {
  var rect = create("rect");
  attr(rect, {
    x: 0,
    y: 0,
    width: element.width || 0,
    height: element.height || 0
  });
  if (isFrameElement2(element)) {
    attr(rect, assign({}, this.FRAME_STYLE, attrs || {}));
  } else {
    attr(rect, assign({}, this.SHAPE_STYLE, attrs || {}));
  }
  append(visuals, rect);
  return rect;
};
DefaultRenderer.prototype.drawConnection = function drawConnection(visuals, connection, attrs) {
  var line = createLine(connection.waypoints, assign({}, this.CONNECTION_STYLE, attrs || {}));
  append(visuals, line);
  return line;
};
DefaultRenderer.prototype.getShapePath = function getShapePath(shape) {
  var x = shape.x, y = shape.y, width = shape.width, height = shape.height;
  var shapePath = [
    ["M", x, y],
    ["l", width, 0],
    ["l", 0, height],
    ["l", -width, 0],
    ["z"]
  ];
  return componentsToPath(shapePath);
};
DefaultRenderer.prototype.getConnectionPath = function getConnectionPath(connection) {
  var waypoints = connection.waypoints;
  var idx, point, connectionPath = [];
  for (idx = 0; point = waypoints[idx]; idx++) {
    point = point.original || point;
    connectionPath.push([idx === 0 ? "M" : "L", point.x, point.y]);
  }
  return componentsToPath(connectionPath);
};
DefaultRenderer.$inject = ["eventBus", "styles"];

// node_modules/diagram-js/lib/draw/Styles.js
function Styles() {
  var defaultTraits = {
    "no-fill": {
      fill: "none"
    },
    "no-border": {
      strokeOpacity: 0
    },
    "no-events": {
      pointerEvents: "none"
    }
  };
  var self = this;
  this.cls = function(className, traits, additionalAttrs) {
    var attrs = this.style(traits, additionalAttrs);
    return assign(attrs, { "class": className });
  };
  this.style = function(traits, additionalAttrs) {
    if (!isArray(traits) && !additionalAttrs) {
      additionalAttrs = traits;
      traits = [];
    }
    var attrs = reduce(traits, function(attrs2, t) {
      return assign(attrs2, defaultTraits[t] || {});
    }, {});
    return additionalAttrs ? assign(attrs, additionalAttrs) : attrs;
  };
  this.computeStyle = function(custom, traits, defaultStyles) {
    if (!isArray(traits)) {
      defaultStyles = traits;
      traits = [];
    }
    return self.style(traits || [], assign({}, defaultStyles, custom || {}));
  };
}

// node_modules/diagram-js/lib/draw/index.js
var draw_default2 = {
  __init__: ["defaultRenderer"],
  defaultRenderer: ["type", DefaultRenderer],
  styles: ["type", Styles]
};

// node_modules/diagram-js/lib/util/Collections.js
function remove3(collection, element) {
  if (!collection || !element) {
    return -1;
  }
  var idx = collection.indexOf(element);
  if (idx !== -1) {
    collection.splice(idx, 1);
  }
  return idx;
}
function add(collection, element, idx) {
  if (!collection || !element) {
    return;
  }
  if (typeof idx !== "number") {
    idx = -1;
  }
  var currentIdx = collection.indexOf(element);
  if (currentIdx !== -1) {
    if (currentIdx === idx) {
      return;
    } else {
      if (idx !== -1) {
        collection.splice(currentIdx, 1);
      } else {
        return;
      }
    }
  }
  if (idx !== -1) {
    collection.splice(idx, 0, element);
  } else {
    collection.push(element);
  }
}

// node_modules/diagram-js/lib/core/Canvas.js
function round(number, resolution) {
  return Math.round(number * resolution) / resolution;
}
function ensurePx(number) {
  return isNumber(number) ? number + "px" : number;
}
function findRoot(element) {
  while (element.parent) {
    element = element.parent;
  }
  return element;
}
function createContainer(options) {
  options = assign({}, { width: "100%", height: "100%" }, options);
  const container = options.container || document.body;
  const parent = document.createElement("div");
  parent.setAttribute("class", "djs-container djs-parent");
  assign2(parent, {
    position: "relative",
    overflow: "hidden",
    width: ensurePx(options.width),
    height: ensurePx(options.height)
  });
  container.appendChild(parent);
  return parent;
}
function createGroup(parent, cls, childIndex) {
  const group = create("g");
  classes(group).add(cls);
  const index = childIndex !== void 0 ? childIndex : parent.childNodes.length - 1;
  parent.insertBefore(group, parent.childNodes[index] || null);
  return group;
}
var BASE_LAYER = "base";
var PLANE_LAYER_INDEX = 0;
var UTILITY_LAYER_INDEX = 1;
var REQUIRED_MODEL_ATTRS = {
  shape: ["x", "y", "width", "height"],
  connection: ["waypoints"]
};
function Canvas(config, eventBus, graphicsFactory, elementRegistry) {
  this._eventBus = eventBus;
  this._elementRegistry = elementRegistry;
  this._graphicsFactory = graphicsFactory;
  this._rootsIdx = 0;
  this._layers = {};
  this._planes = [];
  this._rootElement = null;
  this._init(config || {});
}
Canvas.$inject = [
  "config.canvas",
  "eventBus",
  "graphicsFactory",
  "elementRegistry"
];
Canvas.prototype._init = function(config) {
  const eventBus = this._eventBus;
  const container = this._container = createContainer(config);
  const svg = this._svg = create("svg");
  attr(svg, { width: "100%", height: "100%" });
  append(container, svg);
  const viewport = this._viewport = createGroup(svg, "viewport");
  if (config.deferUpdate) {
    this._viewboxChanged = debounce(bind(this._viewboxChanged, this), 300);
  }
  eventBus.on("diagram.init", () => {
    eventBus.fire("canvas.init", {
      svg,
      viewport
    });
  });
  eventBus.on([
    "shape.added",
    "connection.added",
    "shape.removed",
    "connection.removed",
    "elements.changed",
    "root.set"
  ], () => {
    delete this._cachedViewbox;
  });
  eventBus.on("diagram.destroy", 500, this._destroy, this);
  eventBus.on("diagram.clear", 500, this._clear, this);
};
Canvas.prototype._destroy = function() {
  this._eventBus.fire("canvas.destroy", {
    svg: this._svg,
    viewport: this._viewport
  });
  const parent = this._container.parentNode;
  if (parent) {
    parent.removeChild(this._container);
  }
  delete this._svg;
  delete this._container;
  delete this._layers;
  delete this._planes;
  delete this._rootElement;
  delete this._viewport;
};
Canvas.prototype._clear = function() {
  const allElements = this._elementRegistry.getAll();
  allElements.forEach((element) => {
    const type = getType(element);
    if (type === "root") {
      this.removeRootElement(element);
    } else {
      this._removeElement(element, type);
    }
  });
  this._planes = [];
  this._rootElement = null;
  delete this._cachedViewbox;
};
Canvas.prototype.getDefaultLayer = function() {
  return this.getLayer(BASE_LAYER, PLANE_LAYER_INDEX);
};
Canvas.prototype.getLayer = function(name2, index) {
  if (!name2) {
    throw new Error("must specify a name");
  }
  let layer = this._layers[name2];
  if (!layer) {
    layer = this._layers[name2] = this._createLayer(name2, index);
  }
  if (typeof index !== "undefined" && layer.index !== index) {
    throw new Error("layer <" + name2 + "> already created at index <" + index + ">");
  }
  return layer.group;
};
Canvas.prototype._getChildIndex = function(index) {
  return reduce(this._layers, function(childIndex, layer) {
    if (layer.visible && index >= layer.index) {
      childIndex++;
    }
    return childIndex;
  }, 0);
};
Canvas.prototype._createLayer = function(name2, index) {
  if (typeof index === "undefined") {
    index = UTILITY_LAYER_INDEX;
  }
  const childIndex = this._getChildIndex(index);
  return {
    group: createGroup(this._viewport, "layer-" + name2, childIndex),
    index,
    visible: true
  };
};
Canvas.prototype.showLayer = function(name2) {
  if (!name2) {
    throw new Error("must specify a name");
  }
  const layer = this._layers[name2];
  if (!layer) {
    throw new Error("layer <" + name2 + "> does not exist");
  }
  const viewport = this._viewport;
  const group = layer.group;
  const index = layer.index;
  if (layer.visible) {
    return group;
  }
  const childIndex = this._getChildIndex(index);
  viewport.insertBefore(group, viewport.childNodes[childIndex] || null);
  layer.visible = true;
  return group;
};
Canvas.prototype.hideLayer = function(name2) {
  if (!name2) {
    throw new Error("must specify a name");
  }
  const layer = this._layers[name2];
  if (!layer) {
    throw new Error("layer <" + name2 + "> does not exist");
  }
  const group = layer.group;
  if (!layer.visible) {
    return group;
  }
  remove(group);
  layer.visible = false;
  return group;
};
Canvas.prototype._removeLayer = function(name2) {
  const layer = this._layers[name2];
  if (layer) {
    delete this._layers[name2];
    remove(layer.group);
  }
};
Canvas.prototype.getActiveLayer = function() {
  const plane = this._findPlaneForRoot(this.getRootElement());
  if (!plane) {
    return null;
  }
  return plane.layer;
};
Canvas.prototype.findRoot = function(element) {
  if (typeof element === "string") {
    element = this._elementRegistry.get(element);
  }
  if (!element) {
    return;
  }
  const plane = this._findPlaneForRoot(
    findRoot(element)
  ) || {};
  return plane.rootElement;
};
Canvas.prototype.getRootElements = function() {
  return this._planes.map(function(plane) {
    return plane.rootElement;
  });
};
Canvas.prototype._findPlaneForRoot = function(rootElement) {
  return find(this._planes, function(plane) {
    return plane.rootElement === rootElement;
  });
};
Canvas.prototype.getContainer = function() {
  return this._container;
};
Canvas.prototype._updateMarker = function(element, marker, add2) {
  let container;
  if (!element.id) {
    element = this._elementRegistry.get(element);
  }
  element.markers = element.markers || /* @__PURE__ */ new Set();
  container = this._elementRegistry._elements[element.id];
  if (!container) {
    return;
  }
  forEach([container.gfx, container.secondaryGfx], function(gfx) {
    if (gfx) {
      if (add2) {
        element.markers.add(marker);
        classes(gfx).add(marker);
      } else {
        element.markers.delete(marker);
        classes(gfx).remove(marker);
      }
    }
  });
  this._eventBus.fire("element.marker.update", { element, gfx: container.gfx, marker, add: !!add2 });
};
Canvas.prototype.addMarker = function(element, marker) {
  this._updateMarker(element, marker, true);
};
Canvas.prototype.removeMarker = function(element, marker) {
  this._updateMarker(element, marker, false);
};
Canvas.prototype.hasMarker = function(element, marker) {
  if (!element.id) {
    element = this._elementRegistry.get(element);
  }
  if (!element.markers) {
    return false;
  }
  return element.markers.has(marker);
};
Canvas.prototype.toggleMarker = function(element, marker) {
  if (this.hasMarker(element, marker)) {
    this.removeMarker(element, marker);
  } else {
    this.addMarker(element, marker);
  }
};
Canvas.prototype.getRootElement = function() {
  const rootElement = this._rootElement;
  if (rootElement || this._planes.length) {
    return rootElement;
  }
  return this.setRootElement(this.addRootElement(null));
};
Canvas.prototype.addRootElement = function(rootElement) {
  const idx = this._rootsIdx++;
  if (!rootElement) {
    rootElement = {
      id: "__implicitroot_" + idx,
      children: [],
      isImplicit: true
    };
  }
  const layerName = rootElement.layer = "root-" + idx;
  this._ensureValid("root", rootElement);
  const layer = this.getLayer(layerName, PLANE_LAYER_INDEX);
  this.hideLayer(layerName);
  this._addRoot(rootElement, layer);
  this._planes.push({
    rootElement,
    layer
  });
  return rootElement;
};
Canvas.prototype.removeRootElement = function(rootElement) {
  if (typeof rootElement === "string") {
    rootElement = this._elementRegistry.get(rootElement);
  }
  const plane = this._findPlaneForRoot(rootElement);
  if (!plane) {
    return;
  }
  this._removeRoot(rootElement);
  this._removeLayer(rootElement.layer);
  this._planes = this._planes.filter(function(plane2) {
    return plane2.rootElement !== rootElement;
  });
  if (this._rootElement === rootElement) {
    this._rootElement = null;
  }
  return rootElement;
};
Canvas.prototype.setRootElement = function(rootElement) {
  if (rootElement === this._rootElement) {
    return rootElement;
  }
  let plane;
  if (!rootElement) {
    throw new Error("rootElement required");
  }
  plane = this._findPlaneForRoot(rootElement);
  if (!plane) {
    rootElement = this.addRootElement(rootElement);
  }
  this._setRoot(rootElement);
  return rootElement;
};
Canvas.prototype._removeRoot = function(element) {
  const elementRegistry = this._elementRegistry, eventBus = this._eventBus;
  eventBus.fire("root.remove", { element });
  eventBus.fire("root.removed", { element });
  elementRegistry.remove(element);
};
Canvas.prototype._addRoot = function(element, gfx) {
  const elementRegistry = this._elementRegistry, eventBus = this._eventBus;
  eventBus.fire("root.add", { element });
  elementRegistry.add(element, gfx);
  eventBus.fire("root.added", { element, gfx });
};
Canvas.prototype._setRoot = function(rootElement, layer) {
  const currentRoot = this._rootElement;
  if (currentRoot) {
    this._elementRegistry.updateGraphics(currentRoot, null, true);
    this.hideLayer(currentRoot.layer);
  }
  if (rootElement) {
    if (!layer) {
      layer = this._findPlaneForRoot(rootElement).layer;
    }
    this._elementRegistry.updateGraphics(rootElement, this._svg, true);
    this.showLayer(rootElement.layer);
  }
  this._rootElement = rootElement;
  this._eventBus.fire("root.set", { element: rootElement });
};
Canvas.prototype._ensureValid = function(type, element) {
  if (!element.id) {
    throw new Error("element must have an id");
  }
  if (this._elementRegistry.get(element.id)) {
    throw new Error("element <" + element.id + "> already exists");
  }
  const requiredAttrs = REQUIRED_MODEL_ATTRS[type];
  const valid = every(requiredAttrs, function(attr3) {
    return typeof element[attr3] !== "undefined";
  });
  if (!valid) {
    throw new Error(
      "must supply { " + requiredAttrs.join(", ") + " } with " + type
    );
  }
};
Canvas.prototype._setParent = function(element, parent, parentIndex) {
  add(parent.children, element, parentIndex);
  element.parent = parent;
};
Canvas.prototype._addElement = function(type, element, parent, parentIndex) {
  parent = parent || this.getRootElement();
  const eventBus = this._eventBus, graphicsFactory = this._graphicsFactory;
  this._ensureValid(type, element);
  eventBus.fire(type + ".add", { element, parent });
  this._setParent(element, parent, parentIndex);
  const gfx = graphicsFactory.create(type, element, parentIndex);
  this._elementRegistry.add(element, gfx);
  graphicsFactory.update(type, element, gfx);
  eventBus.fire(type + ".added", { element, gfx });
  return element;
};
Canvas.prototype.addShape = function(shape, parent, parentIndex) {
  return this._addElement("shape", shape, parent, parentIndex);
};
Canvas.prototype.addConnection = function(connection, parent, parentIndex) {
  return this._addElement("connection", connection, parent, parentIndex);
};
Canvas.prototype._removeElement = function(element, type) {
  const elementRegistry = this._elementRegistry, graphicsFactory = this._graphicsFactory, eventBus = this._eventBus;
  element = elementRegistry.get(element.id || element);
  if (!element) {
    return;
  }
  eventBus.fire(type + ".remove", { element });
  graphicsFactory.remove(element);
  remove3(element.parent && element.parent.children, element);
  element.parent = null;
  eventBus.fire(type + ".removed", { element });
  elementRegistry.remove(element);
  return element;
};
Canvas.prototype.removeShape = function(shape) {
  return this._removeElement(shape, "shape");
};
Canvas.prototype.removeConnection = function(connection) {
  return this._removeElement(connection, "connection");
};
Canvas.prototype.getGraphics = function(element, secondary) {
  return this._elementRegistry.getGraphics(element, secondary);
};
Canvas.prototype._changeViewbox = function(changeFn) {
  this._eventBus.fire("canvas.viewbox.changing");
  changeFn.apply(this);
  this._cachedViewbox = null;
  this._viewboxChanged();
};
Canvas.prototype._viewboxChanged = function() {
  this._eventBus.fire("canvas.viewbox.changed", { viewbox: this.viewbox() });
};
Canvas.prototype.viewbox = function(box) {
  if (box === void 0 && this._cachedViewbox) {
    return structuredClone(this._cachedViewbox);
  }
  const viewport = this._viewport, outerBox = this.getSize();
  let innerBox, matrix, activeLayer, transform3, scale, x, y;
  if (!box) {
    activeLayer = this._rootElement ? this.getActiveLayer() : null;
    innerBox = activeLayer && activeLayer.getBBox() || {};
    transform3 = transform(viewport);
    matrix = transform3 ? transform3.matrix : createMatrix();
    scale = round(matrix.a, 1e3);
    x = round(-matrix.e || 0, 1e3);
    y = round(-matrix.f || 0, 1e3);
    box = this._cachedViewbox = {
      x: x ? x / scale : 0,
      y: y ? y / scale : 0,
      width: outerBox.width / scale,
      height: outerBox.height / scale,
      scale,
      inner: {
        width: innerBox.width || 0,
        height: innerBox.height || 0,
        x: innerBox.x || 0,
        y: innerBox.y || 0
      },
      outer: outerBox
    };
    return box;
  } else {
    this._changeViewbox(function() {
      scale = Math.min(outerBox.width / box.width, outerBox.height / box.height);
      const matrix2 = this._svg.createSVGMatrix().scale(scale).translate(-box.x, -box.y);
      transform(viewport, matrix2);
    });
  }
  return box;
};
Canvas.prototype.scroll = function(delta) {
  const node2 = this._viewport;
  let matrix = node2.getCTM();
  if (delta) {
    this._changeViewbox(function() {
      delta = assign({ dx: 0, dy: 0 }, delta || {});
      matrix = this._svg.createSVGMatrix().translate(delta.dx, delta.dy).multiply(matrix);
      setCTM(node2, matrix);
    });
  }
  return { x: matrix.e, y: matrix.f };
};
Canvas.prototype.scrollToElement = function(element, padding) {
  let defaultPadding = 100;
  if (typeof element === "string") {
    element = this._elementRegistry.get(element);
  }
  const rootElement = this.findRoot(element);
  if (rootElement !== this.getRootElement()) {
    this.setRootElement(rootElement);
  }
  if (rootElement === element) {
    return;
  }
  if (!padding) {
    padding = {};
  }
  if (typeof padding === "number") {
    defaultPadding = padding;
  }
  padding = {
    top: padding.top || defaultPadding,
    right: padding.right || defaultPadding,
    bottom: padding.bottom || defaultPadding,
    left: padding.left || defaultPadding
  };
  const elementBounds = getBBox(element), elementTrbl = asTRBL(elementBounds), viewboxBounds = this.viewbox(), zoom = this.zoom();
  let dx, dy;
  viewboxBounds.y += padding.top / zoom;
  viewboxBounds.x += padding.left / zoom;
  viewboxBounds.width -= (padding.right + padding.left) / zoom;
  viewboxBounds.height -= (padding.bottom + padding.top) / zoom;
  const viewboxTrbl = asTRBL(viewboxBounds);
  const canFit = elementBounds.width < viewboxBounds.width && elementBounds.height < viewboxBounds.height;
  if (!canFit) {
    dx = elementBounds.x - viewboxBounds.x;
    dy = elementBounds.y - viewboxBounds.y;
  } else {
    const dRight = Math.max(0, elementTrbl.right - viewboxTrbl.right), dLeft = Math.min(0, elementTrbl.left - viewboxTrbl.left), dBottom = Math.max(0, elementTrbl.bottom - viewboxTrbl.bottom), dTop = Math.min(0, elementTrbl.top - viewboxTrbl.top);
    dx = dRight || dLeft;
    dy = dBottom || dTop;
  }
  this.scroll({ dx: -dx * zoom, dy: -dy * zoom });
};
Canvas.prototype.zoom = function(newScale, center) {
  if (!newScale) {
    return this.viewbox(newScale).scale;
  }
  if (newScale === "fit-viewport") {
    return this._fitViewport(center);
  }
  let outer, matrix;
  this._changeViewbox(function() {
    if (typeof center !== "object") {
      outer = this.viewbox().outer;
      center = {
        x: outer.width / 2,
        y: outer.height / 2
      };
    }
    matrix = this._setZoom(newScale, center);
  });
  return round(matrix.a, 1e3);
};
function setCTM(node2, m) {
  const mstr = "matrix(" + m.a + "," + m.b + "," + m.c + "," + m.d + "," + m.e + "," + m.f + ")";
  node2.setAttribute("transform", mstr);
}
Canvas.prototype._fitViewport = function(center) {
  const vbox = this.viewbox(), outer = vbox.outer, inner = vbox.inner;
  let newScale, newViewbox;
  if (inner.x >= 0 && inner.y >= 0 && inner.x + inner.width <= outer.width && inner.y + inner.height <= outer.height && !center) {
    newViewbox = {
      x: 0,
      y: 0,
      width: Math.max(inner.width + inner.x, outer.width),
      height: Math.max(inner.height + inner.y, outer.height)
    };
  } else {
    newScale = Math.min(1, outer.width / inner.width, outer.height / inner.height);
    newViewbox = {
      x: inner.x + (center ? inner.width / 2 - outer.width / newScale / 2 : 0),
      y: inner.y + (center ? inner.height / 2 - outer.height / newScale / 2 : 0),
      width: outer.width / newScale,
      height: outer.height / newScale
    };
  }
  this.viewbox(newViewbox);
  return this.viewbox(false).scale;
};
Canvas.prototype._setZoom = function(scale, center) {
  const svg = this._svg, viewport = this._viewport;
  const matrix = svg.createSVGMatrix();
  const point = svg.createSVGPoint();
  let centerPoint, originalPoint, currentMatrix, scaleMatrix, newMatrix;
  currentMatrix = viewport.getCTM();
  const currentScale = currentMatrix.a;
  if (center) {
    centerPoint = assign(point, center);
    originalPoint = centerPoint.matrixTransform(currentMatrix.inverse());
    scaleMatrix = matrix.translate(originalPoint.x, originalPoint.y).scale(1 / currentScale * scale).translate(-originalPoint.x, -originalPoint.y);
    newMatrix = currentMatrix.multiply(scaleMatrix);
  } else {
    newMatrix = matrix.scale(scale);
  }
  setCTM(this._viewport, newMatrix);
  return newMatrix;
};
Canvas.prototype.getSize = function() {
  return {
    width: this._container.clientWidth,
    height: this._container.clientHeight
  };
};
Canvas.prototype.getAbsoluteBBox = function(element) {
  const vbox = this.viewbox();
  let bbox;
  if (element.waypoints) {
    const gfx = this.getGraphics(element);
    bbox = gfx.getBBox();
  } else {
    bbox = element;
  }
  const x = bbox.x * vbox.scale - vbox.x * vbox.scale;
  const y = bbox.y * vbox.scale - vbox.y * vbox.scale;
  const width = bbox.width * vbox.scale;
  const height = bbox.height * vbox.scale;
  return {
    x,
    y,
    width,
    height
  };
};
Canvas.prototype.resized = function() {
  delete this._cachedViewbox;
  this._eventBus.fire("canvas.resized");
};

// node_modules/diagram-js/lib/core/ElementRegistry.js
var ELEMENT_ID = "data-element-id";
function ElementRegistry(eventBus) {
  this._elements = {};
  this._eventBus = eventBus;
}
ElementRegistry.$inject = ["eventBus"];
ElementRegistry.prototype.add = function(element, gfx, secondaryGfx) {
  var id = element.id;
  this._validateId(id);
  attr(gfx, ELEMENT_ID, id);
  if (secondaryGfx) {
    attr(secondaryGfx, ELEMENT_ID, id);
  }
  this._elements[id] = { element, gfx, secondaryGfx };
};
ElementRegistry.prototype.remove = function(element) {
  var elements = this._elements, id = element.id || element, container = id && elements[id];
  if (container) {
    attr(container.gfx, ELEMENT_ID, "");
    if (container.secondaryGfx) {
      attr(container.secondaryGfx, ELEMENT_ID, "");
    }
    delete elements[id];
  }
};
ElementRegistry.prototype.updateId = function(element, newId) {
  this._validateId(newId);
  if (typeof element === "string") {
    element = this.get(element);
  }
  this._eventBus.fire("element.updateId", {
    element,
    newId
  });
  var gfx = this.getGraphics(element), secondaryGfx = this.getGraphics(element, true);
  this.remove(element);
  element.id = newId;
  this.add(element, gfx, secondaryGfx);
};
ElementRegistry.prototype.updateGraphics = function(filter2, gfx, secondary) {
  var id = filter2.id || filter2;
  var container = this._elements[id];
  if (secondary) {
    container.secondaryGfx = gfx;
  } else {
    container.gfx = gfx;
  }
  if (gfx) {
    attr(gfx, ELEMENT_ID, id);
  }
  return gfx;
};
ElementRegistry.prototype.get = function(filter2) {
  var id;
  if (typeof filter2 === "string") {
    id = filter2;
  } else {
    id = filter2 && attr(filter2, ELEMENT_ID);
  }
  var container = this._elements[id];
  return container && container.element;
};
ElementRegistry.prototype.filter = function(fn) {
  var filtered = [];
  this.forEach(function(element, gfx) {
    if (fn(element, gfx)) {
      filtered.push(element);
    }
  });
  return filtered;
};
ElementRegistry.prototype.find = function(fn) {
  var map3 = this._elements, keys = Object.keys(map3);
  for (var i = 0; i < keys.length; i++) {
    var id = keys[i], container = map3[id], element = container.element, gfx = container.gfx;
    if (fn(element, gfx)) {
      return element;
    }
  }
};
ElementRegistry.prototype.getAll = function() {
  return this.filter(function(e2) {
    return e2;
  });
};
ElementRegistry.prototype.forEach = function(fn) {
  var map3 = this._elements;
  Object.keys(map3).forEach(function(id) {
    var container = map3[id], element = container.element, gfx = container.gfx;
    return fn(element, gfx);
  });
};
ElementRegistry.prototype.getGraphics = function(filter2, secondary) {
  var id = filter2.id || filter2;
  var container = this._elements[id];
  return container && (secondary ? container.secondaryGfx : container.gfx);
};
ElementRegistry.prototype._validateId = function(id) {
  if (!id) {
    throw new Error("element must have an id");
  }
  if (this._elements[id]) {
    throw new Error("element with id " + id + " already added");
  }
};

// node_modules/object-refs/dist/index.js
function extend2(collection, refs, property, target) {
  var inverseProperty = property.inverse;
  Object.defineProperty(collection, "remove", {
    value: function(element) {
      var idx = this.indexOf(element);
      if (idx !== -1) {
        this.splice(idx, 1);
        refs.unset(element, inverseProperty, target);
      }
      return element;
    }
  });
  Object.defineProperty(collection, "contains", {
    value: function(element) {
      return this.indexOf(element) !== -1;
    }
  });
  Object.defineProperty(collection, "add", {
    value: function(element, idx) {
      var currentIdx = this.indexOf(element);
      if (typeof idx === "undefined") {
        if (currentIdx !== -1) {
          return;
        }
        idx = this.length;
      }
      if (currentIdx !== -1) {
        this.splice(currentIdx, 1);
      }
      this.splice(idx, 0, element);
      if (currentIdx === -1) {
        refs.set(element, inverseProperty, target);
      }
    }
  });
  Object.defineProperty(collection, "__refs_collection", {
    value: true
  });
  return collection;
}
function isExtended(collection) {
  return collection.__refs_collection === true;
}
function hasOwnProperty(e2, property) {
  return Object.prototype.hasOwnProperty.call(e2, property.name || property);
}
function defineCollectionProperty(ref, property, target) {
  var collection = extend2(target[property.name] || [], ref, property, target);
  Object.defineProperty(target, property.name, {
    enumerable: property.enumerable,
    value: collection
  });
  if (collection.length) {
    collection.forEach(function(o) {
      ref.set(o, property.inverse, target);
    });
  }
}
function defineProperty(ref, property, target) {
  var inverseProperty = property.inverse;
  var _value = target[property.name];
  Object.defineProperty(target, property.name, {
    configurable: property.configurable,
    enumerable: property.enumerable,
    get: function() {
      return _value;
    },
    set: function(value) {
      if (value === _value) {
        return;
      }
      var old = _value;
      _value = null;
      if (old) {
        ref.unset(old, inverseProperty, target);
      }
      _value = value;
      ref.set(_value, inverseProperty, target);
    }
  });
}
function Refs(a, b) {
  if (!(this instanceof Refs)) {
    return new Refs(a, b);
  }
  a.inverse = b;
  b.inverse = a;
  this.props = {};
  this.props[a.name] = a;
  this.props[b.name] = b;
}
Refs.prototype.bind = function(target, property) {
  if (typeof property === "string") {
    if (!this.props[property]) {
      throw new Error("no property <" + property + "> in ref");
    }
    property = this.props[property];
  }
  if (property.collection) {
    defineCollectionProperty(this, property, target);
  } else {
    defineProperty(this, property, target);
  }
};
Refs.prototype.ensureRefsCollection = function(target, property) {
  var collection = target[property.name];
  if (!isExtended(collection)) {
    defineCollectionProperty(this, property, target);
  }
  return collection;
};
Refs.prototype.ensureBound = function(target, property) {
  if (!hasOwnProperty(target, property)) {
    this.bind(target, property);
  }
};
Refs.prototype.unset = function(target, property, value) {
  if (target) {
    this.ensureBound(target, property);
    if (property.collection) {
      this.ensureRefsCollection(target, property).remove(value);
    } else {
      target[property.name] = void 0;
    }
  }
};
Refs.prototype.set = function(target, property, value) {
  if (target) {
    this.ensureBound(target, property);
    if (property.collection) {
      this.ensureRefsCollection(target, property).add(value);
    } else {
      target[property.name] = value;
    }
  }
};

// node_modules/diagram-js/lib/model/index.js
var parentRefs = new Refs({ name: "children", enumerable: true, collection: true }, { name: "parent" });
var labelRefs = new Refs({ name: "labels", enumerable: true, collection: true }, { name: "labelTarget" });
var attacherRefs = new Refs({ name: "attachers", collection: true }, { name: "host" });
var outgoingRefs = new Refs({ name: "outgoing", collection: true }, { name: "source" });
var incomingRefs = new Refs({ name: "incoming", collection: true }, { name: "target" });
function ElementImpl() {
  Object.defineProperty(this, "businessObject", {
    writable: true
  });
  Object.defineProperty(this, "label", {
    get: function() {
      return this.labels[0];
    },
    set: function(newLabel) {
      var label = this.label, labels = this.labels;
      if (!newLabel && label) {
        labels.remove(label);
      } else {
        labels.add(newLabel, 0);
      }
    }
  });
  parentRefs.bind(this, "parent");
  labelRefs.bind(this, "labels");
  outgoingRefs.bind(this, "outgoing");
  incomingRefs.bind(this, "incoming");
}
function ShapeImpl() {
  ElementImpl.call(this);
  parentRefs.bind(this, "children");
  attacherRefs.bind(this, "host");
  attacherRefs.bind(this, "attachers");
}
e(ShapeImpl, ElementImpl);
function RootImpl() {
  ElementImpl.call(this);
  parentRefs.bind(this, "children");
}
e(RootImpl, ShapeImpl);
function LabelImpl() {
  ShapeImpl.call(this);
  labelRefs.bind(this, "labelTarget");
}
e(LabelImpl, ShapeImpl);
function ConnectionImpl() {
  ElementImpl.call(this);
  outgoingRefs.bind(this, "source");
  incomingRefs.bind(this, "target");
}
e(ConnectionImpl, ElementImpl);
var types = {
  connection: ConnectionImpl,
  shape: ShapeImpl,
  label: LabelImpl,
  root: RootImpl
};
function create2(type, attrs) {
  var Type = types[type];
  if (!Type) {
    throw new Error("unknown type: <" + type + ">");
  }
  return assign(new Type(), attrs);
}

// node_modules/diagram-js/lib/core/ElementFactory.js
function ElementFactory() {
  this._uid = 12;
}
ElementFactory.prototype.createRoot = function(attrs) {
  return this.create("root", attrs);
};
ElementFactory.prototype.createLabel = function(attrs) {
  return this.create("label", attrs);
};
ElementFactory.prototype.createShape = function(attrs) {
  return this.create("shape", attrs);
};
ElementFactory.prototype.createConnection = function(attrs) {
  return this.create("connection", attrs);
};
ElementFactory.prototype.create = function(type, attrs) {
  attrs = assign({}, attrs || {});
  if (!attrs.id) {
    attrs.id = type + "_" + this._uid++;
  }
  return create2(type, attrs);
};

// node_modules/diagram-js/lib/core/EventBus.js
var FN_REF = "__fn";
var DEFAULT_PRIORITY3 = 1e3;
var slice2 = Array.prototype.slice;
function EventBus() {
  this._listeners = {};
  this.on("diagram.destroy", 1, this._destroy, this);
}
EventBus.prototype.on = function(events, priority, callback, that) {
  events = isArray(events) ? events : [events];
  if (isFunction(priority)) {
    that = callback;
    callback = priority;
    priority = DEFAULT_PRIORITY3;
  }
  if (!isNumber(priority)) {
    throw new Error("priority must be a number");
  }
  var actualCallback = callback;
  if (that) {
    actualCallback = bind(callback, that);
    actualCallback[FN_REF] = callback[FN_REF] || callback;
  }
  var self = this;
  events.forEach(function(e2) {
    self._addListener(e2, {
      priority,
      callback: actualCallback,
      next: null
    });
  });
};
EventBus.prototype.once = function(events, priority, callback, that) {
  var self = this;
  if (isFunction(priority)) {
    that = callback;
    callback = priority;
    priority = DEFAULT_PRIORITY3;
  }
  if (!isNumber(priority)) {
    throw new Error("priority must be a number");
  }
  function wrappedCallback() {
    wrappedCallback.__isTomb = true;
    var result = callback.apply(that, arguments);
    self.off(events, wrappedCallback);
    return result;
  }
  wrappedCallback[FN_REF] = callback;
  this.on(events, priority, wrappedCallback);
};
EventBus.prototype.off = function(events, callback) {
  events = isArray(events) ? events : [events];
  var self = this;
  events.forEach(function(event2) {
    self._removeListener(event2, callback);
  });
};
EventBus.prototype.createEvent = function(data) {
  var event2 = new InternalEvent();
  event2.init(data);
  return event2;
};
EventBus.prototype.fire = function(type, data) {
  var event2, firstListener, returnValue, args;
  args = slice2.call(arguments);
  if (typeof type === "object") {
    data = type;
    type = data.type;
  }
  if (!type) {
    throw new Error("no event type specified");
  }
  firstListener = this._listeners[type];
  if (!firstListener) {
    return;
  }
  if (data instanceof InternalEvent) {
    event2 = data;
  } else {
    event2 = this.createEvent(data);
  }
  args[0] = event2;
  var originalType = event2.type;
  if (type !== originalType) {
    event2.type = type;
  }
  try {
    returnValue = this._invokeListeners(event2, args, firstListener);
  } finally {
    if (type !== originalType) {
      event2.type = originalType;
    }
  }
  if (returnValue === void 0 && event2.defaultPrevented) {
    returnValue = false;
  }
  return returnValue;
};
EventBus.prototype.handleError = function(error3) {
  return this.fire("error", { error: error3 }) === false;
};
EventBus.prototype._destroy = function() {
  this._listeners = {};
};
EventBus.prototype._invokeListeners = function(event2, args, listener) {
  var returnValue;
  while (listener) {
    if (event2.cancelBubble) {
      break;
    }
    returnValue = this._invokeListener(event2, args, listener);
    listener = listener.next;
  }
  return returnValue;
};
EventBus.prototype._invokeListener = function(event2, args, listener) {
  var returnValue;
  if (listener.callback.__isTomb) {
    return returnValue;
  }
  try {
    returnValue = invokeFunction(listener.callback, args);
    if (returnValue !== void 0) {
      event2.returnValue = returnValue;
      event2.stopPropagation();
    }
    if (returnValue === false) {
      event2.preventDefault();
    }
  } catch (error3) {
    if (!this.handleError(error3)) {
      console.error("unhandled error in event listener", error3);
      throw error3;
    }
  }
  return returnValue;
};
EventBus.prototype._addListener = function(event2, newListener) {
  var listener = this._getListeners(event2), previousListener;
  if (!listener) {
    this._setListeners(event2, newListener);
    return;
  }
  while (listener) {
    if (listener.priority < newListener.priority) {
      newListener.next = listener;
      if (previousListener) {
        previousListener.next = newListener;
      } else {
        this._setListeners(event2, newListener);
      }
      return;
    }
    previousListener = listener;
    listener = listener.next;
  }
  previousListener.next = newListener;
};
EventBus.prototype._getListeners = function(name2) {
  return this._listeners[name2];
};
EventBus.prototype._setListeners = function(name2, listener) {
  this._listeners[name2] = listener;
};
EventBus.prototype._removeListener = function(event2, callback) {
  var listener = this._getListeners(event2), nextListener, previousListener, listenerCallback;
  if (!callback) {
    this._setListeners(event2, null);
    return;
  }
  while (listener) {
    nextListener = listener.next;
    listenerCallback = listener.callback;
    if (listenerCallback === callback || listenerCallback[FN_REF] === callback) {
      if (previousListener) {
        previousListener.next = nextListener;
      } else {
        this._setListeners(event2, nextListener);
      }
    }
    previousListener = listener;
    listener = nextListener;
  }
};
function InternalEvent() {
}
InternalEvent.prototype.stopPropagation = function() {
  this.cancelBubble = true;
};
InternalEvent.prototype.preventDefault = function() {
  this.defaultPrevented = true;
};
InternalEvent.prototype.init = function(data) {
  assign(this, data || {});
};
function invokeFunction(fn, args) {
  return fn.apply(null, args);
}

// node_modules/diagram-js/lib/util/GraphicsUtil.js
function getVisual(gfx) {
  return gfx.childNodes[0];
}
function getChildren(gfx) {
  return gfx.parentNode.childNodes[1];
}

// node_modules/diagram-js/lib/core/GraphicsFactory.js
function GraphicsFactory(eventBus, elementRegistry) {
  this._eventBus = eventBus;
  this._elementRegistry = elementRegistry;
}
GraphicsFactory.$inject = ["eventBus", "elementRegistry"];
GraphicsFactory.prototype._getChildrenContainer = function(element) {
  var gfx = this._elementRegistry.getGraphics(element);
  var childrenGfx;
  if (!element.parent) {
    childrenGfx = gfx;
  } else {
    childrenGfx = getChildren(gfx);
    if (!childrenGfx) {
      childrenGfx = create("g");
      classes(childrenGfx).add("djs-children");
      append(gfx.parentNode, childrenGfx);
    }
  }
  return childrenGfx;
};
GraphicsFactory.prototype._clear = function(gfx) {
  var visual = getVisual(gfx);
  clear2(visual);
  return visual;
};
GraphicsFactory.prototype._createContainer = function(type, childrenGfx, parentIndex, isFrame) {
  var outerGfx = create("g");
  classes(outerGfx).add("djs-group");
  if (typeof parentIndex !== "undefined") {
    prependTo(outerGfx, childrenGfx, childrenGfx.childNodes[parentIndex]);
  } else {
    append(childrenGfx, outerGfx);
  }
  var gfx = create("g");
  classes(gfx).add("djs-element");
  classes(gfx).add("djs-" + type);
  if (isFrame) {
    classes(gfx).add("djs-frame");
  }
  append(outerGfx, gfx);
  var visual = create("g");
  classes(visual).add("djs-visual");
  append(gfx, visual);
  return gfx;
};
GraphicsFactory.prototype.create = function(type, element, parentIndex) {
  var childrenGfx = this._getChildrenContainer(element.parent);
  return this._createContainer(type, childrenGfx, parentIndex, isFrameElement2(element));
};
GraphicsFactory.prototype.updateContainments = function(elements) {
  var self = this, elementRegistry = this._elementRegistry, parents;
  parents = reduce(elements, function(map3, e2) {
    if (e2.parent) {
      map3[e2.parent.id] = e2.parent;
    }
    return map3;
  }, {});
  forEach(parents, function(parent) {
    var children = parent.children;
    if (!children) {
      return;
    }
    var childrenGfx = self._getChildrenContainer(parent);
    forEach(children.slice().reverse(), function(child) {
      var childGfx = elementRegistry.getGraphics(child);
      prependTo(childGfx.parentNode, childrenGfx);
    });
  });
};
GraphicsFactory.prototype.drawShape = function(visual, element, attrs = {}) {
  var eventBus = this._eventBus;
  return eventBus.fire("render.shape", { gfx: visual, element, attrs });
};
GraphicsFactory.prototype.getShapePath = function(element) {
  var eventBus = this._eventBus;
  return eventBus.fire("render.getShapePath", element);
};
GraphicsFactory.prototype.drawConnection = function(visual, element, attrs = {}) {
  var eventBus = this._eventBus;
  return eventBus.fire("render.connection", { gfx: visual, element, attrs });
};
GraphicsFactory.prototype.getConnectionPath = function(connection) {
  var eventBus = this._eventBus;
  return eventBus.fire("render.getConnectionPath", connection);
};
GraphicsFactory.prototype.update = function(type, element, gfx) {
  if (!element.parent) {
    return;
  }
  var visual = this._clear(gfx);
  if (type === "shape") {
    this.drawShape(visual, element);
    translate(gfx, element.x, element.y);
  } else if (type === "connection") {
    this.drawConnection(visual, element);
  } else {
    throw new Error("unknown type: " + type);
  }
  if (element.hidden) {
    attr(gfx, "display", "none");
  } else {
    attr(gfx, "display", "block");
  }
};
GraphicsFactory.prototype.remove = function(element) {
  var gfx = this._elementRegistry.getGraphics(element);
  remove(gfx.parentNode);
};
function prependTo(newNode, parentNode, siblingNode) {
  var node2 = siblingNode || parentNode.firstChild;
  if (newNode === node2) {
    return;
  }
  parentNode.insertBefore(newNode, node2);
}

// node_modules/diagram-js/lib/core/index.js
var core_default2 = {
  __depends__: [draw_default2],
  __init__: ["canvas"],
  canvas: ["type", Canvas],
  elementRegistry: ["type", ElementRegistry],
  elementFactory: ["type", ElementFactory],
  eventBus: ["type", EventBus],
  graphicsFactory: ["type", GraphicsFactory]
};

// node_modules/diagram-js/lib/Diagram.js
function bootstrap(modules) {
  var injector = new Injector(modules);
  injector.init();
  return injector;
}
function createInjector(options) {
  options = options || {};
  var configModule = {
    "config": ["value", options]
  };
  var modules = [configModule, core_default2].concat(options.modules || []);
  return bootstrap(modules);
}
function Diagram(options, injector) {
  this._injector = injector || createInjector(options);
  this.get("eventBus").fire("diagram.init");
}
Diagram.prototype.get = function(name2, strict) {
  return this._injector.get(name2, strict);
};
Diagram.prototype.invoke = function(func, context, locals) {
  return this._injector.invoke(func, context, locals);
};
Diagram.prototype.destroy = function() {
  this.get("eventBus").fire("diagram.destroy");
};
Diagram.prototype.clear = function() {
  this.get("eventBus").fire("diagram.clear");
};

// node_modules/moddle/dist/index.esm.js
function Base() {
}
Base.prototype.get = function(name2) {
  return this.$model.properties.get(this, name2);
};
Base.prototype.set = function(name2, value) {
  this.$model.properties.set(this, name2, value);
};
function Factory(model, properties) {
  this.model = model;
  this.properties = properties;
}
Factory.prototype.createType = function(descriptor) {
  var model = this.model;
  var props = this.properties, prototype = Object.create(Base.prototype);
  forEach(descriptor.properties, function(p) {
    if (!p.isMany && p.default !== void 0) {
      prototype[p.name] = p.default;
    }
  });
  props.defineModel(prototype, model);
  props.defineDescriptor(prototype, descriptor);
  var name2 = descriptor.ns.name;
  function ModdleElement(attrs) {
    props.define(this, "$type", { value: name2, enumerable: true });
    props.define(this, "$attrs", { value: {} });
    props.define(this, "$parent", { writable: true });
    forEach(attrs, bind(function(val, key) {
      this.set(key, val);
    }, this));
  }
  ModdleElement.prototype = prototype;
  ModdleElement.hasType = prototype.$instanceOf = this.model.hasType;
  props.defineModel(ModdleElement, model);
  props.defineDescriptor(ModdleElement, descriptor);
  return ModdleElement;
};
var BUILTINS = {
  String: true,
  Boolean: true,
  Integer: true,
  Real: true,
  Element: true
};
var TYPE_CONVERTERS = {
  String: function(s) {
    return s;
  },
  Boolean: function(s) {
    return s === "true";
  },
  Integer: function(s) {
    return parseInt(s, 10);
  },
  Real: function(s) {
    return parseFloat(s);
  }
};
function coerceType(type, value) {
  var converter = TYPE_CONVERTERS[type];
  if (converter) {
    return converter(value);
  } else {
    return value;
  }
}
function isBuiltIn(type) {
  return !!BUILTINS[type];
}
function isSimple(type) {
  return !!TYPE_CONVERTERS[type];
}
function parseName(name2, defaultPrefix) {
  var parts = name2.split(/:/), localName, prefix3;
  if (parts.length === 1) {
    localName = name2;
    prefix3 = defaultPrefix;
  } else if (parts.length === 2) {
    localName = parts[1];
    prefix3 = parts[0];
  } else {
    throw new Error("expected <prefix:localName> or <localName>, got " + name2);
  }
  name2 = (prefix3 ? prefix3 + ":" : "") + localName;
  return {
    name: name2,
    prefix: prefix3,
    localName
  };
}
function DescriptorBuilder(nameNs) {
  this.ns = nameNs;
  this.name = nameNs.name;
  this.allTypes = [];
  this.allTypesByName = {};
  this.properties = [];
  this.propertiesByName = {};
}
DescriptorBuilder.prototype.build = function() {
  return pick(this, [
    "ns",
    "name",
    "allTypes",
    "allTypesByName",
    "properties",
    "propertiesByName",
    "bodyProperty",
    "idProperty"
  ]);
};
DescriptorBuilder.prototype.addProperty = function(p, idx, validate) {
  if (typeof idx === "boolean") {
    validate = idx;
    idx = void 0;
  }
  this.addNamedProperty(p, validate !== false);
  var properties = this.properties;
  if (idx !== void 0) {
    properties.splice(idx, 0, p);
  } else {
    properties.push(p);
  }
};
DescriptorBuilder.prototype.replaceProperty = function(oldProperty, newProperty, replace) {
  var oldNameNs = oldProperty.ns;
  var props = this.properties, propertiesByName = this.propertiesByName, rename = oldProperty.name !== newProperty.name;
  if (oldProperty.isId) {
    if (!newProperty.isId) {
      throw new Error(
        "property <" + newProperty.ns.name + "> must be id property to refine <" + oldProperty.ns.name + ">"
      );
    }
    this.setIdProperty(newProperty, false);
  }
  if (oldProperty.isBody) {
    if (!newProperty.isBody) {
      throw new Error(
        "property <" + newProperty.ns.name + "> must be body property to refine <" + oldProperty.ns.name + ">"
      );
    }
    this.setBodyProperty(newProperty, false);
  }
  var idx = props.indexOf(oldProperty);
  if (idx === -1) {
    throw new Error("property <" + oldNameNs.name + "> not found in property list");
  }
  props.splice(idx, 1);
  this.addProperty(newProperty, replace ? void 0 : idx, rename);
  propertiesByName[oldNameNs.name] = propertiesByName[oldNameNs.localName] = newProperty;
};
DescriptorBuilder.prototype.redefineProperty = function(p, targetPropertyName, replace) {
  var nsPrefix = p.ns.prefix;
  var parts = targetPropertyName.split("#");
  var name2 = parseName(parts[0], nsPrefix);
  var attrName = parseName(parts[1], name2.prefix).name;
  var redefinedProperty = this.propertiesByName[attrName];
  if (!redefinedProperty) {
    throw new Error("refined property <" + attrName + "> not found");
  } else {
    this.replaceProperty(redefinedProperty, p, replace);
  }
  delete p.redefines;
};
DescriptorBuilder.prototype.addNamedProperty = function(p, validate) {
  var ns2 = p.ns, propsByName = this.propertiesByName;
  if (validate) {
    this.assertNotDefined(p, ns2.name);
    this.assertNotDefined(p, ns2.localName);
  }
  propsByName[ns2.name] = propsByName[ns2.localName] = p;
};
DescriptorBuilder.prototype.removeNamedProperty = function(p) {
  var ns2 = p.ns, propsByName = this.propertiesByName;
  delete propsByName[ns2.name];
  delete propsByName[ns2.localName];
};
DescriptorBuilder.prototype.setBodyProperty = function(p, validate) {
  if (validate && this.bodyProperty) {
    throw new Error(
      "body property defined multiple times (<" + this.bodyProperty.ns.name + ">, <" + p.ns.name + ">)"
    );
  }
  this.bodyProperty = p;
};
DescriptorBuilder.prototype.setIdProperty = function(p, validate) {
  if (validate && this.idProperty) {
    throw new Error(
      "id property defined multiple times (<" + this.idProperty.ns.name + ">, <" + p.ns.name + ">)"
    );
  }
  this.idProperty = p;
};
DescriptorBuilder.prototype.assertNotTrait = function(typeDescriptor) {
  const _extends = typeDescriptor.extends || [];
  if (_extends.length) {
    throw new Error(
      `cannot create <${typeDescriptor.name}> extending <${typeDescriptor.extends}>`
    );
  }
};
DescriptorBuilder.prototype.assertNotDefined = function(p, name2) {
  var propertyName = p.name, definedProperty = this.propertiesByName[propertyName];
  if (definedProperty) {
    throw new Error(
      "property <" + propertyName + "> already defined; override of <" + definedProperty.definedBy.ns.name + "#" + definedProperty.ns.name + "> by <" + p.definedBy.ns.name + "#" + p.ns.name + "> not allowed without redefines"
    );
  }
};
DescriptorBuilder.prototype.hasProperty = function(name2) {
  return this.propertiesByName[name2];
};
DescriptorBuilder.prototype.addTrait = function(t, inherited) {
  if (inherited) {
    this.assertNotTrait(t);
  }
  var typesByName = this.allTypesByName, types3 = this.allTypes;
  var typeName = t.name;
  if (typeName in typesByName) {
    return;
  }
  forEach(t.properties, bind(function(p) {
    p = assign({}, p, {
      name: p.ns.localName,
      inherited
    });
    Object.defineProperty(p, "definedBy", {
      value: t
    });
    var replaces = p.replaces, redefines = p.redefines;
    if (replaces || redefines) {
      this.redefineProperty(p, replaces || redefines, replaces);
    } else {
      if (p.isBody) {
        this.setBodyProperty(p);
      }
      if (p.isId) {
        this.setIdProperty(p);
      }
      this.addProperty(p);
    }
  }, this));
  types3.push(t);
  typesByName[typeName] = t;
};
function Registry(packages2, properties) {
  this.packageMap = {};
  this.typeMap = {};
  this.packages = [];
  this.properties = properties;
  forEach(packages2, bind(this.registerPackage, this));
}
Registry.prototype.getPackage = function(uriOrPrefix) {
  return this.packageMap[uriOrPrefix];
};
Registry.prototype.getPackages = function() {
  return this.packages;
};
Registry.prototype.registerPackage = function(pkg) {
  pkg = assign({}, pkg);
  var pkgMap = this.packageMap;
  ensureAvailable(pkgMap, pkg, "prefix");
  ensureAvailable(pkgMap, pkg, "uri");
  forEach(pkg.types, bind(function(descriptor) {
    this.registerType(descriptor, pkg);
  }, this));
  pkgMap[pkg.uri] = pkgMap[pkg.prefix] = pkg;
  this.packages.push(pkg);
};
Registry.prototype.registerType = function(type, pkg) {
  type = assign({}, type, {
    superClass: (type.superClass || []).slice(),
    extends: (type.extends || []).slice(),
    properties: (type.properties || []).slice(),
    meta: assign(type.meta || {})
  });
  var ns2 = parseName(type.name, pkg.prefix), name2 = ns2.name, propertiesByName = {};
  forEach(type.properties, bind(function(p) {
    var propertyNs = parseName(p.name, ns2.prefix), propertyName = propertyNs.name;
    if (!isBuiltIn(p.type)) {
      p.type = parseName(p.type, propertyNs.prefix).name;
    }
    assign(p, {
      ns: propertyNs,
      name: propertyName
    });
    propertiesByName[propertyName] = p;
  }, this));
  assign(type, {
    ns: ns2,
    name: name2,
    propertiesByName
  });
  forEach(type.extends, bind(function(extendsName) {
    var extendsNameNs = parseName(extendsName, ns2.prefix);
    var extended = this.typeMap[extendsNameNs.name];
    extended.traits = extended.traits || [];
    extended.traits.push(name2);
  }, this));
  this.definePackage(type, pkg);
  this.typeMap[name2] = type;
};
Registry.prototype.mapTypes = function(nsName2, iterator, trait) {
  var type = isBuiltIn(nsName2.name) ? { name: nsName2.name } : this.typeMap[nsName2.name];
  var self = this;
  function traverse(cls, trait2) {
    var parentNs = parseName(cls, isBuiltIn(cls) ? "" : nsName2.prefix);
    self.mapTypes(parentNs, iterator, trait2);
  }
  function traverseTrait(cls) {
    return traverse(cls, true);
  }
  function traverseSuper(cls) {
    return traverse(cls, false);
  }
  if (!type) {
    throw new Error("unknown type <" + nsName2.name + ">");
  }
  forEach(type.superClass, trait ? traverseTrait : traverseSuper);
  iterator(type, !trait);
  forEach(type.traits, traverseTrait);
};
Registry.prototype.getEffectiveDescriptor = function(name2) {
  var nsName2 = parseName(name2);
  var builder = new DescriptorBuilder(nsName2);
  this.mapTypes(nsName2, function(type, inherited) {
    builder.addTrait(type, inherited);
  });
  var descriptor = builder.build();
  this.definePackage(descriptor, descriptor.allTypes[descriptor.allTypes.length - 1].$pkg);
  return descriptor;
};
Registry.prototype.definePackage = function(target, pkg) {
  this.properties.define(target, "$pkg", { value: pkg });
};
function ensureAvailable(packageMap, pkg, identifierKey) {
  var value = pkg[identifierKey];
  if (value in packageMap) {
    throw new Error("package with " + identifierKey + " <" + value + "> already defined");
  }
}
function Properties(model) {
  this.model = model;
}
Properties.prototype.set = function(target, name2, value) {
  if (!isString(name2) || !name2.length) {
    throw new TypeError("property name must be a non-empty string");
  }
  var property = this.getProperty(target, name2);
  var propertyName = property && property.name;
  if (isUndefined3(value)) {
    if (property) {
      delete target[propertyName];
    } else {
      delete target.$attrs[stripGlobal(name2)];
    }
  } else {
    if (property) {
      if (propertyName in target) {
        target[propertyName] = value;
      } else {
        defineProperty2(target, property, value);
      }
    } else {
      target.$attrs[stripGlobal(name2)] = value;
    }
  }
};
Properties.prototype.get = function(target, name2) {
  var property = this.getProperty(target, name2);
  if (!property) {
    return target.$attrs[stripGlobal(name2)];
  }
  var propertyName = property.name;
  if (!target[propertyName] && property.isMany) {
    defineProperty2(target, property, []);
  }
  return target[propertyName];
};
Properties.prototype.define = function(target, name2, options) {
  if (!options.writable) {
    var value = options.value;
    options = assign({}, options, {
      get: function() {
        return value;
      }
    });
    delete options.value;
  }
  Object.defineProperty(target, name2, options);
};
Properties.prototype.defineDescriptor = function(target, descriptor) {
  this.define(target, "$descriptor", { value: descriptor });
};
Properties.prototype.defineModel = function(target, model) {
  this.define(target, "$model", { value: model });
};
Properties.prototype.getProperty = function(target, name2) {
  var model = this.model;
  var property = model.getPropertyDescriptor(target, name2);
  if (property) {
    return property;
  }
  if (name2.includes(":")) {
    return null;
  }
  const strict = model.config.strict;
  if (typeof strict !== "undefined") {
    const error3 = new TypeError(`unknown property <${name2}> on <${target.$type}>`);
    if (strict) {
      throw error3;
    } else {
      typeof console !== "undefined" && console.warn(error3);
    }
  }
  return null;
};
function isUndefined3(val) {
  return typeof val === "undefined";
}
function defineProperty2(target, property, value) {
  Object.defineProperty(target, property.name, {
    enumerable: !property.isReference,
    writable: true,
    value,
    configurable: true
  });
}
function stripGlobal(name2) {
  return name2.replace(/^:/, "");
}
function Moddle(packages2, config = {}) {
  this.properties = new Properties(this);
  this.factory = new Factory(this, this.properties);
  this.registry = new Registry(packages2, this.properties);
  this.typeCache = {};
  this.config = config;
}
Moddle.prototype.create = function(descriptor, attrs) {
  var Type = this.getType(descriptor);
  if (!Type) {
    throw new Error("unknown type <" + descriptor + ">");
  }
  return new Type(attrs);
};
Moddle.prototype.getType = function(descriptor) {
  var cache = this.typeCache;
  var name2 = isString(descriptor) ? descriptor : descriptor.ns.name;
  var type = cache[name2];
  if (!type) {
    descriptor = this.registry.getEffectiveDescriptor(name2);
    type = cache[name2] = this.factory.createType(descriptor);
  }
  return type;
};
Moddle.prototype.createAny = function(name2, nsUri, properties) {
  var nameNs = parseName(name2);
  var element = {
    $type: name2,
    $instanceOf: function(type) {
      return type === this.$type;
    },
    get: function(key) {
      return this[key];
    },
    set: function(key, value) {
      set(this, [key], value);
    }
  };
  var descriptor = {
    name: name2,
    isGeneric: true,
    ns: {
      prefix: nameNs.prefix,
      localName: nameNs.localName,
      uri: nsUri
    }
  };
  this.properties.defineDescriptor(element, descriptor);
  this.properties.defineModel(element, this);
  this.properties.define(element, "get", { enumerable: false, writable: true });
  this.properties.define(element, "set", { enumerable: false, writable: true });
  this.properties.define(element, "$parent", { enumerable: false, writable: true });
  this.properties.define(element, "$instanceOf", { enumerable: false, writable: true });
  forEach(properties, function(a, key) {
    if (isObject(a) && a.value !== void 0) {
      element[a.name] = a.value;
    } else {
      element[key] = a;
    }
  });
  return element;
};
Moddle.prototype.getPackage = function(uriOrPrefix) {
  return this.registry.getPackage(uriOrPrefix);
};
Moddle.prototype.getPackages = function() {
  return this.registry.getPackages();
};
Moddle.prototype.getElementDescriptor = function(element) {
  return element.$descriptor;
};
Moddle.prototype.hasType = function(element, type) {
  if (type === void 0) {
    type = element;
    element = this;
  }
  var descriptor = element.$model.getElementDescriptor(element);
  return type in descriptor.allTypesByName;
};
Moddle.prototype.getPropertyDescriptor = function(element, property) {
  return this.getElementDescriptor(element).propertiesByName[property];
};
Moddle.prototype.getTypeDescriptor = function(type) {
  return this.registry.typeMap[type];
};

// node_modules/saxen/dist/index.esm.js
var fromCharCode = String.fromCharCode;
var hasOwnProperty2 = Object.prototype.hasOwnProperty;
var ENTITY_PATTERN = /&#(\d+);|&#x([0-9a-f]+);|&(\w+);/ig;
var ENTITY_MAPPING = {
  "amp": "&",
  "apos": "'",
  "gt": ">",
  "lt": "<",
  "quot": '"'
};
Object.keys(ENTITY_MAPPING).forEach(function(k) {
  ENTITY_MAPPING[k.toUpperCase()] = ENTITY_MAPPING[k];
});
function replaceEntities(_, d, x, z) {
  if (z) {
    if (hasOwnProperty2.call(ENTITY_MAPPING, z)) {
      return ENTITY_MAPPING[z];
    } else {
      return "&" + z + ";";
    }
  }
  if (d) {
    return fromCharCode(d);
  }
  return fromCharCode(parseInt(x, 16));
}
function decodeEntities(s) {
  if (s.length > 3 && s.indexOf("&") !== -1) {
    return s.replace(ENTITY_PATTERN, replaceEntities);
  }
  return s;
}
var XSI_URI = "http://www.w3.org/2001/XMLSchema-instance";
var XSI_PREFIX = "xsi";
var XSI_TYPE = "xsi:type";
var NON_WHITESPACE_OUTSIDE_ROOT_NODE = "non-whitespace outside of root node";
function error(msg) {
  return new Error(msg);
}
function missingNamespaceForPrefix(prefix3) {
  return "missing namespace for prefix <" + prefix3 + ">";
}
function getter(getFn) {
  return {
    "get": getFn,
    "enumerable": true
  };
}
function cloneNsMatrix(nsMatrix) {
  var clone = {}, key;
  for (key in nsMatrix) {
    clone[key] = nsMatrix[key];
  }
  return clone;
}
function uriPrefix(prefix3) {
  return prefix3 + "$uri";
}
function buildNsMatrix(nsUriToPrefix) {
  var nsMatrix = {}, uri2, prefix3;
  for (uri2 in nsUriToPrefix) {
    prefix3 = nsUriToPrefix[uri2];
    nsMatrix[prefix3] = prefix3;
    nsMatrix[uriPrefix(prefix3)] = uri2;
  }
  return nsMatrix;
}
function noopGetContext() {
  return { "line": 0, "column": 0 };
}
function throwFunc(err) {
  throw err;
}
function Parser(options) {
  if (!this) {
    return new Parser(options);
  }
  var proxy = options && options["proxy"];
  var onText, onOpenTag, onCloseTag, onCDATA, onError = throwFunc, onWarning, onComment, onQuestion, onAttention;
  var getContext = noopGetContext;
  var maybeNS = false;
  var isNamespace = false;
  var returnError = null;
  var parseStop = false;
  var nsUriToPrefix;
  function handleError(err) {
    if (!(err instanceof Error)) {
      err = error(err);
    }
    returnError = err;
    onError(err, getContext);
  }
  function handleWarning(err) {
    if (!onWarning) {
      return;
    }
    if (!(err instanceof Error)) {
      err = error(err);
    }
    onWarning(err, getContext);
  }
  this["on"] = function(name2, cb) {
    if (typeof cb !== "function") {
      throw error("required args <name, cb>");
    }
    switch (name2) {
      case "openTag":
        onOpenTag = cb;
        break;
      case "text":
        onText = cb;
        break;
      case "closeTag":
        onCloseTag = cb;
        break;
      case "error":
        onError = cb;
        break;
      case "warn":
        onWarning = cb;
        break;
      case "cdata":
        onCDATA = cb;
        break;
      case "attention":
        onAttention = cb;
        break;
      case "question":
        onQuestion = cb;
        break;
      case "comment":
        onComment = cb;
        break;
      default:
        throw error("unsupported event: " + name2);
    }
    return this;
  };
  this["ns"] = function(nsMap) {
    if (typeof nsMap === "undefined") {
      nsMap = {};
    }
    if (typeof nsMap !== "object") {
      throw error("required args <nsMap={}>");
    }
    var _nsUriToPrefix = {}, k;
    for (k in nsMap) {
      _nsUriToPrefix[k] = nsMap[k];
    }
    _nsUriToPrefix[XSI_URI] = XSI_PREFIX;
    isNamespace = true;
    nsUriToPrefix = _nsUriToPrefix;
    return this;
  };
  this["parse"] = function(xml2) {
    if (typeof xml2 !== "string") {
      throw error("required args <xml=string>");
    }
    returnError = null;
    parse3(xml2);
    getContext = noopGetContext;
    parseStop = false;
    return returnError;
  };
  this["stop"] = function() {
    parseStop = true;
  };
  function parse3(xml2) {
    var nsMatrixStack = isNamespace ? [] : null, nsMatrix = isNamespace ? buildNsMatrix(nsUriToPrefix) : null, _nsMatrix, nodeStack = [], anonymousNsCount = 0, tagStart = false, tagEnd = false, i = 0, j = 0, x, y, q, w, v, xmlns, elementName, _elementName, elementProxy;
    var attrsString = "", attrsStart = 0, cachedAttrs;
    function getAttrs() {
      if (cachedAttrs !== null) {
        return cachedAttrs;
      }
      var nsUri, nsUriPrefix, nsName2, defaultAlias = isNamespace && nsMatrix["xmlns"], attrList = isNamespace && maybeNS ? [] : null, i2 = attrsStart, s = attrsString, l = s.length, hasNewMatrix, newalias, value, alias, name2, attrs = {}, seenAttrs = {}, skipAttr, w2, j2;
      parseAttr:
        for (; i2 < l; i2++) {
          skipAttr = false;
          w2 = s.charCodeAt(i2);
          if (w2 === 32 || w2 < 14 && w2 > 8) {
            continue;
          }
          if (w2 < 65 || w2 > 122 || w2 > 90 && w2 < 97) {
            if (w2 !== 95 && w2 !== 58) {
              handleWarning("illegal first char attribute name");
              skipAttr = true;
            }
          }
          for (j2 = i2 + 1; j2 < l; j2++) {
            w2 = s.charCodeAt(j2);
            if (w2 > 96 && w2 < 123 || w2 > 64 && w2 < 91 || w2 > 47 && w2 < 59 || w2 === 46 || // '.'
            w2 === 45 || // '-'
            w2 === 95) {
              continue;
            }
            if (w2 === 32 || w2 < 14 && w2 > 8) {
              handleWarning("missing attribute value");
              i2 = j2;
              continue parseAttr;
            }
            if (w2 === 61) {
              break;
            }
            handleWarning("illegal attribute name char");
            skipAttr = true;
          }
          name2 = s.substring(i2, j2);
          if (name2 === "xmlns:xmlns") {
            handleWarning("illegal declaration of xmlns");
            skipAttr = true;
          }
          w2 = s.charCodeAt(j2 + 1);
          if (w2 === 34) {
            j2 = s.indexOf('"', i2 = j2 + 2);
            if (j2 === -1) {
              j2 = s.indexOf("'", i2);
              if (j2 !== -1) {
                handleWarning("attribute value quote missmatch");
                skipAttr = true;
              }
            }
          } else if (w2 === 39) {
            j2 = s.indexOf("'", i2 = j2 + 2);
            if (j2 === -1) {
              j2 = s.indexOf('"', i2);
              if (j2 !== -1) {
                handleWarning("attribute value quote missmatch");
                skipAttr = true;
              }
            }
          } else {
            handleWarning("missing attribute value quotes");
            skipAttr = true;
            for (j2 = j2 + 1; j2 < l; j2++) {
              w2 = s.charCodeAt(j2 + 1);
              if (w2 === 32 || w2 < 14 && w2 > 8) {
                break;
              }
            }
          }
          if (j2 === -1) {
            handleWarning("missing closing quotes");
            j2 = l;
            skipAttr = true;
          }
          if (!skipAttr) {
            value = s.substring(i2, j2);
          }
          i2 = j2;
          for (; j2 + 1 < l; j2++) {
            w2 = s.charCodeAt(j2 + 1);
            if (w2 === 32 || w2 < 14 && w2 > 8) {
              break;
            }
            if (i2 === j2) {
              handleWarning("illegal character after attribute end");
              skipAttr = true;
            }
          }
          i2 = j2 + 1;
          if (skipAttr) {
            continue parseAttr;
          }
          if (name2 in seenAttrs) {
            handleWarning("attribute <" + name2 + "> already defined");
            continue;
          }
          seenAttrs[name2] = true;
          if (!isNamespace) {
            attrs[name2] = value;
            continue;
          }
          if (maybeNS) {
            newalias = name2 === "xmlns" ? "xmlns" : name2.charCodeAt(0) === 120 && name2.substr(0, 6) === "xmlns:" ? name2.substr(6) : null;
            if (newalias !== null) {
              nsUri = decodeEntities(value);
              nsUriPrefix = uriPrefix(newalias);
              alias = nsUriToPrefix[nsUri];
              if (!alias) {
                if (newalias === "xmlns" || nsUriPrefix in nsMatrix && nsMatrix[nsUriPrefix] !== nsUri) {
                  do {
                    alias = "ns" + anonymousNsCount++;
                  } while (typeof nsMatrix[alias] !== "undefined");
                } else {
                  alias = newalias;
                }
                nsUriToPrefix[nsUri] = alias;
              }
              if (nsMatrix[newalias] !== alias) {
                if (!hasNewMatrix) {
                  nsMatrix = cloneNsMatrix(nsMatrix);
                  hasNewMatrix = true;
                }
                nsMatrix[newalias] = alias;
                if (newalias === "xmlns") {
                  nsMatrix[uriPrefix(alias)] = nsUri;
                  defaultAlias = alias;
                }
                nsMatrix[nsUriPrefix] = nsUri;
              }
              attrs[name2] = value;
              continue;
            }
            attrList.push(name2, value);
            continue;
          }
          w2 = name2.indexOf(":");
          if (w2 === -1) {
            attrs[name2] = value;
            continue;
          }
          if (!(nsName2 = nsMatrix[name2.substring(0, w2)])) {
            handleWarning(missingNamespaceForPrefix(name2.substring(0, w2)));
            continue;
          }
          name2 = defaultAlias === nsName2 ? name2.substr(w2 + 1) : nsName2 + name2.substr(w2);
          if (name2 === XSI_TYPE) {
            w2 = value.indexOf(":");
            if (w2 !== -1) {
              nsName2 = value.substring(0, w2);
              nsName2 = nsMatrix[nsName2] || nsName2;
              value = nsName2 + value.substring(w2);
            } else {
              value = defaultAlias + ":" + value;
            }
          }
          attrs[name2] = value;
        }
      if (maybeNS) {
        for (i2 = 0, l = attrList.length; i2 < l; i2++) {
          name2 = attrList[i2++];
          value = attrList[i2];
          w2 = name2.indexOf(":");
          if (w2 !== -1) {
            if (!(nsName2 = nsMatrix[name2.substring(0, w2)])) {
              handleWarning(missingNamespaceForPrefix(name2.substring(0, w2)));
              continue;
            }
            name2 = defaultAlias === nsName2 ? name2.substr(w2 + 1) : nsName2 + name2.substr(w2);
            if (name2 === XSI_TYPE) {
              w2 = value.indexOf(":");
              if (w2 !== -1) {
                nsName2 = value.substring(0, w2);
                nsName2 = nsMatrix[nsName2] || nsName2;
                value = nsName2 + value.substring(w2);
              } else {
                value = defaultAlias + ":" + value;
              }
            }
          }
          attrs[name2] = value;
        }
      }
      return cachedAttrs = attrs;
    }
    function getParseContext() {
      var splitsRe = /(\r\n|\r|\n)/g;
      var line = 0;
      var column = 0;
      var startOfLine = 0;
      var endOfLine = j;
      var match;
      var data;
      while (i >= startOfLine) {
        match = splitsRe.exec(xml2);
        if (!match) {
          break;
        }
        endOfLine = match[0].length + match.index;
        if (endOfLine > i) {
          break;
        }
        line += 1;
        startOfLine = endOfLine;
      }
      if (i == -1) {
        column = endOfLine;
        data = xml2.substring(j);
      } else if (j === 0) {
        data = xml2.substring(j, i);
      } else {
        column = i - startOfLine;
        data = j == -1 ? xml2.substring(i) : xml2.substring(i, j + 1);
      }
      return {
        "data": data,
        "line": line,
        "column": column
      };
    }
    getContext = getParseContext;
    if (proxy) {
      elementProxy = Object.create({}, {
        "name": getter(function() {
          return elementName;
        }),
        "originalName": getter(function() {
          return _elementName;
        }),
        "attrs": getter(getAttrs),
        "ns": getter(function() {
          return nsMatrix;
        })
      });
    }
    while (j !== -1) {
      if (xml2.charCodeAt(j) === 60) {
        i = j;
      } else {
        i = xml2.indexOf("<", j);
      }
      if (i === -1) {
        if (nodeStack.length) {
          return handleError("unexpected end of file");
        }
        if (j === 0) {
          return handleError("missing start tag");
        }
        if (j < xml2.length) {
          if (xml2.substring(j).trim()) {
            handleWarning(NON_WHITESPACE_OUTSIDE_ROOT_NODE);
          }
        }
        return;
      }
      if (j !== i) {
        if (nodeStack.length) {
          if (onText) {
            onText(xml2.substring(j, i), decodeEntities, getContext);
            if (parseStop) {
              return;
            }
          }
        } else {
          if (xml2.substring(j, i).trim()) {
            handleWarning(NON_WHITESPACE_OUTSIDE_ROOT_NODE);
            if (parseStop) {
              return;
            }
          }
        }
      }
      w = xml2.charCodeAt(i + 1);
      if (w === 33) {
        q = xml2.charCodeAt(i + 2);
        if (q === 91 && xml2.substr(i + 3, 6) === "CDATA[") {
          j = xml2.indexOf("]]>", i);
          if (j === -1) {
            return handleError("unclosed cdata");
          }
          if (onCDATA) {
            onCDATA(xml2.substring(i + 9, j), getContext);
            if (parseStop) {
              return;
            }
          }
          j += 3;
          continue;
        }
        if (q === 45 && xml2.charCodeAt(i + 3) === 45) {
          j = xml2.indexOf("-->", i);
          if (j === -1) {
            return handleError("unclosed comment");
          }
          if (onComment) {
            onComment(xml2.substring(i + 4, j), decodeEntities, getContext);
            if (parseStop) {
              return;
            }
          }
          j += 3;
          continue;
        }
      }
      if (w === 63) {
        j = xml2.indexOf("?>", i);
        if (j === -1) {
          return handleError("unclosed question");
        }
        if (onQuestion) {
          onQuestion(xml2.substring(i, j + 2), getContext);
          if (parseStop) {
            return;
          }
        }
        j += 2;
        continue;
      }
      for (x = i + 1; ; x++) {
        v = xml2.charCodeAt(x);
        if (isNaN(v)) {
          j = -1;
          return handleError("unclosed tag");
        }
        if (v === 34) {
          q = xml2.indexOf('"', x + 1);
          x = q !== -1 ? q : x;
        } else if (v === 39) {
          q = xml2.indexOf("'", x + 1);
          x = q !== -1 ? q : x;
        } else if (v === 62) {
          j = x;
          break;
        }
      }
      if (w === 33) {
        if (onAttention) {
          onAttention(xml2.substring(i, j + 1), decodeEntities, getContext);
          if (parseStop) {
            return;
          }
        }
        j += 1;
        continue;
      }
      cachedAttrs = {};
      if (w === 47) {
        tagStart = false;
        tagEnd = true;
        if (!nodeStack.length) {
          return handleError("missing open tag");
        }
        x = elementName = nodeStack.pop();
        q = i + 2 + x.length;
        if (xml2.substring(i + 2, q) !== x) {
          return handleError("closing tag mismatch");
        }
        for (; q < j; q++) {
          w = xml2.charCodeAt(q);
          if (w === 32 || w > 8 && w < 14) {
            continue;
          }
          return handleError("close tag");
        }
      } else {
        if (xml2.charCodeAt(j - 1) === 47) {
          x = elementName = xml2.substring(i + 1, j - 1);
          tagStart = true;
          tagEnd = true;
        } else {
          x = elementName = xml2.substring(i + 1, j);
          tagStart = true;
          tagEnd = false;
        }
        if (!(w > 96 && w < 123 || w > 64 && w < 91 || w === 95 || w === 58)) {
          return handleError("illegal first char nodeName");
        }
        for (q = 1, y = x.length; q < y; q++) {
          w = x.charCodeAt(q);
          if (w > 96 && w < 123 || w > 64 && w < 91 || w > 47 && w < 59 || w === 45 || w === 95 || w == 46) {
            continue;
          }
          if (w === 32 || w < 14 && w > 8) {
            elementName = x.substring(0, q);
            cachedAttrs = null;
            break;
          }
          return handleError("invalid nodeName");
        }
        if (!tagEnd) {
          nodeStack.push(elementName);
        }
      }
      if (isNamespace) {
        _nsMatrix = nsMatrix;
        if (tagStart) {
          if (!tagEnd) {
            nsMatrixStack.push(_nsMatrix);
          }
          if (cachedAttrs === null) {
            if (maybeNS = x.indexOf("xmlns", q) !== -1) {
              attrsStart = q;
              attrsString = x;
              getAttrs();
              maybeNS = false;
            }
          }
        }
        _elementName = elementName;
        w = elementName.indexOf(":");
        if (w !== -1) {
          xmlns = nsMatrix[elementName.substring(0, w)];
          if (!xmlns) {
            return handleError("missing namespace on <" + _elementName + ">");
          }
          elementName = elementName.substr(w + 1);
        } else {
          xmlns = nsMatrix["xmlns"];
        }
        if (xmlns) {
          elementName = xmlns + ":" + elementName;
        }
      }
      if (tagStart) {
        attrsStart = q;
        attrsString = x;
        if (onOpenTag) {
          if (proxy) {
            onOpenTag(elementProxy, decodeEntities, tagEnd, getContext);
          } else {
            onOpenTag(elementName, getAttrs, decodeEntities, tagEnd, getContext);
          }
          if (parseStop) {
            return;
          }
        }
      }
      if (tagEnd) {
        if (onCloseTag) {
          onCloseTag(proxy ? elementProxy : elementName, decodeEntities, tagStart, getContext);
          if (parseStop) {
            return;
          }
        }
        if (isNamespace) {
          if (!tagStart) {
            nsMatrix = nsMatrixStack.pop();
          } else {
            nsMatrix = _nsMatrix;
          }
        }
      }
      j += 1;
    }
  }
}

// node_modules/moddle-xml/dist/index.esm.js
function hasLowerCaseAlias(pkg) {
  return pkg.xml && pkg.xml.tagAlias === "lowerCase";
}
var DEFAULT_NS_MAP = {
  "xsi": "http://www.w3.org/2001/XMLSchema-instance",
  "xml": "http://www.w3.org/XML/1998/namespace"
};
var XSI_TYPE2 = "xsi:type";
function serializeFormat(element) {
  return element.xml && element.xml.serialize;
}
function serializeAsType(element) {
  return serializeFormat(element) === XSI_TYPE2;
}
function serializeAsProperty(element) {
  return serializeFormat(element) === "property";
}
function capitalize(str) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
function aliasToName(aliasNs, pkg) {
  if (!hasLowerCaseAlias(pkg)) {
    return aliasNs.name;
  }
  return aliasNs.prefix + ":" + capitalize(aliasNs.localName);
}
function prefixedToName(nameNs, pkg) {
  var name2 = nameNs.name, localName = nameNs.localName;
  var typePrefix = pkg.xml && pkg.xml.typePrefix;
  if (typePrefix && localName.indexOf(typePrefix) === 0) {
    return nameNs.prefix + ":" + localName.slice(typePrefix.length);
  } else {
    return name2;
  }
}
function normalizeXsiTypeName(name2, model) {
  var nameNs = parseName(name2);
  var pkg = model.getPackage(nameNs.prefix);
  return prefixedToName(nameNs, pkg);
}
function error2(message) {
  return new Error(message);
}
function getModdleDescriptor(element) {
  return element.$descriptor;
}
function Context(options) {
  assign(this, options);
  this.elementsById = {};
  this.references = [];
  this.warnings = [];
  this.addReference = function(reference) {
    this.references.push(reference);
  };
  this.addElement = function(element) {
    if (!element) {
      throw error2("expected element");
    }
    var elementsById = this.elementsById;
    var descriptor = getModdleDescriptor(element);
    var idProperty = descriptor.idProperty, id;
    if (idProperty) {
      id = element.get(idProperty.name);
      if (id) {
        if (!/^([a-z][\w-.]*:)?[a-z_][\w-.]*$/i.test(id)) {
          throw new Error("illegal ID <" + id + ">");
        }
        if (elementsById[id]) {
          throw error2("duplicate ID <" + id + ">");
        }
        elementsById[id] = element;
      }
    }
  };
  this.addWarning = function(warning) {
    this.warnings.push(warning);
  };
}
function BaseHandler() {
}
BaseHandler.prototype.handleEnd = function() {
};
BaseHandler.prototype.handleText = function() {
};
BaseHandler.prototype.handleNode = function() {
};
function NoopHandler() {
}
NoopHandler.prototype = Object.create(BaseHandler.prototype);
NoopHandler.prototype.handleNode = function() {
  return this;
};
function BodyHandler() {
}
BodyHandler.prototype = Object.create(BaseHandler.prototype);
BodyHandler.prototype.handleText = function(text) {
  this.body = (this.body || "") + text;
};
function ReferenceHandler(property, context) {
  this.property = property;
  this.context = context;
}
ReferenceHandler.prototype = Object.create(BodyHandler.prototype);
ReferenceHandler.prototype.handleNode = function(node2) {
  if (this.element) {
    throw error2("expected no sub nodes");
  } else {
    this.element = this.createReference(node2);
  }
  return this;
};
ReferenceHandler.prototype.handleEnd = function() {
  this.element.id = this.body;
};
ReferenceHandler.prototype.createReference = function(node2) {
  return {
    property: this.property.ns.name,
    id: ""
  };
};
function ValueHandler(propertyDesc, element) {
  this.element = element;
  this.propertyDesc = propertyDesc;
}
ValueHandler.prototype = Object.create(BodyHandler.prototype);
ValueHandler.prototype.handleEnd = function() {
  var value = this.body || "", element = this.element, propertyDesc = this.propertyDesc;
  value = coerceType(propertyDesc.type, value);
  if (propertyDesc.isMany) {
    element.get(propertyDesc.name).push(value);
  } else {
    element.set(propertyDesc.name, value);
  }
};
function BaseElementHandler() {
}
BaseElementHandler.prototype = Object.create(BodyHandler.prototype);
BaseElementHandler.prototype.handleNode = function(node2) {
  var parser = this, element = this.element;
  if (!element) {
    element = this.element = this.createElement(node2);
    this.context.addElement(element);
  } else {
    parser = this.handleChild(node2);
  }
  return parser;
};
function ElementHandler(model, typeName, context) {
  this.model = model;
  this.type = model.getType(typeName);
  this.context = context;
}
ElementHandler.prototype = Object.create(BaseElementHandler.prototype);
ElementHandler.prototype.addReference = function(reference) {
  this.context.addReference(reference);
};
ElementHandler.prototype.handleText = function(text) {
  var element = this.element, descriptor = getModdleDescriptor(element), bodyProperty = descriptor.bodyProperty;
  if (!bodyProperty) {
    throw error2("unexpected body text <" + text + ">");
  }
  BodyHandler.prototype.handleText.call(this, text);
};
ElementHandler.prototype.handleEnd = function() {
  var value = this.body, element = this.element, descriptor = getModdleDescriptor(element), bodyProperty = descriptor.bodyProperty;
  if (bodyProperty && value !== void 0) {
    value = coerceType(bodyProperty.type, value);
    element.set(bodyProperty.name, value);
  }
};
ElementHandler.prototype.createElement = function(node2) {
  var attributes = node2.attributes, Type = this.type, descriptor = getModdleDescriptor(Type), context = this.context, instance = new Type({}), model = this.model, propNameNs;
  forEach(attributes, function(value, name2) {
    var prop = descriptor.propertiesByName[name2], values;
    if (prop && prop.isReference) {
      if (!prop.isMany) {
        context.addReference({
          element: instance,
          property: prop.ns.name,
          id: value
        });
      } else {
        values = value.split(" ");
        forEach(values, function(v) {
          context.addReference({
            element: instance,
            property: prop.ns.name,
            id: v
          });
        });
      }
    } else {
      if (prop) {
        value = coerceType(prop.type, value);
      } else if (name2 !== "xmlns") {
        propNameNs = parseName(name2, descriptor.ns.prefix);
        if (model.getPackage(propNameNs.prefix)) {
          context.addWarning({
            message: "unknown attribute <" + name2 + ">",
            element: instance,
            property: name2,
            value
          });
        }
      }
      instance.set(name2, value);
    }
  });
  return instance;
};
ElementHandler.prototype.getPropertyForNode = function(node2) {
  var name2 = node2.name;
  var nameNs = parseName(name2);
  var type = this.type, model = this.model, descriptor = getModdleDescriptor(type);
  var propertyName = nameNs.name, property = descriptor.propertiesByName[propertyName], elementTypeName, elementType;
  if (property && !property.isAttr) {
    if (serializeAsType(property)) {
      elementTypeName = node2.attributes[XSI_TYPE2];
      if (elementTypeName) {
        elementTypeName = normalizeXsiTypeName(elementTypeName, model);
        elementType = model.getType(elementTypeName);
        return assign({}, property, {
          effectiveType: getModdleDescriptor(elementType).name
        });
      }
    }
    return property;
  }
  var pkg = model.getPackage(nameNs.prefix);
  if (pkg) {
    elementTypeName = aliasToName(nameNs, pkg);
    elementType = model.getType(elementTypeName);
    property = find(descriptor.properties, function(p) {
      return !p.isVirtual && !p.isReference && !p.isAttribute && elementType.hasType(p.type);
    });
    if (property) {
      return assign({}, property, {
        effectiveType: getModdleDescriptor(elementType).name
      });
    }
  } else {
    property = find(descriptor.properties, function(p) {
      return !p.isReference && !p.isAttribute && p.type === "Element";
    });
    if (property) {
      return property;
    }
  }
  throw error2("unrecognized element <" + nameNs.name + ">");
};
ElementHandler.prototype.toString = function() {
  return "ElementDescriptor[" + getModdleDescriptor(this.type).name + "]";
};
ElementHandler.prototype.valueHandler = function(propertyDesc, element) {
  return new ValueHandler(propertyDesc, element);
};
ElementHandler.prototype.referenceHandler = function(propertyDesc) {
  return new ReferenceHandler(propertyDesc, this.context);
};
ElementHandler.prototype.handler = function(type) {
  if (type === "Element") {
    return new GenericElementHandler(this.model, type, this.context);
  } else {
    return new ElementHandler(this.model, type, this.context);
  }
};
ElementHandler.prototype.handleChild = function(node2) {
  var propertyDesc, type, element, childHandler;
  propertyDesc = this.getPropertyForNode(node2);
  element = this.element;
  type = propertyDesc.effectiveType || propertyDesc.type;
  if (isSimple(type)) {
    return this.valueHandler(propertyDesc, element);
  }
  if (propertyDesc.isReference) {
    childHandler = this.referenceHandler(propertyDesc).handleNode(node2);
  } else {
    childHandler = this.handler(type).handleNode(node2);
  }
  var newElement = childHandler.element;
  if (newElement !== void 0) {
    if (propertyDesc.isMany) {
      element.get(propertyDesc.name).push(newElement);
    } else {
      element.set(propertyDesc.name, newElement);
    }
    if (propertyDesc.isReference) {
      assign(newElement, {
        element
      });
      this.context.addReference(newElement);
    } else {
      newElement.$parent = element;
    }
  }
  return childHandler;
};
function RootElementHandler(model, typeName, context) {
  ElementHandler.call(this, model, typeName, context);
}
RootElementHandler.prototype = Object.create(ElementHandler.prototype);
RootElementHandler.prototype.createElement = function(node2) {
  var name2 = node2.name, nameNs = parseName(name2), model = this.model, type = this.type, pkg = model.getPackage(nameNs.prefix), typeName = pkg && aliasToName(nameNs, pkg) || name2;
  if (!type.hasType(typeName)) {
    throw error2("unexpected element <" + node2.originalName + ">");
  }
  return ElementHandler.prototype.createElement.call(this, node2);
};
function GenericElementHandler(model, typeName, context) {
  this.model = model;
  this.context = context;
}
GenericElementHandler.prototype = Object.create(BaseElementHandler.prototype);
GenericElementHandler.prototype.createElement = function(node2) {
  var name2 = node2.name, ns2 = parseName(name2), prefix3 = ns2.prefix, uri2 = node2.ns[prefix3 + "$uri"], attributes = node2.attributes;
  return this.model.createAny(name2, uri2, attributes);
};
GenericElementHandler.prototype.handleChild = function(node2) {
  var handler = new GenericElementHandler(this.model, "Element", this.context).handleNode(node2), element = this.element;
  var newElement = handler.element, children;
  if (newElement !== void 0) {
    children = element.$children = element.$children || [];
    children.push(newElement);
    newElement.$parent = element;
  }
  return handler;
};
GenericElementHandler.prototype.handleEnd = function() {
  if (this.body) {
    this.element.$body = this.body;
  }
};
function Reader(options) {
  if (options instanceof Moddle) {
    options = {
      model: options
    };
  }
  assign(this, { lax: false }, options);
}
Reader.prototype.fromXML = function(xml2, options, done) {
  var rootHandler = options.rootHandler;
  if (options instanceof ElementHandler) {
    rootHandler = options;
    options = {};
  } else {
    if (typeof options === "string") {
      rootHandler = this.handler(options);
      options = {};
    } else if (typeof rootHandler === "string") {
      rootHandler = this.handler(rootHandler);
    }
  }
  var model = this.model, lax = this.lax;
  var context = new Context(assign({}, options, { rootHandler })), parser = new Parser({ proxy: true }), stack = createStack();
  rootHandler.context = context;
  stack.push(rootHandler);
  function handleError(err, getContext, lax2) {
    var ctx = getContext();
    var line = ctx.line, column = ctx.column, data = ctx.data;
    if (data.charAt(0) === "<" && data.indexOf(" ") !== -1) {
      data = data.slice(0, data.indexOf(" ")) + ">";
    }
    var message = "unparsable content " + (data ? data + " " : "") + "detected\n	line: " + line + "\n	column: " + column + "\n	nested error: " + err.message;
    if (lax2) {
      context.addWarning({
        message,
        error: err
      });
      return true;
    } else {
      throw error2(message);
    }
  }
  function handleWarning(err, getContext) {
    return handleError(err, getContext, true);
  }
  function resolveReferences() {
    var elementsById = context.elementsById;
    var references = context.references;
    var i, r;
    for (i = 0; r = references[i]; i++) {
      var element = r.element;
      var reference = elementsById[r.id];
      var property = getModdleDescriptor(element).propertiesByName[r.property];
      if (!reference) {
        context.addWarning({
          message: "unresolved reference <" + r.id + ">",
          element: r.element,
          property: r.property,
          value: r.id
        });
      }
      if (property.isMany) {
        var collection = element.get(property.name), idx = collection.indexOf(r);
        if (idx === -1) {
          idx = collection.length;
        }
        if (!reference) {
          collection.splice(idx, 1);
        } else {
          collection[idx] = reference;
        }
      } else {
        element.set(property.name, reference);
      }
    }
  }
  function handleClose() {
    stack.pop().handleEnd();
  }
  var PREAMBLE_START_PATTERN = /^<\?xml /i;
  var ENCODING_PATTERN = / encoding="([^"]+)"/i;
  var UTF_8_PATTERN = /^utf-8$/i;
  function handleQuestion(question) {
    if (!PREAMBLE_START_PATTERN.test(question)) {
      return;
    }
    var match = ENCODING_PATTERN.exec(question);
    var encoding = match && match[1];
    if (!encoding || UTF_8_PATTERN.test(encoding)) {
      return;
    }
    context.addWarning({
      message: "unsupported document encoding <" + encoding + ">, falling back to UTF-8"
    });
  }
  function handleOpen(node2, getContext) {
    var handler = stack.peek();
    try {
      stack.push(handler.handleNode(node2));
    } catch (err) {
      if (handleError(err, getContext, lax)) {
        stack.push(new NoopHandler());
      }
    }
  }
  function handleCData(text, getContext) {
    try {
      stack.peek().handleText(text);
    } catch (err) {
      handleWarning(err, getContext);
    }
  }
  function handleText(text, getContext) {
    if (!text.trim()) {
      return;
    }
    handleCData(text, getContext);
  }
  var uriMap = model.getPackages().reduce(function(uriMap2, p) {
    uriMap2[p.uri] = p.prefix;
    return uriMap2;
  }, {
    "http://www.w3.org/XML/1998/namespace": "xml"
    // add default xml ns
  });
  parser.ns(uriMap).on("openTag", function(obj, decodeStr, selfClosing, getContext) {
    var attrs = obj.attrs || {};
    var decodedAttrs = Object.keys(attrs).reduce(function(d, key) {
      var value = decodeStr(attrs[key]);
      d[key] = value;
      return d;
    }, {});
    var node2 = {
      name: obj.name,
      originalName: obj.originalName,
      attributes: decodedAttrs,
      ns: obj.ns
    };
    handleOpen(node2, getContext);
  }).on("question", handleQuestion).on("closeTag", handleClose).on("cdata", handleCData).on("text", function(text, decodeEntities2, getContext) {
    handleText(decodeEntities2(text), getContext);
  }).on("error", handleError).on("warn", handleWarning);
  return new Promise(function(resolve, reject) {
    var err;
    try {
      parser.parse(xml2);
      resolveReferences();
    } catch (e2) {
      err = e2;
    }
    var rootElement = rootHandler.element;
    if (!err && !rootElement) {
      err = error2("failed to parse document as <" + rootHandler.type.$descriptor.name + ">");
    }
    var warnings = context.warnings;
    var references = context.references;
    var elementsById = context.elementsById;
    if (err) {
      err.warnings = warnings;
      return reject(err);
    } else {
      return resolve({
        rootElement,
        elementsById,
        references,
        warnings
      });
    }
  });
};
Reader.prototype.handler = function(name2) {
  return new RootElementHandler(this.model, name2);
};
function createStack() {
  var stack = [];
  Object.defineProperty(stack, "peek", {
    value: function() {
      return this[this.length - 1];
    }
  });
  return stack;
}
var XML_PREAMBLE = '<?xml version="1.0" encoding="UTF-8"?>\n';
var ESCAPE_ATTR_CHARS = /<|>|'|"|&|\n\r|\n/g;
var ESCAPE_CHARS = /<|>|&/g;
function Namespaces(parent) {
  var prefixMap = {};
  var uriMap = {};
  var used = {};
  var wellknown = [];
  var custom = [];
  this.byUri = function(uri2) {
    return uriMap[uri2] || parent && parent.byUri(uri2);
  };
  this.add = function(ns2, isWellknown) {
    uriMap[ns2.uri] = ns2;
    if (isWellknown) {
      wellknown.push(ns2);
    } else {
      custom.push(ns2);
    }
    this.mapPrefix(ns2.prefix, ns2.uri);
  };
  this.uriByPrefix = function(prefix3) {
    return prefixMap[prefix3 || "xmlns"];
  };
  this.mapPrefix = function(prefix3, uri2) {
    prefixMap[prefix3 || "xmlns"] = uri2;
  };
  this.getNSKey = function(ns2) {
    return ns2.prefix !== void 0 ? ns2.uri + "|" + ns2.prefix : ns2.uri;
  };
  this.logUsed = function(ns2) {
    var uri2 = ns2.uri;
    var nsKey = this.getNSKey(ns2);
    used[nsKey] = this.byUri(uri2);
    if (parent) {
      parent.logUsed(ns2);
    }
  };
  this.getUsed = function(ns2) {
    function isUsed(ns3) {
      var nsKey = self.getNSKey(ns3);
      return used[nsKey];
    }
    var self = this;
    var allNs = [].concat(wellknown, custom);
    return allNs.filter(isUsed);
  };
}
function lower(string) {
  return string.charAt(0).toLowerCase() + string.slice(1);
}
function nameToAlias(name2, pkg) {
  if (hasLowerCaseAlias(pkg)) {
    return lower(name2);
  } else {
    return name2;
  }
}
function inherits(ctor, superCtor) {
  ctor.super_ = superCtor;
  ctor.prototype = Object.create(superCtor.prototype, {
    constructor: {
      value: ctor,
      enumerable: false,
      writable: true,
      configurable: true
    }
  });
}
function nsName(ns2) {
  if (isString(ns2)) {
    return ns2;
  } else {
    return (ns2.prefix ? ns2.prefix + ":" : "") + ns2.localName;
  }
}
function getNsAttrs(namespaces) {
  return namespaces.getUsed().filter(function(ns2) {
    return ns2.prefix !== "xml";
  }).map(function(ns2) {
    var name2 = "xmlns" + (ns2.prefix ? ":" + ns2.prefix : "");
    return { name: name2, value: ns2.uri };
  });
}
function getElementNs(ns2, descriptor) {
  if (descriptor.isGeneric) {
    return assign({ localName: descriptor.ns.localName }, ns2);
  } else {
    return assign({ localName: nameToAlias(descriptor.ns.localName, descriptor.$pkg) }, ns2);
  }
}
function getPropertyNs(ns2, descriptor) {
  return assign({ localName: descriptor.ns.localName }, ns2);
}
function getSerializableProperties(element) {
  var descriptor = element.$descriptor;
  return filter(descriptor.properties, function(p) {
    var name2 = p.name;
    if (p.isVirtual) {
      return false;
    }
    if (!has(element, name2)) {
      return false;
    }
    var value = element[name2];
    if (value === p.default) {
      return false;
    }
    if (value === null) {
      return false;
    }
    return p.isMany ? value.length : true;
  });
}
var ESCAPE_ATTR_MAP = {
  "\n": "#10",
  "\n\r": "#10",
  '"': "#34",
  "'": "#39",
  "<": "#60",
  ">": "#62",
  "&": "#38"
};
var ESCAPE_MAP = {
  "<": "lt",
  ">": "gt",
  "&": "amp"
};
function escape2(str, charPattern, replaceMap) {
  str = isString(str) ? str : "" + str;
  return str.replace(charPattern, function(s) {
    return "&" + replaceMap[s] + ";";
  });
}
function escapeAttr(str) {
  return escape2(str, ESCAPE_ATTR_CHARS, ESCAPE_ATTR_MAP);
}
function escapeBody(str) {
  return escape2(str, ESCAPE_CHARS, ESCAPE_MAP);
}
function filterAttributes(props) {
  return filter(props, function(p) {
    return p.isAttr;
  });
}
function filterContained(props) {
  return filter(props, function(p) {
    return !p.isAttr;
  });
}
function ReferenceSerializer(tagName) {
  this.tagName = tagName;
}
ReferenceSerializer.prototype.build = function(element) {
  this.element = element;
  return this;
};
ReferenceSerializer.prototype.serializeTo = function(writer) {
  writer.appendIndent().append("<" + this.tagName + ">" + this.element.id + "</" + this.tagName + ">").appendNewLine();
};
function BodySerializer() {
}
BodySerializer.prototype.serializeValue = BodySerializer.prototype.serializeTo = function(writer) {
  writer.append(
    this.escape ? escapeBody(this.value) : this.value
  );
};
BodySerializer.prototype.build = function(prop, value) {
  this.value = value;
  if (prop.type === "String" && value.search(ESCAPE_CHARS) !== -1) {
    this.escape = true;
  }
  return this;
};
function ValueSerializer(tagName) {
  this.tagName = tagName;
}
inherits(ValueSerializer, BodySerializer);
ValueSerializer.prototype.serializeTo = function(writer) {
  writer.appendIndent().append("<" + this.tagName + ">");
  this.serializeValue(writer);
  writer.append("</" + this.tagName + ">").appendNewLine();
};
function ElementSerializer(parent, propertyDescriptor) {
  this.body = [];
  this.attrs = [];
  this.parent = parent;
  this.propertyDescriptor = propertyDescriptor;
}
ElementSerializer.prototype.build = function(element) {
  this.element = element;
  var elementDescriptor = element.$descriptor, propertyDescriptor = this.propertyDescriptor;
  var otherAttrs, properties;
  var isGeneric = elementDescriptor.isGeneric;
  if (isGeneric) {
    otherAttrs = this.parseGeneric(element);
  } else {
    otherAttrs = this.parseNsAttributes(element);
  }
  if (propertyDescriptor) {
    this.ns = this.nsPropertyTagName(propertyDescriptor);
  } else {
    this.ns = this.nsTagName(elementDescriptor);
  }
  this.tagName = this.addTagName(this.ns);
  if (!isGeneric) {
    properties = getSerializableProperties(element);
    this.parseAttributes(filterAttributes(properties));
    this.parseContainments(filterContained(properties));
  }
  this.parseGenericAttributes(element, otherAttrs);
  return this;
};
ElementSerializer.prototype.nsTagName = function(descriptor) {
  var effectiveNs = this.logNamespaceUsed(descriptor.ns);
  return getElementNs(effectiveNs, descriptor);
};
ElementSerializer.prototype.nsPropertyTagName = function(descriptor) {
  var effectiveNs = this.logNamespaceUsed(descriptor.ns);
  return getPropertyNs(effectiveNs, descriptor);
};
ElementSerializer.prototype.isLocalNs = function(ns2) {
  return ns2.uri === this.ns.uri;
};
ElementSerializer.prototype.nsAttributeName = function(element) {
  var ns2;
  if (isString(element)) {
    ns2 = parseName(element);
  } else {
    ns2 = element.ns;
  }
  if (element.inherited) {
    return { localName: ns2.localName };
  }
  var effectiveNs = this.logNamespaceUsed(ns2);
  this.getNamespaces().logUsed(effectiveNs);
  if (this.isLocalNs(effectiveNs)) {
    return { localName: ns2.localName };
  } else {
    return assign({ localName: ns2.localName }, effectiveNs);
  }
};
ElementSerializer.prototype.parseGeneric = function(element) {
  var self = this, body = this.body;
  var attributes = [];
  forEach(element, function(val, key) {
    var nonNsAttr;
    if (key === "$body") {
      body.push(new BodySerializer().build({ type: "String" }, val));
    } else if (key === "$children") {
      forEach(val, function(child) {
        body.push(new ElementSerializer(self).build(child));
      });
    } else if (key.indexOf("$") !== 0) {
      nonNsAttr = self.parseNsAttribute(element, key, val);
      if (nonNsAttr) {
        attributes.push({ name: key, value: val });
      }
    }
  });
  return attributes;
};
ElementSerializer.prototype.parseNsAttribute = function(element, name2, value) {
  var model = element.$model;
  var nameNs = parseName(name2);
  var ns2;
  if (nameNs.prefix === "xmlns") {
    ns2 = { prefix: nameNs.localName, uri: value };
  }
  if (!nameNs.prefix && nameNs.localName === "xmlns") {
    ns2 = { uri: value };
  }
  if (!ns2) {
    return {
      name: name2,
      value
    };
  }
  if (model && model.getPackage(value)) {
    this.logNamespace(ns2, true, true);
  } else {
    var actualNs = this.logNamespaceUsed(ns2, true);
    this.getNamespaces().logUsed(actualNs);
  }
};
ElementSerializer.prototype.parseNsAttributes = function(element, attrs) {
  var self = this;
  var genericAttrs = element.$attrs;
  var attributes = [];
  forEach(genericAttrs, function(value, name2) {
    var nonNsAttr = self.parseNsAttribute(element, name2, value);
    if (nonNsAttr) {
      attributes.push(nonNsAttr);
    }
  });
  return attributes;
};
ElementSerializer.prototype.parseGenericAttributes = function(element, attributes) {
  var self = this;
  forEach(attributes, function(attr3) {
    if (attr3.name === XSI_TYPE2) {
      return;
    }
    try {
      self.addAttribute(self.nsAttributeName(attr3.name), attr3.value);
    } catch (e2) {
      console.warn(
        "missing namespace information for ",
        attr3.name,
        "=",
        attr3.value,
        "on",
        element,
        e2
      );
    }
  });
};
ElementSerializer.prototype.parseContainments = function(properties) {
  var self = this, body = this.body, element = this.element;
  forEach(properties, function(p) {
    var value = element.get(p.name), isReference = p.isReference, isMany = p.isMany;
    if (!isMany) {
      value = [value];
    }
    if (p.isBody) {
      body.push(new BodySerializer().build(p, value[0]));
    } else if (isSimple(p.type)) {
      forEach(value, function(v) {
        body.push(new ValueSerializer(self.addTagName(self.nsPropertyTagName(p))).build(p, v));
      });
    } else if (isReference) {
      forEach(value, function(v) {
        body.push(new ReferenceSerializer(self.addTagName(self.nsPropertyTagName(p))).build(v));
      });
    } else {
      var asType = serializeAsType(p), asProperty = serializeAsProperty(p);
      forEach(value, function(v) {
        var serializer;
        if (asType) {
          serializer = new TypeSerializer(self, p);
        } else if (asProperty) {
          serializer = new ElementSerializer(self, p);
        } else {
          serializer = new ElementSerializer(self);
        }
        body.push(serializer.build(v));
      });
    }
  });
};
ElementSerializer.prototype.getNamespaces = function(local) {
  var namespaces = this.namespaces, parent = this.parent, parentNamespaces;
  if (!namespaces) {
    parentNamespaces = parent && parent.getNamespaces();
    if (local || !parentNamespaces) {
      this.namespaces = namespaces = new Namespaces(parentNamespaces);
    } else {
      namespaces = parentNamespaces;
    }
  }
  return namespaces;
};
ElementSerializer.prototype.logNamespace = function(ns2, wellknown, local) {
  var namespaces = this.getNamespaces(local);
  var nsUri = ns2.uri, nsPrefix = ns2.prefix;
  var existing = namespaces.byUri(nsUri);
  if (!existing || local) {
    namespaces.add(ns2, wellknown);
  }
  namespaces.mapPrefix(nsPrefix, nsUri);
  return ns2;
};
ElementSerializer.prototype.logNamespaceUsed = function(ns2, local) {
  var element = this.element, model = element.$model, namespaces = this.getNamespaces(local);
  var prefix3 = ns2.prefix, uri2 = ns2.uri, newPrefix, idx, wellknownUri;
  if (!prefix3 && !uri2) {
    return { localName: ns2.localName };
  }
  wellknownUri = DEFAULT_NS_MAP[prefix3] || model && (model.getPackage(prefix3) || {}).uri;
  uri2 = uri2 || wellknownUri || namespaces.uriByPrefix(prefix3);
  if (!uri2) {
    throw new Error("no namespace uri given for prefix <" + prefix3 + ">");
  }
  ns2 = namespaces.byUri(uri2);
  if (!ns2) {
    newPrefix = prefix3;
    idx = 1;
    while (namespaces.uriByPrefix(newPrefix)) {
      newPrefix = prefix3 + "_" + idx++;
    }
    ns2 = this.logNamespace({ prefix: newPrefix, uri: uri2 }, wellknownUri === uri2);
  }
  if (prefix3) {
    namespaces.mapPrefix(prefix3, uri2);
  }
  return ns2;
};
ElementSerializer.prototype.parseAttributes = function(properties) {
  var self = this, element = this.element;
  forEach(properties, function(p) {
    var value = element.get(p.name);
    if (p.isReference) {
      if (!p.isMany) {
        value = value.id;
      } else {
        var values = [];
        forEach(value, function(v) {
          values.push(v.id);
        });
        value = values.join(" ");
      }
    }
    self.addAttribute(self.nsAttributeName(p), value);
  });
};
ElementSerializer.prototype.addTagName = function(nsTagName) {
  var actualNs = this.logNamespaceUsed(nsTagName);
  this.getNamespaces().logUsed(actualNs);
  return nsName(nsTagName);
};
ElementSerializer.prototype.addAttribute = function(name2, value) {
  var attrs = this.attrs;
  if (isString(value)) {
    value = escapeAttr(value);
  }
  var idx = findIndex(attrs, function(element) {
    return element.name.localName === name2.localName && element.name.uri === name2.uri && element.name.prefix === name2.prefix;
  });
  var attr3 = { name: name2, value };
  if (idx !== -1) {
    attrs.splice(idx, 1, attr3);
  } else {
    attrs.push(attr3);
  }
};
ElementSerializer.prototype.serializeAttributes = function(writer) {
  var attrs = this.attrs, namespaces = this.namespaces;
  if (namespaces) {
    attrs = getNsAttrs(namespaces).concat(attrs);
  }
  forEach(attrs, function(a) {
    writer.append(" ").append(nsName(a.name)).append('="').append(a.value).append('"');
  });
};
ElementSerializer.prototype.serializeTo = function(writer) {
  var firstBody = this.body[0], indent = firstBody && firstBody.constructor !== BodySerializer;
  writer.appendIndent().append("<" + this.tagName);
  this.serializeAttributes(writer);
  writer.append(firstBody ? ">" : " />");
  if (firstBody) {
    if (indent) {
      writer.appendNewLine().indent();
    }
    forEach(this.body, function(b) {
      b.serializeTo(writer);
    });
    if (indent) {
      writer.unindent().appendIndent();
    }
    writer.append("</" + this.tagName + ">");
  }
  writer.appendNewLine();
};
function TypeSerializer(parent, propertyDescriptor) {
  ElementSerializer.call(this, parent, propertyDescriptor);
}
inherits(TypeSerializer, ElementSerializer);
TypeSerializer.prototype.parseNsAttributes = function(element) {
  var attributes = ElementSerializer.prototype.parseNsAttributes.call(this, element);
  var descriptor = element.$descriptor;
  if (descriptor.name === this.propertyDescriptor.type) {
    return attributes;
  }
  var typeNs = this.typeNs = this.nsTagName(descriptor);
  this.getNamespaces().logUsed(this.typeNs);
  var pkg = element.$model.getPackage(typeNs.uri), typePrefix = pkg.xml && pkg.xml.typePrefix || "";
  this.addAttribute(
    this.nsAttributeName(XSI_TYPE2),
    (typeNs.prefix ? typeNs.prefix + ":" : "") + typePrefix + descriptor.ns.localName
  );
  return attributes;
};
TypeSerializer.prototype.isLocalNs = function(ns2) {
  return ns2.uri === (this.typeNs || this.ns).uri;
};
function SavingWriter() {
  this.value = "";
  this.write = function(str) {
    this.value += str;
  };
}
function FormatingWriter(out, format2) {
  var indent = [""];
  this.append = function(str) {
    out.write(str);
    return this;
  };
  this.appendNewLine = function() {
    if (format2) {
      out.write("\n");
    }
    return this;
  };
  this.appendIndent = function() {
    if (format2) {
      out.write(indent.join("  "));
    }
    return this;
  };
  this.indent = function() {
    indent.push("");
    return this;
  };
  this.unindent = function() {
    indent.pop();
    return this;
  };
}
function Writer(options) {
  options = assign({ format: false, preamble: true }, options || {});
  function toXML(tree, writer) {
    var internalWriter = writer || new SavingWriter();
    var formatingWriter = new FormatingWriter(internalWriter, options.format);
    if (options.preamble) {
      formatingWriter.append(XML_PREAMBLE);
    }
    new ElementSerializer().build(tree).serializeTo(formatingWriter);
    if (!writer) {
      return internalWriter.value;
    }
  }
  return {
    toXML
  };
}

// node_modules/bpmn-moddle/dist/index.esm.js
function BpmnModdle(packages2, options) {
  Moddle.call(this, packages2, options);
}
BpmnModdle.prototype = Object.create(Moddle.prototype);
BpmnModdle.prototype.fromXML = function(xmlStr, typeName, options) {
  if (!isString(typeName)) {
    options = typeName;
    typeName = "bpmn:Definitions";
  }
  var reader = new Reader(assign({ model: this, lax: true }, options));
  var rootHandler = reader.handler(typeName);
  return reader.fromXML(xmlStr, rootHandler);
};
BpmnModdle.prototype.toXML = function(element, options) {
  var writer = new Writer(options);
  return new Promise(function(resolve, reject) {
    try {
      var result = writer.toXML(element);
      return resolve({
        xml: result
      });
    } catch (err) {
      return reject(err);
    }
  });
};
var name$5 = "BPMN20";
var uri$5 = "http://www.omg.org/spec/BPMN/20100524/MODEL";
var prefix$5 = "bpmn";
var associations$5 = [];
var types$5 = [
  {
    name: "Interface",
    superClass: [
      "RootElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "operations",
        type: "Operation",
        isMany: true
      },
      {
        name: "implementationRef",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "Operation",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "inMessageRef",
        type: "Message",
        isReference: true
      },
      {
        name: "outMessageRef",
        type: "Message",
        isReference: true
      },
      {
        name: "errorRef",
        type: "Error",
        isMany: true,
        isReference: true
      },
      {
        name: "implementationRef",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "EndPoint",
    superClass: [
      "RootElement"
    ]
  },
  {
    name: "Auditing",
    superClass: [
      "BaseElement"
    ]
  },
  {
    name: "GlobalTask",
    superClass: [
      "CallableElement"
    ],
    properties: [
      {
        name: "resources",
        type: "ResourceRole",
        isMany: true
      }
    ]
  },
  {
    name: "Monitoring",
    superClass: [
      "BaseElement"
    ]
  },
  {
    name: "Performer",
    superClass: [
      "ResourceRole"
    ]
  },
  {
    name: "Process",
    superClass: [
      "FlowElementsContainer",
      "CallableElement"
    ],
    properties: [
      {
        name: "processType",
        type: "ProcessType",
        isAttr: true
      },
      {
        name: "isClosed",
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "auditing",
        type: "Auditing"
      },
      {
        name: "monitoring",
        type: "Monitoring"
      },
      {
        name: "properties",
        type: "Property",
        isMany: true
      },
      {
        name: "laneSets",
        isMany: true,
        replaces: "FlowElementsContainer#laneSets",
        type: "LaneSet"
      },
      {
        name: "flowElements",
        isMany: true,
        replaces: "FlowElementsContainer#flowElements",
        type: "FlowElement"
      },
      {
        name: "artifacts",
        type: "Artifact",
        isMany: true
      },
      {
        name: "resources",
        type: "ResourceRole",
        isMany: true
      },
      {
        name: "correlationSubscriptions",
        type: "CorrelationSubscription",
        isMany: true
      },
      {
        name: "supports",
        type: "Process",
        isMany: true,
        isReference: true
      },
      {
        name: "definitionalCollaborationRef",
        type: "Collaboration",
        isAttr: true,
        isReference: true
      },
      {
        name: "isExecutable",
        isAttr: true,
        type: "Boolean"
      }
    ]
  },
  {
    name: "LaneSet",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "lanes",
        type: "Lane",
        isMany: true
      },
      {
        name: "name",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "Lane",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "partitionElementRef",
        type: "BaseElement",
        isAttr: true,
        isReference: true
      },
      {
        name: "partitionElement",
        type: "BaseElement"
      },
      {
        name: "flowNodeRef",
        type: "FlowNode",
        isMany: true,
        isReference: true
      },
      {
        name: "childLaneSet",
        type: "LaneSet",
        xml: {
          serialize: "xsi:type"
        }
      }
    ]
  },
  {
    name: "GlobalManualTask",
    superClass: [
      "GlobalTask"
    ]
  },
  {
    name: "ManualTask",
    superClass: [
      "Task"
    ]
  },
  {
    name: "UserTask",
    superClass: [
      "Task"
    ],
    properties: [
      {
        name: "renderings",
        type: "Rendering",
        isMany: true
      },
      {
        name: "implementation",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "Rendering",
    superClass: [
      "BaseElement"
    ]
  },
  {
    name: "HumanPerformer",
    superClass: [
      "Performer"
    ]
  },
  {
    name: "PotentialOwner",
    superClass: [
      "HumanPerformer"
    ]
  },
  {
    name: "GlobalUserTask",
    superClass: [
      "GlobalTask"
    ],
    properties: [
      {
        name: "implementation",
        isAttr: true,
        type: "String"
      },
      {
        name: "renderings",
        type: "Rendering",
        isMany: true
      }
    ]
  },
  {
    name: "Gateway",
    isAbstract: true,
    superClass: [
      "FlowNode"
    ],
    properties: [
      {
        name: "gatewayDirection",
        type: "GatewayDirection",
        "default": "Unspecified",
        isAttr: true
      }
    ]
  },
  {
    name: "EventBasedGateway",
    superClass: [
      "Gateway"
    ],
    properties: [
      {
        name: "instantiate",
        "default": false,
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "eventGatewayType",
        type: "EventBasedGatewayType",
        isAttr: true,
        "default": "Exclusive"
      }
    ]
  },
  {
    name: "ComplexGateway",
    superClass: [
      "Gateway"
    ],
    properties: [
      {
        name: "activationCondition",
        type: "Expression",
        xml: {
          serialize: "xsi:type"
        }
      },
      {
        name: "default",
        type: "SequenceFlow",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "ExclusiveGateway",
    superClass: [
      "Gateway"
    ],
    properties: [
      {
        name: "default",
        type: "SequenceFlow",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "InclusiveGateway",
    superClass: [
      "Gateway"
    ],
    properties: [
      {
        name: "default",
        type: "SequenceFlow",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "ParallelGateway",
    superClass: [
      "Gateway"
    ]
  },
  {
    name: "RootElement",
    isAbstract: true,
    superClass: [
      "BaseElement"
    ]
  },
  {
    name: "Relationship",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "type",
        isAttr: true,
        type: "String"
      },
      {
        name: "direction",
        type: "RelationshipDirection",
        isAttr: true
      },
      {
        name: "source",
        isMany: true,
        isReference: true,
        type: "Element"
      },
      {
        name: "target",
        isMany: true,
        isReference: true,
        type: "Element"
      }
    ]
  },
  {
    name: "BaseElement",
    isAbstract: true,
    properties: [
      {
        name: "id",
        isAttr: true,
        type: "String",
        isId: true
      },
      {
        name: "documentation",
        type: "Documentation",
        isMany: true
      },
      {
        name: "extensionDefinitions",
        type: "ExtensionDefinition",
        isMany: true,
        isReference: true
      },
      {
        name: "extensionElements",
        type: "ExtensionElements"
      }
    ]
  },
  {
    name: "Extension",
    properties: [
      {
        name: "mustUnderstand",
        "default": false,
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "definition",
        type: "ExtensionDefinition",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "ExtensionDefinition",
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "extensionAttributeDefinitions",
        type: "ExtensionAttributeDefinition",
        isMany: true
      }
    ]
  },
  {
    name: "ExtensionAttributeDefinition",
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "type",
        isAttr: true,
        type: "String"
      },
      {
        name: "isReference",
        "default": false,
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "extensionDefinition",
        type: "ExtensionDefinition",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "ExtensionElements",
    properties: [
      {
        name: "valueRef",
        isAttr: true,
        isReference: true,
        type: "Element"
      },
      {
        name: "values",
        type: "Element",
        isMany: true
      },
      {
        name: "extensionAttributeDefinition",
        type: "ExtensionAttributeDefinition",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "Documentation",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "text",
        type: "String",
        isBody: true
      },
      {
        name: "textFormat",
        "default": "text/plain",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "Event",
    isAbstract: true,
    superClass: [
      "FlowNode",
      "InteractionNode"
    ],
    properties: [
      {
        name: "properties",
        type: "Property",
        isMany: true
      }
    ]
  },
  {
    name: "IntermediateCatchEvent",
    superClass: [
      "CatchEvent"
    ]
  },
  {
    name: "IntermediateThrowEvent",
    superClass: [
      "ThrowEvent"
    ]
  },
  {
    name: "EndEvent",
    superClass: [
      "ThrowEvent"
    ]
  },
  {
    name: "StartEvent",
    superClass: [
      "CatchEvent"
    ],
    properties: [
      {
        name: "isInterrupting",
        "default": true,
        isAttr: true,
        type: "Boolean"
      }
    ]
  },
  {
    name: "ThrowEvent",
    isAbstract: true,
    superClass: [
      "Event"
    ],
    properties: [
      {
        name: "dataInputs",
        type: "DataInput",
        isMany: true
      },
      {
        name: "dataInputAssociations",
        type: "DataInputAssociation",
        isMany: true
      },
      {
        name: "inputSet",
        type: "InputSet"
      },
      {
        name: "eventDefinitions",
        type: "EventDefinition",
        isMany: true
      },
      {
        name: "eventDefinitionRef",
        type: "EventDefinition",
        isMany: true,
        isReference: true
      }
    ]
  },
  {
    name: "CatchEvent",
    isAbstract: true,
    superClass: [
      "Event"
    ],
    properties: [
      {
        name: "parallelMultiple",
        isAttr: true,
        type: "Boolean",
        "default": false
      },
      {
        name: "dataOutputs",
        type: "DataOutput",
        isMany: true
      },
      {
        name: "dataOutputAssociations",
        type: "DataOutputAssociation",
        isMany: true
      },
      {
        name: "outputSet",
        type: "OutputSet"
      },
      {
        name: "eventDefinitions",
        type: "EventDefinition",
        isMany: true
      },
      {
        name: "eventDefinitionRef",
        type: "EventDefinition",
        isMany: true,
        isReference: true
      }
    ]
  },
  {
    name: "BoundaryEvent",
    superClass: [
      "CatchEvent"
    ],
    properties: [
      {
        name: "cancelActivity",
        "default": true,
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "attachedToRef",
        type: "Activity",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "EventDefinition",
    isAbstract: true,
    superClass: [
      "RootElement"
    ]
  },
  {
    name: "CancelEventDefinition",
    superClass: [
      "EventDefinition"
    ]
  },
  {
    name: "ErrorEventDefinition",
    superClass: [
      "EventDefinition"
    ],
    properties: [
      {
        name: "errorRef",
        type: "Error",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "TerminateEventDefinition",
    superClass: [
      "EventDefinition"
    ]
  },
  {
    name: "EscalationEventDefinition",
    superClass: [
      "EventDefinition"
    ],
    properties: [
      {
        name: "escalationRef",
        type: "Escalation",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "Escalation",
    properties: [
      {
        name: "structureRef",
        type: "ItemDefinition",
        isAttr: true,
        isReference: true
      },
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "escalationCode",
        isAttr: true,
        type: "String"
      }
    ],
    superClass: [
      "RootElement"
    ]
  },
  {
    name: "CompensateEventDefinition",
    superClass: [
      "EventDefinition"
    ],
    properties: [
      {
        name: "waitForCompletion",
        isAttr: true,
        type: "Boolean",
        "default": true
      },
      {
        name: "activityRef",
        type: "Activity",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "TimerEventDefinition",
    superClass: [
      "EventDefinition"
    ],
    properties: [
      {
        name: "timeDate",
        type: "Expression",
        xml: {
          serialize: "xsi:type"
        }
      },
      {
        name: "timeCycle",
        type: "Expression",
        xml: {
          serialize: "xsi:type"
        }
      },
      {
        name: "timeDuration",
        type: "Expression",
        xml: {
          serialize: "xsi:type"
        }
      }
    ]
  },
  {
    name: "LinkEventDefinition",
    superClass: [
      "EventDefinition"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "target",
        type: "LinkEventDefinition",
        isReference: true
      },
      {
        name: "source",
        type: "LinkEventDefinition",
        isMany: true,
        isReference: true
      }
    ]
  },
  {
    name: "MessageEventDefinition",
    superClass: [
      "EventDefinition"
    ],
    properties: [
      {
        name: "messageRef",
        type: "Message",
        isAttr: true,
        isReference: true
      },
      {
        name: "operationRef",
        type: "Operation",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "ConditionalEventDefinition",
    superClass: [
      "EventDefinition"
    ],
    properties: [
      {
        name: "condition",
        type: "Expression",
        xml: {
          serialize: "xsi:type"
        }
      }
    ]
  },
  {
    name: "SignalEventDefinition",
    superClass: [
      "EventDefinition"
    ],
    properties: [
      {
        name: "signalRef",
        type: "Signal",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "Signal",
    superClass: [
      "RootElement"
    ],
    properties: [
      {
        name: "structureRef",
        type: "ItemDefinition",
        isAttr: true,
        isReference: true
      },
      {
        name: "name",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "ImplicitThrowEvent",
    superClass: [
      "ThrowEvent"
    ]
  },
  {
    name: "DataState",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "ItemAwareElement",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "itemSubjectRef",
        type: "ItemDefinition",
        isAttr: true,
        isReference: true
      },
      {
        name: "dataState",
        type: "DataState"
      }
    ]
  },
  {
    name: "DataAssociation",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "sourceRef",
        type: "ItemAwareElement",
        isMany: true,
        isReference: true
      },
      {
        name: "targetRef",
        type: "ItemAwareElement",
        isReference: true
      },
      {
        name: "transformation",
        type: "FormalExpression",
        xml: {
          serialize: "property"
        }
      },
      {
        name: "assignment",
        type: "Assignment",
        isMany: true
      }
    ]
  },
  {
    name: "DataInput",
    superClass: [
      "ItemAwareElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "isCollection",
        "default": false,
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "inputSetRef",
        type: "InputSet",
        isMany: true,
        isVirtual: true,
        isReference: true
      },
      {
        name: "inputSetWithOptional",
        type: "InputSet",
        isMany: true,
        isVirtual: true,
        isReference: true
      },
      {
        name: "inputSetWithWhileExecuting",
        type: "InputSet",
        isMany: true,
        isVirtual: true,
        isReference: true
      }
    ]
  },
  {
    name: "DataOutput",
    superClass: [
      "ItemAwareElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "isCollection",
        "default": false,
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "outputSetRef",
        type: "OutputSet",
        isMany: true,
        isVirtual: true,
        isReference: true
      },
      {
        name: "outputSetWithOptional",
        type: "OutputSet",
        isMany: true,
        isVirtual: true,
        isReference: true
      },
      {
        name: "outputSetWithWhileExecuting",
        type: "OutputSet",
        isMany: true,
        isVirtual: true,
        isReference: true
      }
    ]
  },
  {
    name: "InputSet",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "dataInputRefs",
        type: "DataInput",
        isMany: true,
        isReference: true
      },
      {
        name: "optionalInputRefs",
        type: "DataInput",
        isMany: true,
        isReference: true
      },
      {
        name: "whileExecutingInputRefs",
        type: "DataInput",
        isMany: true,
        isReference: true
      },
      {
        name: "outputSetRefs",
        type: "OutputSet",
        isMany: true,
        isReference: true
      }
    ]
  },
  {
    name: "OutputSet",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "dataOutputRefs",
        type: "DataOutput",
        isMany: true,
        isReference: true
      },
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "inputSetRefs",
        type: "InputSet",
        isMany: true,
        isReference: true
      },
      {
        name: "optionalOutputRefs",
        type: "DataOutput",
        isMany: true,
        isReference: true
      },
      {
        name: "whileExecutingOutputRefs",
        type: "DataOutput",
        isMany: true,
        isReference: true
      }
    ]
  },
  {
    name: "Property",
    superClass: [
      "ItemAwareElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "DataInputAssociation",
    superClass: [
      "DataAssociation"
    ]
  },
  {
    name: "DataOutputAssociation",
    superClass: [
      "DataAssociation"
    ]
  },
  {
    name: "InputOutputSpecification",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "dataInputs",
        type: "DataInput",
        isMany: true
      },
      {
        name: "dataOutputs",
        type: "DataOutput",
        isMany: true
      },
      {
        name: "inputSets",
        type: "InputSet",
        isMany: true
      },
      {
        name: "outputSets",
        type: "OutputSet",
        isMany: true
      }
    ]
  },
  {
    name: "DataObject",
    superClass: [
      "FlowElement",
      "ItemAwareElement"
    ],
    properties: [
      {
        name: "isCollection",
        "default": false,
        isAttr: true,
        type: "Boolean"
      }
    ]
  },
  {
    name: "InputOutputBinding",
    properties: [
      {
        name: "inputDataRef",
        type: "InputSet",
        isAttr: true,
        isReference: true
      },
      {
        name: "outputDataRef",
        type: "OutputSet",
        isAttr: true,
        isReference: true
      },
      {
        name: "operationRef",
        type: "Operation",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "Assignment",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "from",
        type: "Expression",
        xml: {
          serialize: "xsi:type"
        }
      },
      {
        name: "to",
        type: "Expression",
        xml: {
          serialize: "xsi:type"
        }
      }
    ]
  },
  {
    name: "DataStore",
    superClass: [
      "RootElement",
      "ItemAwareElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "capacity",
        isAttr: true,
        type: "Integer"
      },
      {
        name: "isUnlimited",
        "default": true,
        isAttr: true,
        type: "Boolean"
      }
    ]
  },
  {
    name: "DataStoreReference",
    superClass: [
      "ItemAwareElement",
      "FlowElement"
    ],
    properties: [
      {
        name: "dataStoreRef",
        type: "DataStore",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "DataObjectReference",
    superClass: [
      "ItemAwareElement",
      "FlowElement"
    ],
    properties: [
      {
        name: "dataObjectRef",
        type: "DataObject",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "ConversationLink",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "sourceRef",
        type: "InteractionNode",
        isAttr: true,
        isReference: true
      },
      {
        name: "targetRef",
        type: "InteractionNode",
        isAttr: true,
        isReference: true
      },
      {
        name: "name",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "ConversationAssociation",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "innerConversationNodeRef",
        type: "ConversationNode",
        isAttr: true,
        isReference: true
      },
      {
        name: "outerConversationNodeRef",
        type: "ConversationNode",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "CallConversation",
    superClass: [
      "ConversationNode"
    ],
    properties: [
      {
        name: "calledCollaborationRef",
        type: "Collaboration",
        isAttr: true,
        isReference: true
      },
      {
        name: "participantAssociations",
        type: "ParticipantAssociation",
        isMany: true
      }
    ]
  },
  {
    name: "Conversation",
    superClass: [
      "ConversationNode"
    ]
  },
  {
    name: "SubConversation",
    superClass: [
      "ConversationNode"
    ],
    properties: [
      {
        name: "conversationNodes",
        type: "ConversationNode",
        isMany: true
      }
    ]
  },
  {
    name: "ConversationNode",
    isAbstract: true,
    superClass: [
      "InteractionNode",
      "BaseElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "participantRef",
        type: "Participant",
        isMany: true,
        isReference: true
      },
      {
        name: "messageFlowRefs",
        type: "MessageFlow",
        isMany: true,
        isReference: true
      },
      {
        name: "correlationKeys",
        type: "CorrelationKey",
        isMany: true
      }
    ]
  },
  {
    name: "GlobalConversation",
    superClass: [
      "Collaboration"
    ]
  },
  {
    name: "PartnerEntity",
    superClass: [
      "RootElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "participantRef",
        type: "Participant",
        isMany: true,
        isReference: true
      }
    ]
  },
  {
    name: "PartnerRole",
    superClass: [
      "RootElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "participantRef",
        type: "Participant",
        isMany: true,
        isReference: true
      }
    ]
  },
  {
    name: "CorrelationProperty",
    superClass: [
      "RootElement"
    ],
    properties: [
      {
        name: "correlationPropertyRetrievalExpression",
        type: "CorrelationPropertyRetrievalExpression",
        isMany: true
      },
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "type",
        type: "ItemDefinition",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "Error",
    superClass: [
      "RootElement"
    ],
    properties: [
      {
        name: "structureRef",
        type: "ItemDefinition",
        isAttr: true,
        isReference: true
      },
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "errorCode",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "CorrelationKey",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "correlationPropertyRef",
        type: "CorrelationProperty",
        isMany: true,
        isReference: true
      },
      {
        name: "name",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "Expression",
    superClass: [
      "BaseElement"
    ],
    isAbstract: false,
    properties: [
      {
        name: "body",
        isBody: true,
        type: "String"
      }
    ]
  },
  {
    name: "FormalExpression",
    superClass: [
      "Expression"
    ],
    properties: [
      {
        name: "language",
        isAttr: true,
        type: "String"
      },
      {
        name: "evaluatesToTypeRef",
        type: "ItemDefinition",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "Message",
    superClass: [
      "RootElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "itemRef",
        type: "ItemDefinition",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "ItemDefinition",
    superClass: [
      "RootElement"
    ],
    properties: [
      {
        name: "itemKind",
        type: "ItemKind",
        isAttr: true
      },
      {
        name: "structureRef",
        isAttr: true,
        type: "String"
      },
      {
        name: "isCollection",
        "default": false,
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "import",
        type: "Import",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "FlowElement",
    isAbstract: true,
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "auditing",
        type: "Auditing"
      },
      {
        name: "monitoring",
        type: "Monitoring"
      },
      {
        name: "categoryValueRef",
        type: "CategoryValue",
        isMany: true,
        isReference: true
      }
    ]
  },
  {
    name: "SequenceFlow",
    superClass: [
      "FlowElement"
    ],
    properties: [
      {
        name: "isImmediate",
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "conditionExpression",
        type: "Expression",
        xml: {
          serialize: "xsi:type"
        }
      },
      {
        name: "sourceRef",
        type: "FlowNode",
        isAttr: true,
        isReference: true
      },
      {
        name: "targetRef",
        type: "FlowNode",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "FlowElementsContainer",
    isAbstract: true,
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "laneSets",
        type: "LaneSet",
        isMany: true
      },
      {
        name: "flowElements",
        type: "FlowElement",
        isMany: true
      }
    ]
  },
  {
    name: "CallableElement",
    isAbstract: true,
    superClass: [
      "RootElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "ioSpecification",
        type: "InputOutputSpecification",
        xml: {
          serialize: "property"
        }
      },
      {
        name: "supportedInterfaceRef",
        type: "Interface",
        isMany: true,
        isReference: true
      },
      {
        name: "ioBinding",
        type: "InputOutputBinding",
        isMany: true,
        xml: {
          serialize: "property"
        }
      }
    ]
  },
  {
    name: "FlowNode",
    isAbstract: true,
    superClass: [
      "FlowElement"
    ],
    properties: [
      {
        name: "incoming",
        type: "SequenceFlow",
        isMany: true,
        isReference: true
      },
      {
        name: "outgoing",
        type: "SequenceFlow",
        isMany: true,
        isReference: true
      },
      {
        name: "lanes",
        type: "Lane",
        isMany: true,
        isVirtual: true,
        isReference: true
      }
    ]
  },
  {
    name: "CorrelationPropertyRetrievalExpression",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "messagePath",
        type: "FormalExpression"
      },
      {
        name: "messageRef",
        type: "Message",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "CorrelationPropertyBinding",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "dataPath",
        type: "FormalExpression"
      },
      {
        name: "correlationPropertyRef",
        type: "CorrelationProperty",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "Resource",
    superClass: [
      "RootElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "resourceParameters",
        type: "ResourceParameter",
        isMany: true
      }
    ]
  },
  {
    name: "ResourceParameter",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "isRequired",
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "type",
        type: "ItemDefinition",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "CorrelationSubscription",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "correlationKeyRef",
        type: "CorrelationKey",
        isAttr: true,
        isReference: true
      },
      {
        name: "correlationPropertyBinding",
        type: "CorrelationPropertyBinding",
        isMany: true
      }
    ]
  },
  {
    name: "MessageFlow",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "sourceRef",
        type: "InteractionNode",
        isAttr: true,
        isReference: true
      },
      {
        name: "targetRef",
        type: "InteractionNode",
        isAttr: true,
        isReference: true
      },
      {
        name: "messageRef",
        type: "Message",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "MessageFlowAssociation",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "innerMessageFlowRef",
        type: "MessageFlow",
        isAttr: true,
        isReference: true
      },
      {
        name: "outerMessageFlowRef",
        type: "MessageFlow",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "InteractionNode",
    isAbstract: true,
    properties: [
      {
        name: "incomingConversationLinks",
        type: "ConversationLink",
        isMany: true,
        isVirtual: true,
        isReference: true
      },
      {
        name: "outgoingConversationLinks",
        type: "ConversationLink",
        isMany: true,
        isVirtual: true,
        isReference: true
      }
    ]
  },
  {
    name: "Participant",
    superClass: [
      "InteractionNode",
      "BaseElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "interfaceRef",
        type: "Interface",
        isMany: true,
        isReference: true
      },
      {
        name: "participantMultiplicity",
        type: "ParticipantMultiplicity"
      },
      {
        name: "endPointRefs",
        type: "EndPoint",
        isMany: true,
        isReference: true
      },
      {
        name: "processRef",
        type: "Process",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "ParticipantAssociation",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "innerParticipantRef",
        type: "Participant",
        isAttr: true,
        isReference: true
      },
      {
        name: "outerParticipantRef",
        type: "Participant",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "ParticipantMultiplicity",
    properties: [
      {
        name: "minimum",
        "default": 0,
        isAttr: true,
        type: "Integer"
      },
      {
        name: "maximum",
        "default": 1,
        isAttr: true,
        type: "Integer"
      }
    ],
    superClass: [
      "BaseElement"
    ]
  },
  {
    name: "Collaboration",
    superClass: [
      "RootElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "isClosed",
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "participants",
        type: "Participant",
        isMany: true
      },
      {
        name: "messageFlows",
        type: "MessageFlow",
        isMany: true
      },
      {
        name: "artifacts",
        type: "Artifact",
        isMany: true
      },
      {
        name: "conversations",
        type: "ConversationNode",
        isMany: true
      },
      {
        name: "conversationAssociations",
        type: "ConversationAssociation"
      },
      {
        name: "participantAssociations",
        type: "ParticipantAssociation",
        isMany: true
      },
      {
        name: "messageFlowAssociations",
        type: "MessageFlowAssociation",
        isMany: true
      },
      {
        name: "correlationKeys",
        type: "CorrelationKey",
        isMany: true
      },
      {
        name: "choreographyRef",
        type: "Choreography",
        isMany: true,
        isReference: true
      },
      {
        name: "conversationLinks",
        type: "ConversationLink",
        isMany: true
      }
    ]
  },
  {
    name: "ChoreographyActivity",
    isAbstract: true,
    superClass: [
      "FlowNode"
    ],
    properties: [
      {
        name: "participantRef",
        type: "Participant",
        isMany: true,
        isReference: true
      },
      {
        name: "initiatingParticipantRef",
        type: "Participant",
        isAttr: true,
        isReference: true
      },
      {
        name: "correlationKeys",
        type: "CorrelationKey",
        isMany: true
      },
      {
        name: "loopType",
        type: "ChoreographyLoopType",
        "default": "None",
        isAttr: true
      }
    ]
  },
  {
    name: "CallChoreography",
    superClass: [
      "ChoreographyActivity"
    ],
    properties: [
      {
        name: "calledChoreographyRef",
        type: "Choreography",
        isAttr: true,
        isReference: true
      },
      {
        name: "participantAssociations",
        type: "ParticipantAssociation",
        isMany: true
      }
    ]
  },
  {
    name: "SubChoreography",
    superClass: [
      "ChoreographyActivity",
      "FlowElementsContainer"
    ],
    properties: [
      {
        name: "artifacts",
        type: "Artifact",
        isMany: true
      }
    ]
  },
  {
    name: "ChoreographyTask",
    superClass: [
      "ChoreographyActivity"
    ],
    properties: [
      {
        name: "messageFlowRef",
        type: "MessageFlow",
        isMany: true,
        isReference: true
      }
    ]
  },
  {
    name: "Choreography",
    superClass: [
      "Collaboration",
      "FlowElementsContainer"
    ]
  },
  {
    name: "GlobalChoreographyTask",
    superClass: [
      "Choreography"
    ],
    properties: [
      {
        name: "initiatingParticipantRef",
        type: "Participant",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "TextAnnotation",
    superClass: [
      "Artifact"
    ],
    properties: [
      {
        name: "text",
        type: "String"
      },
      {
        name: "textFormat",
        "default": "text/plain",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "Group",
    superClass: [
      "Artifact"
    ],
    properties: [
      {
        name: "categoryValueRef",
        type: "CategoryValue",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "Association",
    superClass: [
      "Artifact"
    ],
    properties: [
      {
        name: "associationDirection",
        type: "AssociationDirection",
        isAttr: true
      },
      {
        name: "sourceRef",
        type: "BaseElement",
        isAttr: true,
        isReference: true
      },
      {
        name: "targetRef",
        type: "BaseElement",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "Category",
    superClass: [
      "RootElement"
    ],
    properties: [
      {
        name: "categoryValue",
        type: "CategoryValue",
        isMany: true
      },
      {
        name: "name",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "Artifact",
    isAbstract: true,
    superClass: [
      "BaseElement"
    ]
  },
  {
    name: "CategoryValue",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "categorizedFlowElements",
        type: "FlowElement",
        isMany: true,
        isVirtual: true,
        isReference: true
      },
      {
        name: "value",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "Activity",
    isAbstract: true,
    superClass: [
      "FlowNode"
    ],
    properties: [
      {
        name: "isForCompensation",
        "default": false,
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "default",
        type: "SequenceFlow",
        isAttr: true,
        isReference: true
      },
      {
        name: "ioSpecification",
        type: "InputOutputSpecification",
        xml: {
          serialize: "property"
        }
      },
      {
        name: "boundaryEventRefs",
        type: "BoundaryEvent",
        isMany: true,
        isReference: true
      },
      {
        name: "properties",
        type: "Property",
        isMany: true
      },
      {
        name: "dataInputAssociations",
        type: "DataInputAssociation",
        isMany: true
      },
      {
        name: "dataOutputAssociations",
        type: "DataOutputAssociation",
        isMany: true
      },
      {
        name: "startQuantity",
        "default": 1,
        isAttr: true,
        type: "Integer"
      },
      {
        name: "resources",
        type: "ResourceRole",
        isMany: true
      },
      {
        name: "completionQuantity",
        "default": 1,
        isAttr: true,
        type: "Integer"
      },
      {
        name: "loopCharacteristics",
        type: "LoopCharacteristics"
      }
    ]
  },
  {
    name: "ServiceTask",
    superClass: [
      "Task"
    ],
    properties: [
      {
        name: "implementation",
        isAttr: true,
        type: "String"
      },
      {
        name: "operationRef",
        type: "Operation",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "SubProcess",
    superClass: [
      "Activity",
      "FlowElementsContainer",
      "InteractionNode"
    ],
    properties: [
      {
        name: "triggeredByEvent",
        "default": false,
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "artifacts",
        type: "Artifact",
        isMany: true
      }
    ]
  },
  {
    name: "LoopCharacteristics",
    isAbstract: true,
    superClass: [
      "BaseElement"
    ]
  },
  {
    name: "MultiInstanceLoopCharacteristics",
    superClass: [
      "LoopCharacteristics"
    ],
    properties: [
      {
        name: "isSequential",
        "default": false,
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "behavior",
        type: "MultiInstanceBehavior",
        "default": "All",
        isAttr: true
      },
      {
        name: "loopCardinality",
        type: "Expression",
        xml: {
          serialize: "xsi:type"
        }
      },
      {
        name: "loopDataInputRef",
        type: "ItemAwareElement",
        isReference: true
      },
      {
        name: "loopDataOutputRef",
        type: "ItemAwareElement",
        isReference: true
      },
      {
        name: "inputDataItem",
        type: "DataInput",
        xml: {
          serialize: "property"
        }
      },
      {
        name: "outputDataItem",
        type: "DataOutput",
        xml: {
          serialize: "property"
        }
      },
      {
        name: "complexBehaviorDefinition",
        type: "ComplexBehaviorDefinition",
        isMany: true
      },
      {
        name: "completionCondition",
        type: "Expression",
        xml: {
          serialize: "xsi:type"
        }
      },
      {
        name: "oneBehaviorEventRef",
        type: "EventDefinition",
        isAttr: true,
        isReference: true
      },
      {
        name: "noneBehaviorEventRef",
        type: "EventDefinition",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "StandardLoopCharacteristics",
    superClass: [
      "LoopCharacteristics"
    ],
    properties: [
      {
        name: "testBefore",
        "default": false,
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "loopCondition",
        type: "Expression",
        xml: {
          serialize: "xsi:type"
        }
      },
      {
        name: "loopMaximum",
        type: "Integer",
        isAttr: true
      }
    ]
  },
  {
    name: "CallActivity",
    superClass: [
      "Activity",
      "InteractionNode"
    ],
    properties: [
      {
        name: "calledElement",
        type: "String",
        isAttr: true
      }
    ]
  },
  {
    name: "Task",
    superClass: [
      "Activity",
      "InteractionNode"
    ]
  },
  {
    name: "SendTask",
    superClass: [
      "Task"
    ],
    properties: [
      {
        name: "implementation",
        isAttr: true,
        type: "String"
      },
      {
        name: "operationRef",
        type: "Operation",
        isAttr: true,
        isReference: true
      },
      {
        name: "messageRef",
        type: "Message",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "ReceiveTask",
    superClass: [
      "Task"
    ],
    properties: [
      {
        name: "implementation",
        isAttr: true,
        type: "String"
      },
      {
        name: "instantiate",
        "default": false,
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "operationRef",
        type: "Operation",
        isAttr: true,
        isReference: true
      },
      {
        name: "messageRef",
        type: "Message",
        isAttr: true,
        isReference: true
      }
    ]
  },
  {
    name: "ScriptTask",
    superClass: [
      "Task"
    ],
    properties: [
      {
        name: "scriptFormat",
        isAttr: true,
        type: "String"
      },
      {
        name: "script",
        type: "String"
      }
    ]
  },
  {
    name: "BusinessRuleTask",
    superClass: [
      "Task"
    ],
    properties: [
      {
        name: "implementation",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "AdHocSubProcess",
    superClass: [
      "SubProcess"
    ],
    properties: [
      {
        name: "completionCondition",
        type: "Expression",
        xml: {
          serialize: "xsi:type"
        }
      },
      {
        name: "ordering",
        type: "AdHocOrdering",
        isAttr: true
      },
      {
        name: "cancelRemainingInstances",
        "default": true,
        isAttr: true,
        type: "Boolean"
      }
    ]
  },
  {
    name: "Transaction",
    superClass: [
      "SubProcess"
    ],
    properties: [
      {
        name: "protocol",
        isAttr: true,
        type: "String"
      },
      {
        name: "method",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "GlobalScriptTask",
    superClass: [
      "GlobalTask"
    ],
    properties: [
      {
        name: "scriptLanguage",
        isAttr: true,
        type: "String"
      },
      {
        name: "script",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "GlobalBusinessRuleTask",
    superClass: [
      "GlobalTask"
    ],
    properties: [
      {
        name: "implementation",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "ComplexBehaviorDefinition",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "condition",
        type: "FormalExpression"
      },
      {
        name: "event",
        type: "ImplicitThrowEvent"
      }
    ]
  },
  {
    name: "ResourceRole",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "resourceRef",
        type: "Resource",
        isReference: true
      },
      {
        name: "resourceParameterBindings",
        type: "ResourceParameterBinding",
        isMany: true
      },
      {
        name: "resourceAssignmentExpression",
        type: "ResourceAssignmentExpression"
      },
      {
        name: "name",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "ResourceParameterBinding",
    properties: [
      {
        name: "expression",
        type: "Expression",
        xml: {
          serialize: "xsi:type"
        }
      },
      {
        name: "parameterRef",
        type: "ResourceParameter",
        isAttr: true,
        isReference: true
      }
    ],
    superClass: [
      "BaseElement"
    ]
  },
  {
    name: "ResourceAssignmentExpression",
    properties: [
      {
        name: "expression",
        type: "Expression",
        xml: {
          serialize: "xsi:type"
        }
      }
    ],
    superClass: [
      "BaseElement"
    ]
  },
  {
    name: "Import",
    properties: [
      {
        name: "importType",
        isAttr: true,
        type: "String"
      },
      {
        name: "location",
        isAttr: true,
        type: "String"
      },
      {
        name: "namespace",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "Definitions",
    superClass: [
      "BaseElement"
    ],
    properties: [
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "targetNamespace",
        isAttr: true,
        type: "String"
      },
      {
        name: "expressionLanguage",
        "default": "http://www.w3.org/1999/XPath",
        isAttr: true,
        type: "String"
      },
      {
        name: "typeLanguage",
        "default": "http://www.w3.org/2001/XMLSchema",
        isAttr: true,
        type: "String"
      },
      {
        name: "imports",
        type: "Import",
        isMany: true
      },
      {
        name: "extensions",
        type: "Extension",
        isMany: true
      },
      {
        name: "rootElements",
        type: "RootElement",
        isMany: true
      },
      {
        name: "diagrams",
        isMany: true,
        type: "bpmndi:BPMNDiagram"
      },
      {
        name: "exporter",
        isAttr: true,
        type: "String"
      },
      {
        name: "relationships",
        type: "Relationship",
        isMany: true
      },
      {
        name: "exporterVersion",
        isAttr: true,
        type: "String"
      }
    ]
  }
];
var enumerations$3 = [
  {
    name: "ProcessType",
    literalValues: [
      {
        name: "None"
      },
      {
        name: "Public"
      },
      {
        name: "Private"
      }
    ]
  },
  {
    name: "GatewayDirection",
    literalValues: [
      {
        name: "Unspecified"
      },
      {
        name: "Converging"
      },
      {
        name: "Diverging"
      },
      {
        name: "Mixed"
      }
    ]
  },
  {
    name: "EventBasedGatewayType",
    literalValues: [
      {
        name: "Parallel"
      },
      {
        name: "Exclusive"
      }
    ]
  },
  {
    name: "RelationshipDirection",
    literalValues: [
      {
        name: "None"
      },
      {
        name: "Forward"
      },
      {
        name: "Backward"
      },
      {
        name: "Both"
      }
    ]
  },
  {
    name: "ItemKind",
    literalValues: [
      {
        name: "Physical"
      },
      {
        name: "Information"
      }
    ]
  },
  {
    name: "ChoreographyLoopType",
    literalValues: [
      {
        name: "None"
      },
      {
        name: "Standard"
      },
      {
        name: "MultiInstanceSequential"
      },
      {
        name: "MultiInstanceParallel"
      }
    ]
  },
  {
    name: "AssociationDirection",
    literalValues: [
      {
        name: "None"
      },
      {
        name: "One"
      },
      {
        name: "Both"
      }
    ]
  },
  {
    name: "MultiInstanceBehavior",
    literalValues: [
      {
        name: "None"
      },
      {
        name: "One"
      },
      {
        name: "All"
      },
      {
        name: "Complex"
      }
    ]
  },
  {
    name: "AdHocOrdering",
    literalValues: [
      {
        name: "Parallel"
      },
      {
        name: "Sequential"
      }
    ]
  }
];
var xml$1 = {
  tagAlias: "lowerCase",
  typePrefix: "t"
};
var BpmnPackage = {
  name: name$5,
  uri: uri$5,
  prefix: prefix$5,
  associations: associations$5,
  types: types$5,
  enumerations: enumerations$3,
  xml: xml$1
};
var name$4 = "BPMNDI";
var uri$4 = "http://www.omg.org/spec/BPMN/20100524/DI";
var prefix$4 = "bpmndi";
var types$4 = [
  {
    name: "BPMNDiagram",
    properties: [
      {
        name: "plane",
        type: "BPMNPlane",
        redefines: "di:Diagram#rootElement"
      },
      {
        name: "labelStyle",
        type: "BPMNLabelStyle",
        isMany: true
      }
    ],
    superClass: [
      "di:Diagram"
    ]
  },
  {
    name: "BPMNPlane",
    properties: [
      {
        name: "bpmnElement",
        isAttr: true,
        isReference: true,
        type: "bpmn:BaseElement",
        redefines: "di:DiagramElement#modelElement"
      }
    ],
    superClass: [
      "di:Plane"
    ]
  },
  {
    name: "BPMNShape",
    properties: [
      {
        name: "bpmnElement",
        isAttr: true,
        isReference: true,
        type: "bpmn:BaseElement",
        redefines: "di:DiagramElement#modelElement"
      },
      {
        name: "isHorizontal",
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "isExpanded",
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "isMarkerVisible",
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "label",
        type: "BPMNLabel"
      },
      {
        name: "isMessageVisible",
        isAttr: true,
        type: "Boolean"
      },
      {
        name: "participantBandKind",
        type: "ParticipantBandKind",
        isAttr: true
      },
      {
        name: "choreographyActivityShape",
        type: "BPMNShape",
        isAttr: true,
        isReference: true
      }
    ],
    superClass: [
      "di:LabeledShape"
    ]
  },
  {
    name: "BPMNEdge",
    properties: [
      {
        name: "label",
        type: "BPMNLabel"
      },
      {
        name: "bpmnElement",
        isAttr: true,
        isReference: true,
        type: "bpmn:BaseElement",
        redefines: "di:DiagramElement#modelElement"
      },
      {
        name: "sourceElement",
        isAttr: true,
        isReference: true,
        type: "di:DiagramElement",
        redefines: "di:Edge#source"
      },
      {
        name: "targetElement",
        isAttr: true,
        isReference: true,
        type: "di:DiagramElement",
        redefines: "di:Edge#target"
      },
      {
        name: "messageVisibleKind",
        type: "MessageVisibleKind",
        isAttr: true,
        "default": "initiating"
      }
    ],
    superClass: [
      "di:LabeledEdge"
    ]
  },
  {
    name: "BPMNLabel",
    properties: [
      {
        name: "labelStyle",
        type: "BPMNLabelStyle",
        isAttr: true,
        isReference: true,
        redefines: "di:DiagramElement#style"
      }
    ],
    superClass: [
      "di:Label"
    ]
  },
  {
    name: "BPMNLabelStyle",
    properties: [
      {
        name: "font",
        type: "dc:Font"
      }
    ],
    superClass: [
      "di:Style"
    ]
  }
];
var enumerations$2 = [
  {
    name: "ParticipantBandKind",
    literalValues: [
      {
        name: "top_initiating"
      },
      {
        name: "middle_initiating"
      },
      {
        name: "bottom_initiating"
      },
      {
        name: "top_non_initiating"
      },
      {
        name: "middle_non_initiating"
      },
      {
        name: "bottom_non_initiating"
      }
    ]
  },
  {
    name: "MessageVisibleKind",
    literalValues: [
      {
        name: "initiating"
      },
      {
        name: "non_initiating"
      }
    ]
  }
];
var associations$4 = [];
var BpmnDiPackage = {
  name: name$4,
  uri: uri$4,
  prefix: prefix$4,
  types: types$4,
  enumerations: enumerations$2,
  associations: associations$4
};
var name$3 = "DC";
var uri$3 = "http://www.omg.org/spec/DD/20100524/DC";
var prefix$3 = "dc";
var types$3 = [
  {
    name: "Boolean"
  },
  {
    name: "Integer"
  },
  {
    name: "Real"
  },
  {
    name: "String"
  },
  {
    name: "Font",
    properties: [
      {
        name: "name",
        type: "String",
        isAttr: true
      },
      {
        name: "size",
        type: "Real",
        isAttr: true
      },
      {
        name: "isBold",
        type: "Boolean",
        isAttr: true
      },
      {
        name: "isItalic",
        type: "Boolean",
        isAttr: true
      },
      {
        name: "isUnderline",
        type: "Boolean",
        isAttr: true
      },
      {
        name: "isStrikeThrough",
        type: "Boolean",
        isAttr: true
      }
    ]
  },
  {
    name: "Point",
    properties: [
      {
        name: "x",
        type: "Real",
        "default": "0",
        isAttr: true
      },
      {
        name: "y",
        type: "Real",
        "default": "0",
        isAttr: true
      }
    ]
  },
  {
    name: "Bounds",
    properties: [
      {
        name: "x",
        type: "Real",
        "default": "0",
        isAttr: true
      },
      {
        name: "y",
        type: "Real",
        "default": "0",
        isAttr: true
      },
      {
        name: "width",
        type: "Real",
        isAttr: true
      },
      {
        name: "height",
        type: "Real",
        isAttr: true
      }
    ]
  }
];
var associations$3 = [];
var DcPackage = {
  name: name$3,
  uri: uri$3,
  prefix: prefix$3,
  types: types$3,
  associations: associations$3
};
var name$2 = "DI";
var uri$2 = "http://www.omg.org/spec/DD/20100524/DI";
var prefix$2 = "di";
var types$2 = [
  {
    name: "DiagramElement",
    isAbstract: true,
    properties: [
      {
        name: "id",
        isAttr: true,
        isId: true,
        type: "String"
      },
      {
        name: "extension",
        type: "Extension"
      },
      {
        name: "owningDiagram",
        type: "Diagram",
        isReadOnly: true,
        isVirtual: true,
        isReference: true
      },
      {
        name: "owningElement",
        type: "DiagramElement",
        isReadOnly: true,
        isVirtual: true,
        isReference: true
      },
      {
        name: "modelElement",
        isReadOnly: true,
        isVirtual: true,
        isReference: true,
        type: "Element"
      },
      {
        name: "style",
        type: "Style",
        isReadOnly: true,
        isVirtual: true,
        isReference: true
      },
      {
        name: "ownedElement",
        type: "DiagramElement",
        isReadOnly: true,
        isMany: true,
        isVirtual: true
      }
    ]
  },
  {
    name: "Node",
    isAbstract: true,
    superClass: [
      "DiagramElement"
    ]
  },
  {
    name: "Edge",
    isAbstract: true,
    superClass: [
      "DiagramElement"
    ],
    properties: [
      {
        name: "source",
        type: "DiagramElement",
        isReadOnly: true,
        isVirtual: true,
        isReference: true
      },
      {
        name: "target",
        type: "DiagramElement",
        isReadOnly: true,
        isVirtual: true,
        isReference: true
      },
      {
        name: "waypoint",
        isUnique: false,
        isMany: true,
        type: "dc:Point",
        xml: {
          serialize: "xsi:type"
        }
      }
    ]
  },
  {
    name: "Diagram",
    isAbstract: true,
    properties: [
      {
        name: "id",
        isAttr: true,
        isId: true,
        type: "String"
      },
      {
        name: "rootElement",
        type: "DiagramElement",
        isReadOnly: true,
        isVirtual: true
      },
      {
        name: "name",
        isAttr: true,
        type: "String"
      },
      {
        name: "documentation",
        isAttr: true,
        type: "String"
      },
      {
        name: "resolution",
        isAttr: true,
        type: "Real"
      },
      {
        name: "ownedStyle",
        type: "Style",
        isReadOnly: true,
        isMany: true,
        isVirtual: true
      }
    ]
  },
  {
    name: "Shape",
    isAbstract: true,
    superClass: [
      "Node"
    ],
    properties: [
      {
        name: "bounds",
        type: "dc:Bounds"
      }
    ]
  },
  {
    name: "Plane",
    isAbstract: true,
    superClass: [
      "Node"
    ],
    properties: [
      {
        name: "planeElement",
        type: "DiagramElement",
        subsettedProperty: "DiagramElement-ownedElement",
        isMany: true
      }
    ]
  },
  {
    name: "LabeledEdge",
    isAbstract: true,
    superClass: [
      "Edge"
    ],
    properties: [
      {
        name: "ownedLabel",
        type: "Label",
        isReadOnly: true,
        subsettedProperty: "DiagramElement-ownedElement",
        isMany: true,
        isVirtual: true
      }
    ]
  },
  {
    name: "LabeledShape",
    isAbstract: true,
    superClass: [
      "Shape"
    ],
    properties: [
      {
        name: "ownedLabel",
        type: "Label",
        isReadOnly: true,
        subsettedProperty: "DiagramElement-ownedElement",
        isMany: true,
        isVirtual: true
      }
    ]
  },
  {
    name: "Label",
    isAbstract: true,
    superClass: [
      "Node"
    ],
    properties: [
      {
        name: "bounds",
        type: "dc:Bounds"
      }
    ]
  },
  {
    name: "Style",
    isAbstract: true,
    properties: [
      {
        name: "id",
        isAttr: true,
        isId: true,
        type: "String"
      }
    ]
  },
  {
    name: "Extension",
    properties: [
      {
        name: "values",
        isMany: true,
        type: "Element"
      }
    ]
  }
];
var associations$2 = [];
var xml = {
  tagAlias: "lowerCase"
};
var DiPackage = {
  name: name$2,
  uri: uri$2,
  prefix: prefix$2,
  types: types$2,
  associations: associations$2,
  xml
};
var name$1 = "bpmn.io colors for BPMN";
var uri$1 = "http://bpmn.io/schema/bpmn/biocolor/1.0";
var prefix$1 = "bioc";
var types$1 = [
  {
    name: "ColoredShape",
    "extends": [
      "bpmndi:BPMNShape"
    ],
    properties: [
      {
        name: "stroke",
        isAttr: true,
        type: "String"
      },
      {
        name: "fill",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "ColoredEdge",
    "extends": [
      "bpmndi:BPMNEdge"
    ],
    properties: [
      {
        name: "stroke",
        isAttr: true,
        type: "String"
      },
      {
        name: "fill",
        isAttr: true,
        type: "String"
      }
    ]
  }
];
var enumerations$1 = [];
var associations$1 = [];
var BiocPackage = {
  name: name$1,
  uri: uri$1,
  prefix: prefix$1,
  types: types$1,
  enumerations: enumerations$1,
  associations: associations$1
};
var name = "BPMN in Color";
var uri = "http://www.omg.org/spec/BPMN/non-normative/color/1.0";
var prefix2 = "color";
var types2 = [
  {
    name: "ColoredLabel",
    "extends": [
      "bpmndi:BPMNLabel"
    ],
    properties: [
      {
        name: "color",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "ColoredShape",
    "extends": [
      "bpmndi:BPMNShape"
    ],
    properties: [
      {
        name: "background-color",
        isAttr: true,
        type: "String"
      },
      {
        name: "border-color",
        isAttr: true,
        type: "String"
      }
    ]
  },
  {
    name: "ColoredEdge",
    "extends": [
      "bpmndi:BPMNEdge"
    ],
    properties: [
      {
        name: "border-color",
        isAttr: true,
        type: "String"
      }
    ]
  }
];
var enumerations = [];
var associations = [];
var BpmnInColorPackage = {
  name,
  uri,
  prefix: prefix2,
  types: types2,
  enumerations,
  associations
};
var packages = {
  bpmn: BpmnPackage,
  bpmndi: BpmnDiPackage,
  dc: DcPackage,
  di: DiPackage,
  bioc: BiocPackage,
  color: BpmnInColorPackage
};
function simple(additionalPackages, options) {
  var pks = assign({}, packages, additionalPackages);
  return new BpmnModdle(pks, options);
}

// node_modules/bpmn-js/lib/util/CompatibilityUtil.js
var DI_ERROR_MESSAGE = "Tried to access di from the businessObject. The di is available through the diagram element only. For more information, see https://github.com/bpmn-io/bpmn-js/issues/1472";
function ensureCompatDiRef(businessObject) {
  if (!has(businessObject, "di")) {
    Object.defineProperty(businessObject, "di", {
      enumerable: false,
      get: function() {
        throw new Error(DI_ERROR_MESSAGE);
      }
    });
  }
}

// node_modules/bpmn-js/lib/import/BpmnTreeWalker.js
function is2(element, type) {
  return element.$instanceOf(type);
}
function findDisplayCandidate(definitions) {
  return find(definitions.rootElements, function(e2) {
    return is2(e2, "bpmn:Process") || is2(e2, "bpmn:Collaboration");
  });
}
function BpmnTreeWalker(handler) {
  var handledElements = {};
  var deferred = [];
  var diMap = {};
  function contextual(fn, ctx) {
    return function(e2) {
      fn(e2, ctx);
    };
  }
  function handled(element) {
    handledElements[element.id] = element;
  }
  function isHandled(element) {
    return handledElements[element.id];
  }
  function visit(element, ctx) {
    var gfx = element.gfx;
    if (gfx) {
      throw new Error(
        `already rendered ${elementToString(element)}`
      );
    }
    return handler.element(element, diMap[element.id], ctx);
  }
  function visitRoot(element, diagram) {
    return handler.root(element, diMap[element.id], diagram);
  }
  function visitIfDi(element, ctx) {
    try {
      var gfx = diMap[element.id] && visit(element, ctx);
      handled(element);
      return gfx;
    } catch (error3) {
      logError(error3.message, { element, error: error3 });
      console.error(`failed to import ${elementToString(element)}`, error3);
    }
  }
  function logError(message, context) {
    handler.error(message, context);
  }
  var registerDi = this.registerDi = function registerDi2(di) {
    var bpmnElement = di.bpmnElement;
    if (bpmnElement) {
      if (diMap[bpmnElement.id]) {
        logError(
          `multiple DI elements defined for ${elementToString(bpmnElement)}`,
          { element: bpmnElement }
        );
      } else {
        diMap[bpmnElement.id] = di;
        ensureCompatDiRef(bpmnElement);
      }
    } else {
      logError(
        `no bpmnElement referenced in ${elementToString(di)}`,
        { element: di }
      );
    }
  };
  function handleDiagram(diagram) {
    handlePlane(diagram.plane);
  }
  function handlePlane(plane) {
    registerDi(plane);
    forEach(plane.planeElement, handlePlaneElement);
  }
  function handlePlaneElement(planeElement) {
    registerDi(planeElement);
  }
  this.handleDefinitions = function handleDefinitions(definitions, diagram) {
    var diagrams = definitions.diagrams;
    if (diagram && diagrams.indexOf(diagram) === -1) {
      throw new Error("diagram not part of <bpmn:Definitions />");
    }
    if (!diagram && diagrams && diagrams.length) {
      diagram = diagrams[0];
    }
    if (!diagram) {
      throw new Error("no diagram to display");
    }
    diMap = {};
    handleDiagram(diagram);
    var plane = diagram.plane;
    if (!plane) {
      throw new Error(
        `no plane for ${elementToString(diagram)}`
      );
    }
    var rootElement = plane.bpmnElement;
    if (!rootElement) {
      rootElement = findDisplayCandidate(definitions);
      if (!rootElement) {
        throw new Error("no process or collaboration to display");
      } else {
        logError(
          `correcting missing bpmnElement on ${elementToString(plane)} to ${elementToString(rootElement)}`
        );
        plane.bpmnElement = rootElement;
        registerDi(plane);
      }
    }
    var ctx = visitRoot(rootElement, plane);
    if (is2(rootElement, "bpmn:Process") || is2(rootElement, "bpmn:SubProcess")) {
      handleProcess(rootElement, ctx);
    } else if (is2(rootElement, "bpmn:Collaboration")) {
      handleCollaboration(rootElement, ctx);
      handleUnhandledProcesses(definitions.rootElements, ctx);
    } else {
      throw new Error(
        `unsupported bpmnElement for ${elementToString(plane)}: ${elementToString(rootElement)}`
      );
    }
    handleDeferred(deferred);
  };
  var handleDeferred = this.handleDeferred = function handleDeferred2() {
    var fn;
    while (deferred.length) {
      fn = deferred.shift();
      fn();
    }
  };
  function handleProcess(process, context) {
    handleFlowElementsContainer(process, context);
    handleIoSpecification(process.ioSpecification, context);
    handleArtifacts(process.artifacts, context);
    handled(process);
  }
  function handleUnhandledProcesses(rootElements, ctx) {
    var processes = filter(rootElements, function(e2) {
      return !isHandled(e2) && is2(e2, "bpmn:Process") && e2.laneSets;
    });
    processes.forEach(contextual(handleProcess, ctx));
  }
  function handleMessageFlow(messageFlow, context) {
    visitIfDi(messageFlow, context);
  }
  function handleMessageFlows(messageFlows, context) {
    forEach(messageFlows, contextual(handleMessageFlow, context));
  }
  function handleDataAssociation(association, context) {
    visitIfDi(association, context);
  }
  function handleDataInput(dataInput, context) {
    visitIfDi(dataInput, context);
  }
  function handleDataOutput(dataOutput, context) {
    visitIfDi(dataOutput, context);
  }
  function handleArtifact(artifact, context) {
    visitIfDi(artifact, context);
  }
  function handleArtifacts(artifacts, context) {
    forEach(artifacts, function(e2) {
      if (is2(e2, "bpmn:Association")) {
        deferred.push(function() {
          handleArtifact(e2, context);
        });
      } else {
        handleArtifact(e2, context);
      }
    });
  }
  function handleIoSpecification(ioSpecification, context) {
    if (!ioSpecification) {
      return;
    }
    forEach(ioSpecification.dataInputs, contextual(handleDataInput, context));
    forEach(ioSpecification.dataOutputs, contextual(handleDataOutput, context));
  }
  var handleSubProcess = this.handleSubProcess = function handleSubProcess2(subProcess, context) {
    handleFlowElementsContainer(subProcess, context);
    handleArtifacts(subProcess.artifacts, context);
  };
  function handleFlowNode(flowNode, context) {
    var childCtx = visitIfDi(flowNode, context);
    if (is2(flowNode, "bpmn:SubProcess")) {
      handleSubProcess(flowNode, childCtx || context);
    }
    if (is2(flowNode, "bpmn:Activity")) {
      handleIoSpecification(flowNode.ioSpecification, context);
    }
    deferred.push(function() {
      forEach(flowNode.dataInputAssociations, contextual(handleDataAssociation, context));
      forEach(flowNode.dataOutputAssociations, contextual(handleDataAssociation, context));
    });
  }
  function handleSequenceFlow(sequenceFlow, context) {
    visitIfDi(sequenceFlow, context);
  }
  function handleDataElement(dataObject, context) {
    visitIfDi(dataObject, context);
  }
  function handleLane(lane, context) {
    deferred.push(function() {
      var newContext = visitIfDi(lane, context);
      if (lane.childLaneSet) {
        handleLaneSet(lane.childLaneSet, newContext || context);
      }
      wireFlowNodeRefs(lane);
    });
  }
  function handleLaneSet(laneSet, context) {
    forEach(laneSet.lanes, contextual(handleLane, context));
  }
  function handleLaneSets(laneSets, context) {
    forEach(laneSets, contextual(handleLaneSet, context));
  }
  function handleFlowElementsContainer(container, context) {
    handleFlowElements(container.flowElements, context);
    if (container.laneSets) {
      handleLaneSets(container.laneSets, context);
    }
  }
  function handleFlowElements(flowElements, context) {
    forEach(flowElements, function(flowElement) {
      if (is2(flowElement, "bpmn:SequenceFlow")) {
        deferred.push(function() {
          handleSequenceFlow(flowElement, context);
        });
      } else if (is2(flowElement, "bpmn:BoundaryEvent")) {
        deferred.unshift(function() {
          handleFlowNode(flowElement, context);
        });
      } else if (is2(flowElement, "bpmn:FlowNode")) {
        handleFlowNode(flowElement, context);
      } else if (is2(flowElement, "bpmn:DataObject")) {
      } else if (is2(flowElement, "bpmn:DataStoreReference")) {
        handleDataElement(flowElement, context);
      } else if (is2(flowElement, "bpmn:DataObjectReference")) {
        handleDataElement(flowElement, context);
      } else {
        logError(
          `unrecognized flowElement ${elementToString(flowElement)} in context ${elementToString(context && context.businessObject)}`,
          {
            element: flowElement,
            context
          }
        );
      }
    });
  }
  function handleParticipant(participant, context) {
    var newCtx = visitIfDi(participant, context);
    var process = participant.processRef;
    if (process) {
      handleProcess(process, newCtx || context);
    }
  }
  function handleCollaboration(collaboration, context) {
    forEach(collaboration.participants, contextual(handleParticipant, context));
    handleArtifacts(collaboration.artifacts, context);
    deferred.push(function() {
      handleMessageFlows(collaboration.messageFlows, context);
    });
  }
  function wireFlowNodeRefs(lane) {
    forEach(lane.flowNodeRef, function(flowNode) {
      var lanes = flowNode.get("lanes");
      if (lanes) {
        lanes.push(lane);
      }
    });
  }
}

// node_modules/bpmn-js/lib/import/Importer.js
function importBpmnDiagram(diagram, definitions, bpmnDiagram) {
  var importer, eventBus, canvas;
  var error3, warnings = [];
  function render(definitions2, bpmnDiagram2) {
    var visitor = {
      root: function(element, di) {
        return importer.add(element, di);
      },
      element: function(element, di, parentShape) {
        return importer.add(element, di, parentShape);
      },
      error: function(message, context) {
        warnings.push({ message, context });
      }
    };
    var walker = new BpmnTreeWalker(visitor);
    bpmnDiagram2 = bpmnDiagram2 || definitions2.diagrams && definitions2.diagrams[0];
    var diagramsToImport = getDiagramsToImport(definitions2, bpmnDiagram2);
    if (!diagramsToImport) {
      throw new Error("no diagram to display");
    }
    forEach(diagramsToImport, function(diagram2) {
      walker.handleDefinitions(definitions2, diagram2);
    });
    var rootId = bpmnDiagram2.plane.bpmnElement.id;
    canvas.setRootElement(
      canvas.findRoot(rootId + "_plane") || canvas.findRoot(rootId)
    );
  }
  return new Promise(function(resolve, reject) {
    try {
      importer = diagram.get("bpmnImporter");
      eventBus = diagram.get("eventBus");
      canvas = diagram.get("canvas");
      eventBus.fire("import.render.start", { definitions });
      render(definitions, bpmnDiagram);
      eventBus.fire("import.render.complete", {
        error: error3,
        warnings
      });
      return resolve({ warnings });
    } catch (e2) {
      e2.warnings = warnings;
      return reject(e2);
    }
  });
}
function getDiagramsToImport(definitions, bpmnDiagram) {
  if (!bpmnDiagram || !bpmnDiagram.plane) {
    return;
  }
  var bpmnElement = bpmnDiagram.plane.bpmnElement, rootElement = bpmnElement;
  if (!is(bpmnElement, "bpmn:Process") && !is(bpmnElement, "bpmn:Collaboration")) {
    rootElement = findRootProcess(bpmnElement);
  }
  var collaboration;
  if (is(rootElement, "bpmn:Collaboration")) {
    collaboration = rootElement;
  } else {
    collaboration = find(definitions.rootElements, function(element) {
      if (!is(element, "bpmn:Collaboration")) {
        return;
      }
      return find(element.participants, function(participant) {
        return participant.processRef === rootElement;
      });
    });
  }
  var rootElements = [rootElement];
  if (collaboration) {
    rootElements = map(collaboration.participants, function(participant) {
      return participant.processRef;
    });
    rootElements.push(collaboration);
  }
  var allChildren = selfAndAllFlowElements(rootElements);
  var diagramsToImport = [bpmnDiagram];
  var handledElements = [bpmnElement];
  forEach(definitions.diagrams, function(diagram) {
    if (!diagram.plane) {
      return;
    }
    var businessObject = diagram.plane.bpmnElement;
    if (allChildren.indexOf(businessObject) !== -1 && handledElements.indexOf(businessObject) === -1) {
      diagramsToImport.push(diagram);
      handledElements.push(businessObject);
    }
  });
  return diagramsToImport;
}
function selfAndAllFlowElements(elements) {
  var result = [];
  forEach(elements, function(element) {
    if (!element) {
      return;
    }
    result.push(element);
    result = result.concat(selfAndAllFlowElements(element.flowElements));
  });
  return result;
}
function findRootProcess(element) {
  var parent = element;
  while (parent) {
    if (is(parent, "bpmn:Process")) {
      return parent;
    }
    parent = parent.$parent;
  }
}

// node_modules/bpmn-js/lib/util/PoweredByUtil.js
var BPMNIO_LOGO_SVG = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14.02 5.57" width="53" height="21"><path fill="currentColor" d="M1.88.92v.14c0 .41-.13.68-.4.8.33.14.46.44.46.86v.33c0 .61-.33.95-.95.95H0V0h.95c.65 0 .93.3.93.92zM.63.57v1.06h.24c.24 0 .38-.1.38-.43V.98c0-.28-.1-.4-.32-.4zm0 1.63v1.22h.36c.2 0 .32-.1.32-.39v-.35c0-.37-.12-.48-.4-.48H.63zM4.18.99v.52c0 .64-.31.98-.94.98h-.3V4h-.62V0h.92c.63 0 .94.35.94.99zM2.94.57v1.35h.3c.2 0 .3-.09.3-.37v-.6c0-.29-.1-.38-.3-.38h-.3zm2.89 2.27L6.25 0h.88v4h-.6V1.12L6.1 3.99h-.6l-.46-2.82v2.82h-.55V0h.87zM8.14 1.1V4h-.56V0h.79L9 2.4V0h.56v4h-.64zm2.49 2.29v.6h-.6v-.6zM12.12 1c0-.63.33-1 .95-1 .61 0 .95.37.95 1v2.04c0 .64-.34 1-.95 1-.62 0-.95-.37-.95-1zm.62 2.08c0 .28.13.39.33.39s.32-.1.32-.4V.98c0-.29-.12-.4-.32-.4s-.33.11-.33.4z"/><path fill="currentColor" d="M0 4.53h14.02v1.04H0zM11.08 0h.63v.62h-.63zm.63 4V1h-.63v2.98z"/></svg>';
var BPMNIO_IMG = BPMNIO_LOGO_SVG;
var LOGO_STYLES = {
  verticalAlign: "middle"
};
var LINK_STYLES = {
  "color": "#404040"
};
var LIGHTBOX_STYLES = {
  "zIndex": "1001",
  "position": "fixed",
  "top": "0",
  "left": "0",
  "right": "0",
  "bottom": "0"
};
var BACKDROP_STYLES = {
  "width": "100%",
  "height": "100%",
  "background": "rgba(40,40,40,0.2)"
};
var NOTICE_STYLES = {
  "position": "absolute",
  "left": "50%",
  "top": "40%",
  "transform": "translate(-50%)",
  "width": "260px",
  "padding": "10px",
  "background": "white",
  "boxShadow": "0 1px 4px rgba(0,0,0,0.3)",
  "fontFamily": "Helvetica, Arial, sans-serif",
  "fontSize": "14px",
  "display": "flex",
  "lineHeight": "1.3"
};
var LIGHTBOX_MARKUP = '<div class="bjs-powered-by-lightbox"><div class="backdrop"></div><div class="notice"><a href="https://bpmn.io" target="_blank" rel="noopener" class="link">' + BPMNIO_IMG + '</a><span>Web-based tooling for BPMN, DMN and forms powered by <a href="https://bpmn.io" target="_blank" rel="noopener">bpmn.io</a>.</span></div></div>';
var lightbox;
function createLightbox() {
  lightbox = domify$1(LIGHTBOX_MARKUP);
  assign2(lightbox, LIGHTBOX_STYLES);
  assign2(query("svg", lightbox), LOGO_STYLES);
  assign2(query(".backdrop", lightbox), BACKDROP_STYLES);
  assign2(query(".notice", lightbox), NOTICE_STYLES);
  assign2(query(".link", lightbox), LINK_STYLES, {
    "margin": "15px 20px 15px 10px",
    "alignSelf": "center"
  });
}
function open() {
  if (!lightbox) {
    createLightbox();
    delegate.bind(lightbox, ".backdrop", "click", function(event2) {
      document.body.removeChild(lightbox);
    });
  }
  document.body.appendChild(lightbox);
}

// node_modules/bpmn-js/lib/BaseViewer.js
function BaseViewer(options) {
  options = assign({}, DEFAULT_OPTIONS, options);
  this._moddle = this._createModdle(options);
  this._container = this._createContainer(options);
  addProjectLogo(this._container);
  this._init(this._container, this._moddle, options);
}
e(BaseViewer, Diagram);
BaseViewer.prototype.importXML = async function importXML(xml2, bpmnDiagram) {
  const self = this;
  function ParseCompleteEvent(data) {
    return self.get("eventBus").createEvent(data);
  }
  let aggregatedWarnings = [];
  try {
    xml2 = this._emit("import.parse.start", { xml: xml2 }) || xml2;
    let parseResult;
    try {
      parseResult = await this._moddle.fromXML(xml2, "bpmn:Definitions");
    } catch (error3) {
      this._emit("import.parse.complete", {
        error: error3
      });
      throw error3;
    }
    let definitions = parseResult.rootElement;
    const references = parseResult.references;
    const parseWarnings = parseResult.warnings;
    const elementsById = parseResult.elementsById;
    aggregatedWarnings = aggregatedWarnings.concat(parseWarnings);
    definitions = this._emit("import.parse.complete", ParseCompleteEvent({
      error: null,
      definitions,
      elementsById,
      references,
      warnings: aggregatedWarnings
    })) || definitions;
    const importResult = await this.importDefinitions(definitions, bpmnDiagram);
    aggregatedWarnings = aggregatedWarnings.concat(importResult.warnings);
    this._emit("import.done", { error: null, warnings: aggregatedWarnings });
    return { warnings: aggregatedWarnings };
  } catch (err) {
    let error3 = err;
    aggregatedWarnings = aggregatedWarnings.concat(error3.warnings || []);
    addWarningsToError(error3, aggregatedWarnings);
    error3 = checkValidationError(error3);
    this._emit("import.done", { error: error3, warnings: error3.warnings });
    throw error3;
  }
};
BaseViewer.prototype.importDefinitions = async function importDefinitions(definitions, bpmnDiagram) {
  this._setDefinitions(definitions);
  const result = await this.open(bpmnDiagram);
  return { warnings: result.warnings };
};
BaseViewer.prototype.open = async function open2(bpmnDiagramOrId) {
  const definitions = this._definitions;
  let bpmnDiagram = bpmnDiagramOrId;
  if (!definitions) {
    const error3 = new Error("no XML imported");
    addWarningsToError(error3, []);
    throw error3;
  }
  if (typeof bpmnDiagramOrId === "string") {
    bpmnDiagram = findBPMNDiagram(definitions, bpmnDiagramOrId);
    if (!bpmnDiagram) {
      const error3 = new Error("BPMNDiagram <" + bpmnDiagramOrId + "> not found");
      addWarningsToError(error3, []);
      throw error3;
    }
  }
  try {
    this.clear();
  } catch (error3) {
    addWarningsToError(error3, []);
    throw error3;
  }
  const { warnings } = await importBpmnDiagram(this, definitions, bpmnDiagram);
  return { warnings };
};
BaseViewer.prototype.saveXML = async function saveXML(options) {
  options = options || {};
  let definitions = this._definitions, error3, xml2;
  try {
    if (!definitions) {
      throw new Error("no definitions loaded");
    }
    definitions = this._emit("saveXML.start", {
      definitions
    }) || definitions;
    const result2 = await this._moddle.toXML(definitions, options);
    xml2 = result2.xml;
    xml2 = this._emit("saveXML.serialized", {
      xml: xml2
    }) || xml2;
  } catch (err) {
    error3 = err;
  }
  const result = error3 ? { error: error3 } : { xml: xml2 };
  this._emit("saveXML.done", result);
  if (error3) {
    throw error3;
  }
  return result;
};
BaseViewer.prototype.saveSVG = async function saveSVG() {
  this._emit("saveSVG.start");
  let svg, err;
  try {
    const canvas = this.get("canvas");
    const contentNode = canvas.getActiveLayer(), defsNode = query(":scope > defs", canvas._svg);
    const contents = innerSVG(contentNode), defs = defsNode ? "<defs>" + innerSVG(defsNode) + "</defs>" : "";
    const bbox = contentNode.getBBox();
    svg = '<?xml version="1.0" encoding="utf-8"?>\n<!-- created with bpmn-js / http://bpmn.io -->\n<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">\n<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="' + bbox.width + '" height="' + bbox.height + '" viewBox="' + bbox.x + " " + bbox.y + " " + bbox.width + " " + bbox.height + '" version="1.1">' + defs + contents + "</svg>";
  } catch (e2) {
    err = e2;
  }
  this._emit("saveSVG.done", {
    error: err,
    svg
  });
  if (err) {
    throw err;
  }
  return { svg };
};
BaseViewer.prototype._setDefinitions = function(definitions) {
  this._definitions = definitions;
};
BaseViewer.prototype.getModules = function() {
  return this._modules;
};
BaseViewer.prototype.clear = function() {
  if (!this.getDefinitions()) {
    return;
  }
  Diagram.prototype.clear.call(this);
};
BaseViewer.prototype.destroy = function() {
  Diagram.prototype.destroy.call(this);
  remove2(this._container);
};
BaseViewer.prototype.on = function(events, priority, callback, that) {
  return this.get("eventBus").on(events, priority, callback, that);
};
BaseViewer.prototype.off = function(events, callback) {
  this.get("eventBus").off(events, callback);
};
BaseViewer.prototype.attachTo = function(parentNode) {
  if (!parentNode) {
    throw new Error("parentNode required");
  }
  this.detach();
  if (parentNode.get && parentNode.constructor.prototype.jquery) {
    parentNode = parentNode.get(0);
  }
  if (typeof parentNode === "string") {
    parentNode = query(parentNode);
  }
  parentNode.appendChild(this._container);
  this._emit("attach", {});
  this.get("canvas").resized();
};
BaseViewer.prototype.getDefinitions = function() {
  return this._definitions;
};
BaseViewer.prototype.detach = function() {
  const container = this._container, parentNode = container.parentNode;
  if (!parentNode) {
    return;
  }
  this._emit("detach", {});
  parentNode.removeChild(container);
};
BaseViewer.prototype._init = function(container, moddle, options) {
  const baseModules = options.modules || this.getModules(options), additionalModules = options.additionalModules || [], staticModules = [
    {
      bpmnjs: ["value", this],
      moddle: ["value", moddle]
    }
  ];
  const diagramModules = [].concat(staticModules, baseModules, additionalModules);
  const diagramOptions = assign(omit(options, ["additionalModules"]), {
    canvas: assign({}, options.canvas, { container }),
    modules: diagramModules
  });
  Diagram.call(this, diagramOptions);
  if (options && options.container) {
    this.attachTo(options.container);
  }
};
BaseViewer.prototype._emit = function(type, event2) {
  return this.get("eventBus").fire(type, event2);
};
BaseViewer.prototype._createContainer = function(options) {
  const container = domify$1('<div class="bjs-container"></div>');
  assign2(container, {
    width: ensureUnit(options.width),
    height: ensureUnit(options.height),
    position: options.position
  });
  return container;
};
BaseViewer.prototype._createModdle = function(options) {
  const moddleOptions = assign({}, this._moddleExtensions, options.moddleExtensions);
  return new simple(moddleOptions);
};
BaseViewer.prototype._modules = [];
function addWarningsToError(err, warningsAry) {
  err.warnings = warningsAry;
  return err;
}
function checkValidationError(err) {
  const pattern = /unparsable content <([^>]+)> detected([\s\S]*)$/;
  const match = pattern.exec(err.message);
  if (match) {
    err.message = "unparsable content <" + match[1] + "> detected; this may indicate an invalid BPMN 2.0 diagram file" + match[2];
  }
  return err;
}
var DEFAULT_OPTIONS = {
  width: "100%",
  height: "100%",
  position: "relative"
};
function ensureUnit(val) {
  return val + (isNumber(val) ? "px" : "");
}
function findBPMNDiagram(definitions, diagramId) {
  if (!diagramId) {
    return null;
  }
  return find(definitions.diagrams, function(element) {
    return element.id === diagramId;
  }) || null;
}
function addProjectLogo(container) {
  const img = BPMNIO_IMG;
  const linkMarkup = '<a href="http://bpmn.io" target="_blank" class="bjs-powered-by" title="Powered by bpmn.io" >' + img + "</a>";
  const linkElement = domify$1(linkMarkup);
  assign2(query("svg", linkElement), LOGO_STYLES);
  assign2(linkElement, LINK_STYLES, {
    position: "absolute",
    bottom: "15px",
    right: "15px",
    zIndex: "100"
  });
  container.appendChild(linkElement);
  event.bind(linkElement, "click", function(event2) {
    open();
    event2.preventDefault();
  });
}

// node_modules/bpmn-js/lib/Viewer.js
function Viewer(options) {
  BaseViewer.call(this, options);
}
e(Viewer, BaseViewer);
Viewer.prototype._modules = [
  core_default,
  drilldown_default,
  outline_default2,
  overlays_default,
  selection_default,
  translate_default
];
Viewer.prototype._moddleExtensions = {};

export {
  e,
  isArray,
  isFunction,
  bind,
  assign,
  classes2 as classes,
  closest,
  event,
  matches,
  toPoint,
  isMac,
  Viewer
};
//# sourceMappingURL=chunk-2XMDE5M3.js.map
