import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { 
  ArrowLeft, 
  Play, 
  Pause, 
  Square, 
  RefreshCw, 
  Clock, 
  User, 
  Key,
  Activity,
  CheckCircle,
  AlertCircle,
  Eye
} from 'lucide-react';
import { workflowApi } from '../services/workflowApi';
import { ProcessInstanceStatus } from '../types/workflow';

const ProcessInstanceDetails: React.FC = () => {
  const { processInstanceId } = useParams<{ processInstanceId: string }>();
  const [instanceStatus, setInstanceStatus] = useState<ProcessInstanceStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    if (processInstanceId) {
      loadInstanceStatus();
    }
  }, [processInstanceId]);

  const loadInstanceStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      const status = await workflowApi.getProcessInstanceStatus(processInstanceId!);
      setInstanceStatus(status);
    } catch (err) {
      console.error('Error loading instance status:', err);
      setError(err instanceof Error ? err.message : 'Failed to load instance status');
    } finally {
      setLoading(false);
    }
  };

  const handleSuspend = async () => {
    if (!processInstanceId) return;
    
    try {
      setActionLoading('suspend');
      await workflowApi.suspendProcessInstance(processInstanceId);
      await loadInstanceStatus();
    } catch (err) {
      console.error('Error suspending instance:', err);
    } finally {
      setActionLoading(null);
    }
  };

  const handleActivate = async () => {
    if (!processInstanceId) return;
    
    try {
      setActionLoading('activate');
      await workflowApi.activateProcessInstance(processInstanceId);
      await loadInstanceStatus();
    } catch (err) {
      console.error('Error activating instance:', err);
    } finally {
      setActionLoading(null);
    }
  };

  const handleDelete = async () => {
    if (!processInstanceId) return;
    
    if (window.confirm('Are you sure you want to delete this process instance? This action cannot be undone.')) {
      try {
        setActionLoading('delete');
        await workflowApi.deleteProcessInstance(processInstanceId, 'Deleted via BPMN Viewer');
        // Redirect to dashboard after deletion
        window.location.href = '/';
      } catch (err) {
        console.error('Error deleting instance:', err);
        setActionLoading(null);
      }
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <Activity className="w-5 h-5 text-green-500" />;
      case 'COMPLETED':
        return <CheckCircle className="w-5 h-5 text-blue-500" />;
      case 'SUSPENDED':
        return <Pause className="w-5 h-5 text-yellow-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'COMPLETED':
        return 'bg-blue-100 text-blue-800';
      case 'SUSPENDED':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDuration = (startTime: string, endTime?: string) => {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const durationMs = end.getTime() - start.getTime();
    
    const seconds = Math.floor(durationMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  const renderVariableValue = (value: any): string => {
    if (value === null || value === undefined) return 'null';
    if (typeof value === 'object') return JSON.stringify(value, null, 2);
    return String(value);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <Link
                  to="/"
                  className="text-sm font-medium text-red-800 hover:text-red-600"
                >
                  ← Back to Dashboard
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!instanceStatus) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-gray-500">Process instance not found</p>
          <Link
            to="/"
            className="text-blue-600 hover:text-blue-800 mt-2 inline-block"
          >
            ← Back to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link
              to="/"
              className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 mr-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Process Instance Details</h1>
              <p className="text-sm text-gray-500">ID: {instanceStatus.id}</p>
            </div>
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={loadInstanceStatus}
              disabled={loading}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            
            {instanceStatus.status === 'ACTIVE' && (
              <>
                <button
                  onClick={handleSuspend}
                  disabled={actionLoading === 'suspend'}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 disabled:opacity-50"
                >
                  <Pause className="w-4 h-4 mr-2" />
                  {actionLoading === 'suspend' ? 'Suspending...' : 'Suspend'}
                </button>
                <button
                  onClick={handleDelete}
                  disabled={actionLoading === 'delete'}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 disabled:opacity-50"
                >
                  <Square className="w-4 h-4 mr-2" />
                  {actionLoading === 'delete' ? 'Deleting...' : 'Delete'}
                </button>
              </>
            )}
            
            {instanceStatus.status === 'SUSPENDED' && (
              <button
                onClick={handleActivate}
                disabled={actionLoading === 'activate'}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
              >
                <Play className="w-4 h-4 mr-2" />
                {actionLoading === 'activate' ? 'Activating...' : 'Activate'}
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Instance Overview */}
        <div className="lg:col-span-2 space-y-6">
          {/* Status Card */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-medium text-gray-900">Instance Status</h2>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(instanceStatus.status)}`}>
                  {getStatusIcon(instanceStatus.status)}
                  <span className="ml-2">{instanceStatus.status}</span>
                </span>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center">
                  <Key className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Process Key</p>
                    <p className="text-sm text-gray-500">{instanceStatus.processDefinitionKey}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Clock className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Started</p>
                    <p className="text-sm text-gray-500">
                      {new Date(instanceStatus.startTime).toLocaleString()}
                    </p>
                  </div>
                </div>
                
                {instanceStatus.endTime && (
                  <div className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Ended</p>
                      <p className="text-sm text-gray-500">
                        {new Date(instanceStatus.endTime).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}
                
                <div className="flex items-center">
                  <Clock className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Duration</p>
                    <p className="text-sm text-gray-500">
                      {formatDuration(instanceStatus.startTime, instanceStatus.endTime)}
                    </p>
                  </div>
                </div>
                
                {instanceStatus.businessKey && (
                  <div className="flex items-center">
                    <Key className="w-5 h-5 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Business Key</p>
                      <p className="text-sm text-gray-500">{instanceStatus.businessKey}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Current Activities */}
          {instanceStatus.currentActivities.length > 0 && (
            <div className="bg-white rounded-lg shadow">
              <div className="p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Current Activities</h2>
                <div className="space-y-2">
                  {instanceStatus.currentActivities.map((activity, index) => (
                    <div key={index} className="flex items-center p-3 bg-green-50 border border-green-200 rounded-md">
                      <Activity className="w-5 h-5 text-green-500 mr-3" />
                      <span className="text-sm font-medium text-green-800">{activity}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Process Variables */}
          {Object.keys(instanceStatus.variables).length > 0 && (
            <div className="bg-white rounded-lg shadow">
              <div className="p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Process Variables</h2>
                <div className="space-y-3">
                  {Object.entries(instanceStatus.variables).map(([key, value]) => (
                    <div key={key} className="border-b border-gray-200 pb-3 last:border-b-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">{key}</p>
                          <div className="mt-1">
                            {typeof value === 'object' ? (
                              <pre className="text-xs text-gray-600 bg-gray-50 p-2 rounded overflow-x-auto">
                                {renderVariableValue(value)}
                              </pre>
                            ) : (
                              <p className="text-sm text-gray-600">{renderVariableValue(value)}</p>
                            )}
                          </div>
                        </div>
                        <span className="text-xs text-gray-400 ml-4">
                          {typeof value}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Actions Panel */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow">
            <div className="p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Actions</h2>
              
              <div className="space-y-3">
                <Link
                  to={`/viewer/${instanceStatus.processDefinitionKey}`}
                  className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  View BPMN Diagram
                </Link>
                
                {instanceStatus.status === 'ACTIVE' && (
                  <>
                    <button
                      onClick={handleSuspend}
                      disabled={actionLoading === 'suspend'}
                      className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 disabled:opacity-50"
                    >
                      <Pause className="w-4 h-4 mr-2" />
                      {actionLoading === 'suspend' ? 'Suspending...' : 'Suspend Process'}
                    </button>
                    
                    <button
                      onClick={handleDelete}
                      disabled={actionLoading === 'delete'}
                      className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 disabled:opacity-50"
                    >
                      <Square className="w-4 h-4 mr-2" />
                      {actionLoading === 'delete' ? 'Deleting...' : 'Delete Process'}
                    </button>
                  </>
                )}
                
                {instanceStatus.status === 'SUSPENDED' && (
                  <button
                    onClick={handleActivate}
                    disabled={actionLoading === 'activate'}
                    className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    {actionLoading === 'activate' ? 'Activating...' : 'Activate Process'}
                  </button>
                )}
              </div>
              
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Quick Info</h3>
                <div className="space-y-2 text-sm text-gray-600">
                  <p>Instance ID: <span className="font-mono text-xs">{instanceStatus.id}</span></p>
                  <p>Status: <span className="font-medium">{instanceStatus.status}</span></p>
                  <p>Active Tasks: <span className="font-medium">{instanceStatus.currentActivities.length}</span></p>
                  <p>Variables: <span className="font-medium">{Object.keys(instanceStatus.variables).length}</span></p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProcessInstanceDetails;