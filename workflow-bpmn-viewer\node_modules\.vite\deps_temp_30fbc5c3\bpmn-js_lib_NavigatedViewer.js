import {
  Viewer,
  assign,
  bind,
  classes,
  closest,
  e,
  event,
  isArray,
  isFunction,
  isMac,
  matches,
  toPoint
} from "./chunk-2XMDE5M3.js";
import "./chunk-G3PMV62Z.js";

// node_modules/diagram-js/lib/features/keyboard/KeyboardUtil.js
var KEYS_COPY = ["c", "C"];
var KEYS_PASTE = ["v", "V"];
var KEYS_REDO = ["y", "Y"];
var KEYS_UNDO = ["z", "Z"];
function hasModifier(event2) {
  return event2.ctrlKey || event2.metaKey || event2.shiftKey || event2.altKey;
}
function isCmd(event2) {
  if (event2.altKey) {
    return false;
  }
  return event2.ctrlKey || event2.metaKey;
}
function isKey(keys, event2) {
  keys = isArray(keys) ? keys : [keys];
  return keys.indexOf(event2.key) !== -1 || keys.indexOf(event2.code) !== -1;
}
function isShift(event2) {
  return event2.shiftKey;
}
function isCopy(event2) {
  return isCmd(event2) && isKey(KEYS_COPY, event2);
}
function isPaste(event2) {
  return isCmd(event2) && isKey(KEYS_PASTE, event2);
}
function isUndo(event2) {
  return isCmd(event2) && !isShift(event2) && isKey(KEYS_UNDO, event2);
}
function isRedo(event2) {
  return isCmd(event2) && (isKey(KEYS_REDO, event2) || isKey(KEYS_UNDO, event2) && isShift(event2));
}

// node_modules/diagram-js/lib/features/keyboard/Keyboard.js
var KEYDOWN_EVENT = "keyboard.keydown";
var KEYUP_EVENT = "keyboard.keyup";
var HANDLE_MODIFIER_ATTRIBUTE = "input-handle-modified-keys";
var DEFAULT_PRIORITY = 1e3;
function Keyboard(config, eventBus) {
  var self = this;
  this._config = config || {};
  this._eventBus = eventBus;
  this._keydownHandler = this._keydownHandler.bind(this);
  this._keyupHandler = this._keyupHandler.bind(this);
  eventBus.on("diagram.destroy", function() {
    self._fire("destroy");
    self.unbind();
  });
  eventBus.on("diagram.init", function() {
    self._fire("init");
  });
  eventBus.on("attach", function() {
    if (config && config.bindTo) {
      self.bind(config.bindTo);
    }
  });
  eventBus.on("detach", function() {
    self.unbind();
  });
}
Keyboard.$inject = [
  "config.keyboard",
  "eventBus"
];
Keyboard.prototype._keydownHandler = function(event2) {
  this._keyHandler(event2, KEYDOWN_EVENT);
};
Keyboard.prototype._keyupHandler = function(event2) {
  this._keyHandler(event2, KEYUP_EVENT);
};
Keyboard.prototype._keyHandler = function(event2, type) {
  var eventBusResult;
  if (this._isEventIgnored(event2)) {
    return;
  }
  var context = {
    keyEvent: event2
  };
  eventBusResult = this._eventBus.fire(type || KEYDOWN_EVENT, context);
  if (eventBusResult) {
    event2.preventDefault();
  }
};
Keyboard.prototype._isEventIgnored = function(event2) {
  if (event2.defaultPrevented) {
    return true;
  }
  return (isInput(event2.target) || isButton(event2.target) && isKey([" ", "Enter"], event2)) && this._isModifiedKeyIgnored(event2);
};
Keyboard.prototype._isModifiedKeyIgnored = function(event2) {
  if (!isCmd(event2)) {
    return true;
  }
  var allowedModifiers = this._getAllowedModifiers(event2.target);
  return allowedModifiers.indexOf(event2.key) === -1;
};
Keyboard.prototype._getAllowedModifiers = function(element) {
  var modifierContainer = closest(element, "[" + HANDLE_MODIFIER_ATTRIBUTE + "]", true);
  if (!modifierContainer || this._node && !this._node.contains(modifierContainer)) {
    return [];
  }
  return modifierContainer.getAttribute(HANDLE_MODIFIER_ATTRIBUTE).split(",");
};
Keyboard.prototype.bind = function(node) {
  this.unbind();
  this._node = node;
  event.bind(node, "keydown", this._keydownHandler);
  event.bind(node, "keyup", this._keyupHandler);
  this._fire("bind");
};
Keyboard.prototype.getBinding = function() {
  return this._node;
};
Keyboard.prototype.unbind = function() {
  var node = this._node;
  if (node) {
    this._fire("unbind");
    event.unbind(node, "keydown", this._keydownHandler);
    event.unbind(node, "keyup", this._keyupHandler);
  }
  this._node = null;
};
Keyboard.prototype._fire = function(event2) {
  this._eventBus.fire("keyboard." + event2, { node: this._node });
};
Keyboard.prototype.addListener = function(priority, listener, type) {
  if (isFunction(priority)) {
    type = listener;
    listener = priority;
    priority = DEFAULT_PRIORITY;
  }
  this._eventBus.on(type || KEYDOWN_EVENT, priority, listener);
};
Keyboard.prototype.removeListener = function(listener, type) {
  this._eventBus.off(type || KEYDOWN_EVENT, listener);
};
Keyboard.prototype.hasModifier = hasModifier;
Keyboard.prototype.isCmd = isCmd;
Keyboard.prototype.isShift = isShift;
Keyboard.prototype.isKey = isKey;
function isInput(target) {
  return target && (matches(target, "input, textarea") || target.contentEditable === "true");
}
function isButton(target) {
  return target && matches(target, "button, input[type=submit], input[type=button], a[href], [aria-role=button]");
}

// node_modules/diagram-js/lib/features/keyboard/KeyboardBindings.js
var LOW_PRIORITY = 500;
function KeyboardBindings(eventBus, keyboard) {
  var self = this;
  eventBus.on("editorActions.init", LOW_PRIORITY, function(event2) {
    var editorActions = event2.editorActions;
    self.registerBindings(keyboard, editorActions);
  });
}
KeyboardBindings.$inject = [
  "eventBus",
  "keyboard"
];
KeyboardBindings.prototype.registerBindings = function(keyboard, editorActions) {
  function addListener(action, fn) {
    if (editorActions.isRegistered(action)) {
      keyboard.addListener(fn);
    }
  }
  addListener("undo", function(context) {
    var event2 = context.keyEvent;
    if (isUndo(event2)) {
      editorActions.trigger("undo");
      return true;
    }
  });
  addListener("redo", function(context) {
    var event2 = context.keyEvent;
    if (isRedo(event2)) {
      editorActions.trigger("redo");
      return true;
    }
  });
  addListener("copy", function(context) {
    var event2 = context.keyEvent;
    if (isCopy(event2)) {
      editorActions.trigger("copy");
      return true;
    }
  });
  addListener("paste", function(context) {
    var event2 = context.keyEvent;
    if (isPaste(event2)) {
      editorActions.trigger("paste");
      return true;
    }
  });
  addListener("stepZoom", function(context) {
    var event2 = context.keyEvent;
    if (isKey(["+", "Add", "="], event2) && isCmd(event2)) {
      editorActions.trigger("stepZoom", { value: 1 });
      return true;
    }
  });
  addListener("stepZoom", function(context) {
    var event2 = context.keyEvent;
    if (isKey(["-", "Subtract"], event2) && isCmd(event2)) {
      editorActions.trigger("stepZoom", { value: -1 });
      return true;
    }
  });
  addListener("zoom", function(context) {
    var event2 = context.keyEvent;
    if (isKey("0", event2) && isCmd(event2)) {
      editorActions.trigger("zoom", { value: 1 });
      return true;
    }
  });
  addListener("removeSelection", function(context) {
    var event2 = context.keyEvent;
    if (isKey(["Backspace", "Delete", "Del"], event2)) {
      editorActions.trigger("removeSelection");
      return true;
    }
  });
};

// node_modules/diagram-js/lib/features/keyboard/index.js
var keyboard_default = {
  __init__: ["keyboard", "keyboardBindings"],
  keyboard: ["type", Keyboard],
  keyboardBindings: ["type", KeyboardBindings]
};

// node_modules/diagram-js/lib/navigation/keyboard-move/KeyboardMove.js
var DEFAULT_CONFIG = {
  moveSpeed: 50,
  moveSpeedAccelerated: 200
};
function KeyboardMove(config, keyboard, canvas) {
  var self = this;
  this._config = assign({}, DEFAULT_CONFIG, config || {});
  keyboard.addListener(arrowsListener);
  function arrowsListener(context) {
    var event2 = context.keyEvent, config2 = self._config;
    if (!keyboard.isCmd(event2)) {
      return;
    }
    if (keyboard.isKey([
      "ArrowLeft",
      "Left",
      "ArrowUp",
      "Up",
      "ArrowDown",
      "Down",
      "ArrowRight",
      "Right"
    ], event2)) {
      var speed = keyboard.isShift(event2) ? config2.moveSpeedAccelerated : config2.moveSpeed;
      var direction;
      switch (event2.key) {
        case "ArrowLeft":
        case "Left":
          direction = "left";
          break;
        case "ArrowUp":
        case "Up":
          direction = "up";
          break;
        case "ArrowRight":
        case "Right":
          direction = "right";
          break;
        case "ArrowDown":
        case "Down":
          direction = "down";
          break;
      }
      self.moveCanvas({
        speed,
        direction
      });
      return true;
    }
  }
  this.moveCanvas = function(options) {
    var dx = 0, dy = 0, speed = options.speed;
    var actualSpeed = speed / Math.min(Math.sqrt(canvas.viewbox().scale), 1);
    switch (options.direction) {
      case "left":
        dx = actualSpeed;
        break;
      case "up":
        dy = actualSpeed;
        break;
      case "right":
        dx = -actualSpeed;
        break;
      case "down":
        dy = -actualSpeed;
        break;
    }
    canvas.scroll({
      dx,
      dy
    });
  };
}
KeyboardMove.$inject = [
  "config.keyboardMove",
  "keyboard",
  "canvas"
];

// node_modules/diagram-js/lib/navigation/keyboard-move/index.js
var keyboard_move_default = {
  __depends__: [
    keyboard_default
  ],
  __init__: ["keyboardMove"],
  keyboardMove: ["type", KeyboardMove]
};

// node_modules/diagram-js/lib/util/Cursor.js
var CURSOR_CLS_PATTERN = /^djs-cursor-.*$/;
function set(mode) {
  var classes2 = classes(document.body);
  classes2.removeMatching(CURSOR_CLS_PATTERN);
  if (mode) {
    classes2.add("djs-cursor-" + mode);
  }
}
function unset() {
  set(null);
}

// node_modules/diagram-js/lib/util/ClickTrap.js
var TRAP_PRIORITY = 5e3;
function install(eventBus, eventName) {
  eventName = eventName || "element.click";
  function trap() {
    return false;
  }
  eventBus.once(eventName, TRAP_PRIORITY, trap);
  return function() {
    eventBus.off(eventName, trap);
  };
}

// node_modules/diagram-js/lib/util/PositionUtil.js
function delta(a, b) {
  return {
    x: a.x - b.x,
    y: a.y - b.y
  };
}

// node_modules/diagram-js/lib/navigation/movecanvas/MoveCanvas.js
var THRESHOLD = 15;
function MoveCanvas(eventBus, canvas) {
  var context;
  eventBus.on("element.mousedown", 500, function(e2) {
    return handleStart(e2.originalEvent);
  });
  function handleMove(event2) {
    var start = context.start, button = context.button, position = toPoint(event2), delta2 = delta(position, start);
    if (!context.dragging && length(delta2) > THRESHOLD) {
      context.dragging = true;
      if (button === 0) {
        install(eventBus);
      }
      set("grab");
    }
    if (context.dragging) {
      var lastPosition = context.last || context.start;
      delta2 = delta(position, lastPosition);
      canvas.scroll({
        dx: delta2.x,
        dy: delta2.y
      });
      context.last = position;
    }
    event2.preventDefault();
  }
  function handleEnd(event2) {
    event.unbind(document, "mousemove", handleMove);
    event.unbind(document, "mouseup", handleEnd);
    context = null;
    unset();
  }
  function handleStart(event2) {
    if (closest(event2.target, ".djs-draggable")) {
      return;
    }
    var button = event2.button;
    if (button >= 2 || event2.ctrlKey || event2.shiftKey || event2.altKey) {
      return;
    }
    context = {
      button,
      start: toPoint(event2)
    };
    event.bind(document, "mousemove", handleMove);
    event.bind(document, "mouseup", handleEnd);
    return true;
  }
  this.isActive = function() {
    return !!context;
  };
}
MoveCanvas.$inject = [
  "eventBus",
  "canvas"
];
function length(point) {
  return Math.sqrt(Math.pow(point.x, 2) + Math.pow(point.y, 2));
}

// node_modules/diagram-js/lib/navigation/movecanvas/index.js
var movecanvas_default = {
  __init__: ["moveCanvas"],
  moveCanvas: ["type", MoveCanvas]
};

// node_modules/diagram-js/lib/util/Math.js
function log10(x) {
  return Math.log(x) / Math.log(10);
}

// node_modules/diagram-js/lib/navigation/zoomscroll/ZoomUtil.js
function getStepSize(range, steps) {
  var minLinearRange = log10(range.min), maxLinearRange = log10(range.max);
  var absoluteLinearRange = Math.abs(minLinearRange) + Math.abs(maxLinearRange);
  return absoluteLinearRange / steps;
}
function cap(range, scale) {
  return Math.max(range.min, Math.min(range.max, scale));
}

// node_modules/diagram-js/lib/navigation/zoomscroll/ZoomScroll.js
var sign = Math.sign || function(n) {
  return n >= 0 ? 1 : -1;
};
var RANGE = { min: 0.2, max: 4 };
var NUM_STEPS = 10;
var DELTA_THRESHOLD = 0.1;
var DEFAULT_SCALE = 0.75;
function ZoomScroll(config, eventBus, canvas) {
  config = config || {};
  this._enabled = false;
  this._canvas = canvas;
  this._container = canvas._container;
  this._handleWheel = bind(this._handleWheel, this);
  this._totalDelta = 0;
  this._scale = config.scale || DEFAULT_SCALE;
  var self = this;
  eventBus.on("canvas.init", function(e2) {
    self._init(config.enabled !== false);
  });
}
ZoomScroll.$inject = [
  "config.zoomScroll",
  "eventBus",
  "canvas"
];
ZoomScroll.prototype.scroll = function scroll(delta2) {
  this._canvas.scroll(delta2);
};
ZoomScroll.prototype.reset = function reset() {
  this._canvas.zoom("fit-viewport");
};
ZoomScroll.prototype.zoom = function zoom(delta2, position) {
  var stepSize = getStepSize(RANGE, NUM_STEPS * 2);
  this._totalDelta += delta2;
  if (Math.abs(this._totalDelta) > DELTA_THRESHOLD) {
    this._zoom(delta2, position, stepSize);
    this._totalDelta = 0;
  }
};
ZoomScroll.prototype._handleWheel = function handleWheel(event2) {
  if (closest(event2.target, ".djs-scrollable", true)) {
    return;
  }
  var element = this._container;
  event2.preventDefault();
  var isZoom = event2.ctrlKey || isMac() && event2.metaKey;
  var isHorizontalScroll = event2.shiftKey;
  var factor = -1 * this._scale, delta2;
  if (isZoom) {
    factor *= event2.deltaMode === 0 ? 0.02 : 0.32;
  } else {
    factor *= event2.deltaMode === 0 ? 1 : 16;
  }
  if (isZoom) {
    var elementRect = element.getBoundingClientRect();
    var offset = {
      x: event2.clientX - elementRect.left,
      y: event2.clientY - elementRect.top
    };
    delta2 = Math.sqrt(
      Math.pow(event2.deltaY, 2) + Math.pow(event2.deltaX, 2)
    ) * sign(event2.deltaY) * factor;
    this.zoom(delta2, offset);
  } else {
    if (isHorizontalScroll) {
      delta2 = {
        dx: factor * event2.deltaY,
        dy: 0
      };
    } else {
      delta2 = {
        dx: factor * event2.deltaX,
        dy: factor * event2.deltaY
      };
    }
    this.scroll(delta2);
  }
};
ZoomScroll.prototype.stepZoom = function stepZoom(delta2, position) {
  var stepSize = getStepSize(RANGE, NUM_STEPS);
  this._zoom(delta2, position, stepSize);
};
ZoomScroll.prototype._zoom = function(delta2, position, stepSize) {
  var canvas = this._canvas;
  var direction = delta2 > 0 ? 1 : -1;
  var currentLinearZoomLevel = log10(canvas.zoom());
  var newLinearZoomLevel = Math.round(currentLinearZoomLevel / stepSize) * stepSize;
  newLinearZoomLevel += stepSize * direction;
  var newLogZoomLevel = Math.pow(10, newLinearZoomLevel);
  canvas.zoom(cap(RANGE, newLogZoomLevel), position);
};
ZoomScroll.prototype.toggle = function toggle(newEnabled) {
  var element = this._container;
  var handleWheel2 = this._handleWheel;
  var oldEnabled = this._enabled;
  if (typeof newEnabled === "undefined") {
    newEnabled = !oldEnabled;
  }
  if (oldEnabled !== newEnabled) {
    event[newEnabled ? "bind" : "unbind"](element, "wheel", handleWheel2, false);
  }
  this._enabled = newEnabled;
  return newEnabled;
};
ZoomScroll.prototype._init = function(newEnabled) {
  this.toggle(newEnabled);
};

// node_modules/diagram-js/lib/navigation/zoomscroll/index.js
var zoomscroll_default = {
  __init__: ["zoomScroll"],
  zoomScroll: ["type", ZoomScroll]
};

// node_modules/bpmn-js/lib/NavigatedViewer.js
function NavigatedViewer(options) {
  Viewer.call(this, options);
}
e(NavigatedViewer, Viewer);
NavigatedViewer.prototype._navigationModules = [
  keyboard_move_default,
  movecanvas_default,
  zoomscroll_default
];
NavigatedViewer.prototype._modules = [].concat(
  Viewer.prototype._modules,
  NavigatedViewer.prototype._navigationModules
);
export {
  NavigatedViewer as default
};
//# sourceMappingURL=bpmn-js_lib_NavigatedViewer.js.map
