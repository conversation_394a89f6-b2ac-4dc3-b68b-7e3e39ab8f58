import React, { createContext, useContext, useState, ReactNode } from 'react';

interface ApiModeContextType {
  mockMode: boolean;
  setMockMode: (mockMode: boolean) => void;
}

const ApiModeContext = createContext<ApiModeContextType | undefined>(undefined);

export const ApiModeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [mockMode, setMockMode] = useState(true); // Default to mock mode

  return (
    <ApiModeContext.Provider value={{ mockMode, setMockMode }}>
      {children}
    </ApiModeContext.Provider>
  );
};

export const useApiMode = (): ApiModeContextType => {
  const context = useContext(ApiModeContext);
  if (context === undefined) {
    throw new Error('useApiMode must be used within an ApiModeProvider');
  }
  return context;
};
