import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import WorkflowDashboard from './components/WorkflowDashboard';
import EnhancedWorkflowDashboard from './components/EnhancedWorkflowDashboard';
import BpmnViewer from './components/BpmnViewer';
import ProcessInstanceDetails from './components/ProcessInstanceDetails';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Routes>
          <Route path="/" element={<EnhancedWorkflowDashboard />} />
          <Route path="/legacy" element={<WorkflowDashboard />} />
          <Route path="/viewer/:processDefinitionId" element={<BpmnViewer />} />
          <Route path="/instance/:processInstanceId" element={<ProcessInstanceDetails />} />
        </Routes>

        {/* Toast notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              duration: 3000,
              iconTheme: {
                primary: '#10b981',
                secondary: '#fff',
              },
            },
            error: {
              duration: 5000,
              iconTheme: {
                primary: '#ef4444',
                secondary: '#fff',
              },
            },
          }}
        />
      </div>
    </Router>
  );
}

export default App;