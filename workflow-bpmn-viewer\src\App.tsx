import React from 'react';
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import WorkflowDashboard from './components/WorkflowDashboard';
import EnhancedWorkflowDashboard from './components/EnhancedWorkflowDashboard';
import BpmnViewer from './components/BpmnViewer';
import ProcessInstanceDetails from './components/ProcessInstanceDetails';
import { ApiModeProvider } from './context/ApiModeContext';
import { ApiModeToggle } from './components/ApiModeToggle';
import { useApiMode } from './context/ApiModeContext';
import { setMockMode } from './services/workflowApi';

const AppContent: React.FC = () => {
  const { mockMode, setMockMode: setContextMockMode } = useApiMode();

  // Initialize mock mode on component mount
  React.useEffect(() => {
    setMockMode(mockMode);
    console.log('App initialized with mock mode:', mockMode);
  }, [mockMode]);

  const handleMockModeToggle = (newMockMode: boolean) => {
    setContextMockMode(newMockMode);
    setMockMode(newMockMode);
    console.log('Mock mode toggled to:', newMockMode);
  };

  return (
    <>
      <Routes>
        <Route path="/" element={<EnhancedWorkflowDashboard />} />
        <Route path="/legacy" element={<WorkflowDashboard />} />
        <Route path="/viewer/:processDefinitionId" element={<BpmnViewer />} />
        <Route path="/instance/:processInstanceId" element={<ProcessInstanceDetails />} />
      </Routes>

      <ApiModeToggle mockMode={mockMode} onToggle={handleMockModeToggle} />

      {/* Toast notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 3000,
            iconTheme: {
              primary: '#10b981',
              secondary: '#fff',
            },
          },
          error: {
            duration: 5000,
            iconTheme: {
              primary: '#ef4444',
              secondary: '#fff',
            },
          },
        }}
      />
    </>
  );
};

function App() {
  return (
    <ApiModeProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <AppContent />
        </div>
      </Router>
    </ApiModeProvider>
  );
}

export default App;