import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import WorkflowDashboard from './components/WorkflowDashboard';
import BpmnViewer from './components/BpmnViewer';
import ProcessInstanceDetails from './components/ProcessInstanceDetails';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Routes>
          <Route path="/" element={<WorkflowDashboard />} />
          <Route path="/viewer/:processDefinitionId" element={<BpmnViewer />} />
          <Route path="/instance/:processInstanceId" element={<ProcessInstanceDetails />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;