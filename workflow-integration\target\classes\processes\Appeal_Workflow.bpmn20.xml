<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             xmlns:flowable="http://flowable.org/bpmn"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC"
             xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             typeLanguage="http://www.w3.org/2001/XMLSchema"
             expressionLanguage="http://www.w3.org/1999/XPath"
             targetNamespace="http://www.flowable.org/processdef"
             exporter="Flowable Open Source Modeler"
             exporterVersion="6.8.1">

  <signal id="Signal_ManagerAction" name="Signal_ManagerAction" flowable:scope="global"/>

  <process id="Appeal_Workflow" name="Appeal Workflow" isExecutable="true">

    <startEvent id="startEvent_Appeal" name="Start"/>

    <serviceTask id="serviceTask_FetchAppeal" name="Fetch Appeal" flowable:delegateExpression="${fetchAppealApplicationDelegate}"/>
    <sequenceFlow id="flow_StartToFetchAppeal" sourceRef="startEvent_Appeal" targetRef="serviceTask_FetchAppeal"/>

    <serviceTask id="serviceTask_NotifyAllParties" name="Notify Manager" flowable:delegateExpression="${notifyAppealDelegate}"/>
    <sequenceFlow id="flow_FetchToNotify" sourceRef="serviceTask_FetchAppeal" targetRef="serviceTask_NotifyAllParties"/>

    <intermediateCatchEvent id="ManagerActionSignal" name="Manager Action">
      <signalEventDefinition signalRef="Signal_ManagerAction"/>
    </intermediateCatchEvent>
    <sequenceFlow id="flow_NotifyToSignal" sourceRef="serviceTask_NotifyAllParties" targetRef="ManagerActionSignal"/>

    <serviceTask id="serviceTask_FetchAfterManager" name="Fetch After Manager" flowable:delegateExpression="${fetchAppealApplicationDelegate}"/>
    <sequenceFlow id="flow_SignalToFetch" sourceRef="ManagerActionSignal" targetRef="serviceTask_FetchAfterManager"/>

    <exclusiveGateway id="gateway_Decision" name="Decision Gateway"/>
    <sequenceFlow id="flow_FetchToDecision" sourceRef="serviceTask_FetchAfterManager" targetRef="gateway_Decision"/>

    <serviceTask id="serviceTask_NotifyClosure" name="Notify Client (Closed)" flowable:delegateExpression="${notifyAppealDelegate}"/>
    <sequenceFlow id="flow_ToClosure" sourceRef="gateway_Decision" targetRef="serviceTask_NotifyClosure">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${decision == 'close'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow_ClosureToEnd" sourceRef="serviceTask_NotifyClosure" targetRef="endEvent_AppealComplete"/>

    <serviceTask id="serviceTask_NotifyRejection" name="Notify Client (Rejected)" flowable:delegateExpression="${notifyAppealDelegate}"/>
    <sequenceFlow id="flow_ToReject" sourceRef="gateway_Decision" targetRef="serviceTask_NotifyRejection">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${decision == 'reject'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow_RejectToEnd" sourceRef="serviceTask_NotifyRejection" targetRef="endEvent_AppealComplete"/>

    <endEvent id="endEvent_AppealComplete" name="Appeal Completed"/>

  </process>
</definitions> 
