version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: workflow-postgres
    environment:
      POSTGRES_DB: workflow
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: admin
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - workflow-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Spring Boot Backend (Workflow Integration Service)
  workflow-backend:
    build:
      context: ./workflow-integration
      dockerfile: Dockerfile
    container_name: workflow-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
      POSTGRES_DB: workflow
      DB_USERNAME: postgres
      DB_PASSWORD: admin
      <PERSON>KA_SERVER: localhost:9092
    ports:
      - "8080:8082"  # Map external 8080 to internal 8082
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - workflow-network
    volumes:
      - ./workflow-integration/src/main/resources/processes:/app/processes
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8082/actuator/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # React Frontend
  workflow-frontend:
    build:
      context: ./workflow-bpmn-viewer
      dockerfile: Dockerfile
    container_name: workflow-frontend
    environment:
      REACT_APP_API_BASE_URL: http://localhost:8080
      REACT_APP_BACKEND_URL: http://workflow-backend:8082
    ports:
      - "3000:3000"
    depends_on:
      - workflow-backend
    networks:
      - workflow-network
    volumes:
      - ./workflow-bpmn-viewer/src:/app/src
      - ./workflow-bpmn-viewer/public:/app/public

volumes:
  postgres_data:

networks:
  workflow-network:
    driver: bridge
