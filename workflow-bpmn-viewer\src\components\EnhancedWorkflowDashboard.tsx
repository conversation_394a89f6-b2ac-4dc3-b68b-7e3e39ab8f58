import React, { useState, useEffect } from 'react';
import {
  Play,
  Eye,
  Clock,
  Activity,
  RefreshCw,
  Search,
  AlertCircle,
  CheckCircle,
  FileText,
  ArrowLeft
} from 'lucide-react';
import { workflowApi } from '../services/workflowApi';
import { ProcessDefinition, ProcessInstance } from '../types/workflow';
import { EnhancedBpmnViewer } from './EnhancedBpmnViewer';
import toast from 'react-hot-toast';

const EnhancedWorkflowDashboard: React.FC = () => {
  const [processDefinitions, setProcessDefinitions] = useState<ProcessDefinition[]>([]);
  const [processInstances, setProcessInstances] = useState<ProcessInstance[]>([]);
  const [selectedProcess, setSelectedProcess] = useState<ProcessDefinition | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'viewer'>('grid');

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try unified endpoint first
      try {
        const unifiedData = await workflowApi.getUnifiedWorkflowData();
        setProcessDefinitions(unifiedData.processDefinitions || []);
        
        // Load all instances
        const allInstances = await workflowApi.getProcessInstances();
        setProcessInstances(allInstances);
        
        toast.success('Dashboard data loaded successfully');
      } catch (unifiedError) {
        // Fallback to individual endpoints
        console.warn('Unified endpoint failed, using fallback:', unifiedError);

        const [definitions, activeInstances] = await Promise.all([
          workflowApi.getProcessDefinitions(),
          workflowApi.getProcessInstances(),
        ]);

        setProcessDefinitions(definitions);
        setProcessInstances(activeInstances);

        toast.success('Dashboard data loaded (fallback mode)');
      }

    } catch (err) {
      console.error('Error loading dashboard data:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load dashboard data';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleStartProcess = async (processDefinitionKey: string) => {
    try {
      await workflowApi.startProcess(processDefinitionKey, {});
      toast.success(`Process ${processDefinitionKey} started successfully`);
      loadDashboardData(); // Refresh data
    } catch (err) {
      console.error('Error starting process:', err);
      toast.error('Failed to start process');
    }
  };

  const handleViewProcess = (definition: ProcessDefinition) => {
    setSelectedProcess(definition);
    setViewMode('viewer');
  };

  const filteredDefinitions = processDefinitions.filter(def =>
    def.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    def.key?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getInstanceCountForDefinition = (definitionKey: string) => {
    return processInstances.filter(instance => 
      instance.processDefinitionKey === definitionKey
    ).length;
  };

  const getActiveInstanceCountForDefinition = (definitionKey: string) => {
    return processInstances.filter(instance => 
      instance.processDefinitionKey === definitionKey && !instance.suspended
    ).length;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading workflow dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-400 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-red-800">Error Loading Dashboard</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
          <button
            onClick={loadDashboardData}
            className="mt-4 flex items-center px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (viewMode === 'viewer' && selectedProcess) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-6">
            <button
              onClick={() => setViewMode('grid')}
              className="flex items-center text-blue-600 hover:text-blue-800 mb-4 transition-colors"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Dashboard
            </button>
          </div>
          <EnhancedBpmnViewer 
            processDefinition={selectedProcess}
            onInstanceSelect={(instance) => {
              console.log('Selected instance:', instance);
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Workflow Dashboard
          </h1>
          <p className="text-gray-600">
            Manage and monitor your BPMN workflow processes
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Process Definitions</p>
                <p className="text-2xl font-semibold text-gray-900">{processDefinitions.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Activity className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Instances</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {processInstances.filter(i => !i.suspended).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Instances</p>
                <p className="text-2xl font-semibold text-gray-900">{processInstances.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Suspended</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {processInstances.filter(i => i.suspended).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-center mb-6">
          <div className="relative mb-4 sm:mb-0">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search processes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 w-64"
            />
          </div>
          <button
            onClick={loadDashboardData}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>

        {/* Process Definitions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredDefinitions.map((definition) => (
            <div key={definition.id} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 truncate">
                    {definition.name || definition.key}
                  </h3>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    v{definition.version}
                  </span>
                </div>

                <p className="text-sm text-gray-600 mb-4">
                  <span className="font-medium">Key:</span> {definition.key}
                </p>

                <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                  <span className="flex items-center">
                    <Activity className="h-3 w-3 mr-1 text-green-500" />
                    {getActiveInstanceCountForDefinition(definition.key)} active
                  </span>
                  <span className="flex items-center">
                    <Clock className="h-3 w-3 mr-1 text-gray-500" />
                    {getInstanceCountForDefinition(definition.key)} total
                  </span>
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={() => handleViewProcess(definition)}
                    className="flex-1 flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </button>
                  <button 
                    onClick={() => handleStartProcess(definition.key)}
                    className="flex-1 flex items-center justify-center px-3 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                  >
                    <Play className="h-4 w-4 mr-1" />
                    Start
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredDefinitions.length === 0 && (
          <div className="text-center py-12">
            <Activity className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No processes found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'Try adjusting your search terms.' : 'No workflow processes are available.'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedWorkflowDashboard;
