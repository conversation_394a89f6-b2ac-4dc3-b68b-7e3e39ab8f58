{"version": 3, "names": ["_path", "data", "require", "normalizeOptions", "config", "filename", "cwd", "filenameRelative", "path", "relative", "sourceType", "inputSourceMap", "sourceMaps", "sourceRoot", "options", "moduleRoot", "sourceFileName", "basename", "comments", "compact", "opts", "Object", "assign", "parserOpts", "extname", "plugins", "generatorOpts", "auxiliaryCommentBefore", "auxiliaryCommentAfter", "retainLines", "shouldPrintComment", "minified", "passes", "plugin", "manipulateOptions"], "sources": ["../../src/transformation/normalize-opts.ts"], "sourcesContent": ["import path from \"node:path\";\nimport type { ResolvedConfig } from \"../config/index.ts\";\n\nexport default function normalizeOptions(config: ResolvedConfig) {\n  const {\n    filename,\n    cwd,\n    filenameRelative = typeof filename === \"string\"\n      ? path.relative(cwd, filename)\n      : \"unknown\",\n    sourceType = \"module\",\n    inputSourceMap,\n    sourceMaps = !!inputSourceMap,\n    sourceRoot = process.env.BABEL_8_BREAKING\n      ? undefined\n      : config.options.moduleRoot,\n\n    sourceFileName = path.basename(filenameRelative),\n\n    comments = true,\n    compact = \"auto\",\n  } = config.options;\n\n  const opts = config.options;\n\n  const options = {\n    ...opts,\n\n    parserOpts: {\n      sourceType:\n        path.extname(filenameRelative) === \".mjs\" ? \"module\" : sourceType,\n\n      sourceFileName: filename,\n      plugins: [],\n      ...opts.parserOpts,\n    },\n\n    generatorOpts: {\n      // General generator flags.\n      filename,\n\n      auxiliaryCommentBefore: opts.auxiliaryCommentBefore,\n      auxiliaryCommentAfter: opts.auxiliaryCommentAfter,\n      retainLines: opts.retainLines,\n      comments,\n      shouldPrintComment: opts.shouldPrintComment,\n      compact,\n      minified: opts.minified,\n\n      // Source-map generation flags.\n      sourceMaps,\n\n      sourceRoot,\n      sourceFileName,\n      ...opts.generatorOpts,\n    },\n  };\n\n  for (const plugins of config.passes) {\n    for (const plugin of plugins) {\n      if (plugin.manipulateOptions) {\n        plugin.manipulateOptions(options, options.parserOpts);\n      }\n    }\n  }\n\n  return options;\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,MAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGe,SAASE,gBAAgBA,CAACC,MAAsB,EAAE;EAC/D,MAAM;IACJC,QAAQ;IACRC,GAAG;IACHC,gBAAgB,GAAG,OAAOF,QAAQ,KAAK,QAAQ,GAC3CG,MAAGA,CAAC,CAACC,QAAQ,CAACH,GAAG,EAAED,QAAQ,CAAC,GAC5B,SAAS;IACbK,UAAU,GAAG,QAAQ;IACrBC,cAAc;IACdC,UAAU,GAAG,CAAC,CAACD,cAAc;IAC7BE,UAAU,GAENT,MAAM,CAACU,OAAO,CAACC,UAAU;IAE7BC,cAAc,GAAGR,MAAGA,CAAC,CAACS,QAAQ,CAACV,gBAAgB,CAAC;IAEhDW,QAAQ,GAAG,IAAI;IACfC,OAAO,GAAG;EACZ,CAAC,GAAGf,MAAM,CAACU,OAAO;EAElB,MAAMM,IAAI,GAAGhB,MAAM,CAACU,OAAO;EAE3B,MAAMA,OAAO,GAAAO,MAAA,CAAAC,MAAA,KACRF,IAAI;IAEPG,UAAU,EAAAF,MAAA,CAAAC,MAAA;MACRZ,UAAU,EACRF,MAAGA,CAAC,CAACgB,OAAO,CAACjB,gBAAgB,CAAC,KAAK,MAAM,GAAG,QAAQ,GAAGG,UAAU;MAEnEM,cAAc,EAAEX,QAAQ;MACxBoB,OAAO,EAAE;IAAE,GACRL,IAAI,CAACG,UAAU,CACnB;IAEDG,aAAa,EAAAL,MAAA,CAAAC,MAAA;MAEXjB,QAAQ;MAERsB,sBAAsB,EAAEP,IAAI,CAACO,sBAAsB;MACnDC,qBAAqB,EAAER,IAAI,CAACQ,qBAAqB;MACjDC,WAAW,EAAET,IAAI,CAACS,WAAW;MAC7BX,QAAQ;MACRY,kBAAkB,EAAEV,IAAI,CAACU,kBAAkB;MAC3CX,OAAO;MACPY,QAAQ,EAAEX,IAAI,CAACW,QAAQ;MAGvBnB,UAAU;MAEVC,UAAU;MACVG;IAAc,GACXI,IAAI,CAACM,aAAa;EACtB,EACF;EAED,KAAK,MAAMD,OAAO,IAAIrB,MAAM,CAAC4B,MAAM,EAAE;IACnC,KAAK,MAAMC,MAAM,IAAIR,OAAO,EAAE;MAC5B,IAAIQ,MAAM,CAACC,iBAAiB,EAAE;QAC5BD,MAAM,CAACC,iBAAiB,CAACpB,OAAO,EAAEA,OAAO,CAACS,UAAU,CAAC;MACvD;IACF;EACF;EAEA,OAAOT,OAAO;AAChB;AAAC", "ignoreList": []}