import React, { useEffect, useRef, useState } from 'react';
import BpmnJS from 'bpmn-js/lib/Viewer';
import { workflowApi } from '../services/workflowApi';
import { ProcessDefinition, ProcessInstance } from '../types/workflow';
import {
  ZoomIn,
  ZoomOut,
  Maximize2,
  Download,
  Play,
  RefreshCw,
  Clock,
  User,
  Activity,
  ChevronRight,
  ChevronDown
} from 'lucide-react';
import toast from 'react-hot-toast';
import { format } from 'date-fns';

interface EnhancedBpmnViewerProps {
  processDefinition: ProcessDefinition;
  onInstanceSelect?: (instance: ProcessInstance) => void;
}

export const EnhancedBpmnViewer: React.FC<EnhancedBpmnViewerProps> = ({ 
  processDefinition, 
  onInstanceSelect 
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const viewerRef = useRef<BpmnJS | null>(null);
  const [bpmnXml, setBpmnXml] = useState<string>('');
  const [instances, setInstances] = useState<ProcessInstance[]>([]);
  const [selectedInstance, setSelectedInstance] = useState<ProcessInstance | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  useEffect(() => {
    if (containerRef.current && !viewerRef.current) {
      viewerRef.current = new BpmnJS({
        container: containerRef.current,
        width: '100%',
        height: isFullscreen ? '80vh' : '600px'
      });
    }

    return () => {
      if (viewerRef.current) {
        viewerRef.current.destroy();
        viewerRef.current = null;
      }
    };
  }, [isFullscreen]);

  useEffect(() => {
    loadBpmnDiagram();
    loadProcessInstances();
  }, [processDefinition]);

  const loadBpmnDiagram = async () => {
    try {
      setLoading(true);
      const xml = await workflowApi.getBpmnXml(processDefinition.id);
      setBpmnXml(xml);
      
      if (viewerRef.current && xml) {
        await viewerRef.current.importXML(xml);
        viewerRef.current.get('canvas').zoom('fit-viewport');
      }
    } catch (err) {
      setError('Failed to load BPMN diagram');
      toast.error('Failed to load BPMN diagram');
      console.error('Error loading BPMN:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadProcessInstances = async () => {
    try {
      const instancesData = await workflowApi.getProcessInstancesWithTasks(processDefinition.key);
      setInstances(instancesData.instances || []);
    } catch (err) {
      console.error('Error loading process instances:', err);
    }
  };

  const handleInstanceSelect = (instance: ProcessInstance) => {
    setSelectedInstance(instance);
    onInstanceSelect?.(instance);
    
    // Highlight active tasks in the diagram
    if (viewerRef.current && instance.activeActivityIds) {
      const canvas = viewerRef.current.get('canvas');
      const elementRegistry = viewerRef.current.get('elementRegistry');
      
      // Clear previous highlights
      const allElements = elementRegistry.getAll();
      allElements.forEach(element => {
        canvas.removeMarker(element.id, 'highlight-active');
        canvas.removeMarker(element.id, 'highlight-completed');
      });
      
      // Highlight active activities
      instance.activeActivityIds.forEach(activityId => {
        canvas.addMarker(activityId, 'highlight-active');
      });

      toast.success(`Selected instance with ${instance.activeTaskCount} active tasks`);
    }
  };

  const handleZoomIn = () => {
    if (viewerRef.current) {
      viewerRef.current.get('zoomScroll').stepZoom(1);
    }
  };

  const handleZoomOut = () => {
    if (viewerRef.current) {
      viewerRef.current.get('zoomScroll').stepZoom(-1);
    }
  };

  const handleFitViewport = () => {
    if (viewerRef.current) {
      viewerRef.current.get('canvas').zoom('fit-viewport');
    }
  };

  const handleDownload = () => {
    if (bpmnXml) {
      const blob = new Blob([bpmnXml], { type: 'application/xml' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${processDefinition.key}.bpmn`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success('BPMN file downloaded');
    }
  };

  const handleStartProcess = async () => {
    try {
      await workflowApi.startProcess(processDefinition.key, {});
      toast.success('Process instance started successfully');
      loadProcessInstances(); // Refresh instances
    } catch (err) {
      toast.error('Failed to start process instance');
      console.error('Error starting process:', err);
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading BPMN diagram...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Activity className="h-5 w-5 text-red-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error Loading Diagram</h3>
            <p className="text-sm text-red-700 mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg ${isFullscreen ? 'fixed inset-4 z-50' : ''}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <Activity className="h-5 w-5 mr-2 text-blue-600" />
              {processDefinition.name}
            </h2>
            <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
              <span className="flex items-center">
                <span className="font-medium">Key:</span> {processDefinition.key}
              </span>
              <span className="flex items-center">
                <span className="font-medium">Version:</span> {processDefinition.version}
              </span>
              <span className="flex items-center">
                <Clock className="h-4 w-4 mr-1" />
                {instances.length} instance(s)
              </span>
            </div>
          </div>
          
          {/* Controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleStartProcess}
              className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              <Play className="h-4 w-4 mr-1" />
              Start Process
            </button>
            <button
              onClick={handleZoomIn}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
              title="Zoom In"
            >
              <ZoomIn className="h-4 w-4" />
            </button>
            <button
              onClick={handleZoomOut}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
              title="Zoom Out"
            >
              <ZoomOut className="h-4 w-4" />
            </button>
            <button
              onClick={handleFitViewport}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
              title="Fit Viewport"
            >
              <Maximize2 className="h-4 w-4" />
            </button>
            <button
              onClick={handleDownload}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
              title="Download BPMN"
            >
              <Download className="h-4 w-4" />
            </button>
            <button
              onClick={loadProcessInstances}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
              title="Refresh"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
            <button
              onClick={toggleFullscreen}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
              title="Toggle Fullscreen"
            >
              <Maximize2 className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
      
      <div className="flex">
        {/* BPMN Diagram */}
        <div className={`flex-1 ${sidebarCollapsed ? 'w-full' : ''}`}>
          <div ref={containerRef} className={`w-full ${isFullscreen ? 'h-[calc(80vh-120px)]' : 'h-[600px]'}`} />
        </div>
        
        {/* Sidebar */}
        {!sidebarCollapsed && instances.length > 0 && (
          <div className="w-80 border-l border-gray-200 bg-gray-50">
            <div className="p-4 border-b border-gray-200 bg-white">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">
                  Process Instances
                </h3>
                <button
                  onClick={() => setSidebarCollapsed(true)}
                  className="p-1 text-gray-400 hover:text-gray-600"
                >
                  <ChevronRight className="h-4 w-4" />
                </button>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                {instances.length} total • {instances.filter(i => !i.suspended).length} active
              </p>
            </div>
            
            <div className="p-4 space-y-3 max-h-96 overflow-y-auto">
              {instances.map((instance) => (
                <div
                  key={instance.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-all ${
                    selectedInstance?.id === instance.id
                      ? 'border-blue-500 bg-blue-50 shadow-sm'
                      : 'border-gray-200 hover:border-gray-300 bg-white hover:shadow-sm'
                  }`}
                  onClick={() => handleInstanceSelect(instance)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-sm font-medium text-gray-900">
                      {instance.id.substring(0, 8)}...
                    </div>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                      instance.suspended 
                        ? 'bg-red-100 text-red-800' 
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {instance.suspended ? 'Suspended' : 'Active'}
                    </div>
                  </div>
                  
                  <div className="space-y-1 text-xs text-gray-600">
                    <div className="flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      Started: {format(new Date(instance.startTime), 'MMM dd, yyyy HH:mm')}
                    </div>
                    {instance.startUserId && (
                      <div className="flex items-center">
                        <User className="h-3 w-3 mr-1" />
                        By: {instance.startUserId}
                      </div>
                    )}
                    {instance.activeTaskCount > 0 && (
                      <div className="flex items-center text-blue-600">
                        <Activity className="h-3 w-3 mr-1" />
                        {instance.activeTaskCount} active task(s)
                      </div>
                    )}
                  </div>
                  
                  {instance.activeTasks && instance.activeTasks.length > 0 && (
                    <div className="mt-2 pt-2 border-t border-gray-100">
                      <div className="text-xs font-medium text-gray-700 mb-1">Active Tasks:</div>
                      {instance.activeTasks.slice(0, 2).map((task, index) => (
                        <div key={task.id} className="text-xs text-gray-600 truncate">
                          • {task.name || task.taskDefinitionKey}
                        </div>
                      ))}
                      {instance.activeTasks.length > 2 && (
                        <div className="text-xs text-gray-500">
                          +{instance.activeTasks.length - 2} more...
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Collapsed sidebar toggle */}
        {sidebarCollapsed && instances.length > 0 && (
          <div className="w-8 border-l border-gray-200 bg-gray-50 flex items-center justify-center">
            <button
              onClick={() => setSidebarCollapsed(false)}
              className="p-1 text-gray-400 hover:text-gray-600"
            >
              <ChevronDown className="h-4 w-4 transform -rotate-90" />
            </button>
          </div>
        )}
      </div>
      
      {/* Custom CSS for highlighting */}
      <style>{`
        .highlight-active .djs-visual > :nth-child(1) {
          stroke: #ef4444 !important;
          stroke-width: 3px !important;
          fill: #fef2f2 !important;
        }
        .highlight-completed .djs-visual > :nth-child(1) {
          stroke: #10b981 !important;
          stroke-width: 2px !important;
          fill: #f0fdf4 !important;
        }
      `}</style>
    </div>
  );
};
