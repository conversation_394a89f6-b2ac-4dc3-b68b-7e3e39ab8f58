<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:flowable="http://flowable.org/bpmn"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC"
             xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             typeLanguage="http://www.w3.org/2001/XMLSchema"
             expressionLanguage="http://www.w3.org/1999/XPath"
             targetNamespace="http://www.flowable.org/processdef"
             exporter="Flowable Open Source Modeler"
             exporterVersion="6.8.1">

 <!-- Signal declarations -->
  <signal id="ComplaintSignal_AgentAction" name="ComplaintSignal_AgentAction"/>
  <signal id="ComplaintSignal_AgentLeadAction" name="ComplaintSignal_AgentLeadAction"/>

  <process id="Complaint_Workflow" name="Complaint Workflow" isExecutable="true">

    <startEvent id="startEvent_Complaint" name="Start"/>
    <serviceTask id="serviceTask_FetchComplaint" name="Fetch Complaint Application" flowable:delegateExpression="${fetchComplaintDataDelegate}"/>
    <sequenceFlow id="flow_StartToFetchComplaint" sourceRef="startEvent_Complaint" targetRef="serviceTask_FetchComplaint"/>

    <serviceTask id="serviceTask_NotifyAgent" name="Notify Agent" flowable:delegateExpression="${notifyComplaintDelegate}"/>
    <sequenceFlow id="flow_FetchComplaintToNotifyAgent" sourceRef="serviceTask_FetchComplaint" targetRef="serviceTask_NotifyAgent"/>

    <intermediateCatchEvent id="AgentAction" name="AgentAction">
      <signalEventDefinition signalRef="ComplaintSignal_AgentAction"/>
    </intermediateCatchEvent>
    <sequenceFlow id="flow_NotifyAgentToAgentAction" sourceRef="serviceTask_NotifyAgent" targetRef="AgentAction"/>

    <serviceTask id="serviceTask_FetchAfterAgent" name="Fetch Complaint After Agent" flowable:delegateExpression="${fetchComplaintDataDelegate}"/>
    <sequenceFlow id="flow_AgentActionToFetchAfterAgent" sourceRef="AgentAction" targetRef="serviceTask_FetchAfterAgent"/>

    <exclusiveGateway id="gateway_CheckStateAfterAgent" name="Check State After Agent"/>
    <sequenceFlow id="flow_FetchToCheckState" sourceRef="serviceTask_FetchAfterAgent" targetRef="gateway_CheckStateAfterAgent"/>
    <sequenceFlow id="flow_StateCompleted" sourceRef="gateway_CheckStateAfterAgent" targetRef="serviceTask_NotifyClient">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${state == 'COMPLETED'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow_EscalatedToNotifyAgentLead" name="escalated" sourceRef="gateway_CheckStateAfterAgent" targetRef="serviceTask_NotifyAgentLead">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${state == 'ESCALATED'}]]></conditionExpression>
    </sequenceFlow>
    <!-- Default flow should not have a condition -->
    <serviceTask id="serviceTask_NotifyClient" name="Notify Client (After Agent)" flowable:delegateExpression="${notifyComplaintDelegate}"/>
    <sequenceFlow id="flow_NotifyClientToEnd" sourceRef="serviceTask_NotifyClient" targetRef="endEvent_ComplaintCompleted"/>

    <serviceTask id="serviceTask_NotifyAgentLead" name="Notify Agent Lead" flowable:delegateExpression="${notifyComplaintDelegate}"/>
    <sequenceFlow id="flow_NotifyAgentLeadToAgentLeadAction" sourceRef="serviceTask_NotifyAgentLead" targetRef="AgentLeadAction"/>

    <intermediateCatchEvent id="AgentLeadAction" name="AgentLeadAction">
      <signalEventDefinition signalRef="ComplaintSignal_AgentLeadAction"/>
    </intermediateCatchEvent>

    <serviceTask id="serviceTask_FetchAfterLead" name="Fetch Complaint After Lead" flowable:delegateExpression="${fetchComplaintDataDelegate}"/>
    <sequenceFlow id="flow_AgentLeadActionToFetch" sourceRef="AgentLeadAction" targetRef="serviceTask_FetchAfterLead"/>

    <exclusiveGateway id="gateway_CheckStateAfterLead" name="Check State After Lead" default="flow_LeadStateUnhandled"/>
    <sequenceFlow id="flow_FetchToCheckLeadState" sourceRef="serviceTask_FetchAfterLead" targetRef="gateway_CheckStateAfterLead"/>
    <sequenceFlow id="flow_StateCompletedLead" name="Completed" sourceRef="gateway_CheckStateAfterLead" targetRef="serviceTask_NotifyClient_Lead">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${state == 'COMPLETED'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow_LeadStateUnhandled" sourceRef="gateway_CheckStateAfterLead" targetRef="endEvent_ComplaintCompleted"/>

    <serviceTask id="serviceTask_NotifyClient_Lead" name="Notify Client (After Lead)" flowable:delegateExpression="${notifyComplaintDelegate}"/>
    <sequenceFlow id="flow_NotifyClientLeadToEnd" sourceRef="serviceTask_NotifyClient_Lead" targetRef="endEvent_ComplaintCompleted"/>

    <endEvent id="endEvent_ComplaintCompleted" name="End"/>

  </process>
</definitions>
