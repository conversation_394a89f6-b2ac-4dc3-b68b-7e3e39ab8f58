import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Play, 
  Pause, 
  Square, 
  Eye, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Activity,
  FileText,
  Settings
} from 'lucide-react';
import { workflowApi } from '../services/workflowApi';
import { ProcessDefinition, ProcessInstance } from '../types/workflow';

const WorkflowDashboard: React.FC = () => {
  const [processDefinitions, setProcessDefinitions] = useState<ProcessDefinition[]>([]);
  const [activeInstances, setActiveInstances] = useState<ProcessInstance[]>([]);
  const [historicInstances, setHistoricInstances] = useState<ProcessInstance[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'definitions' | 'active' | 'history'>('definitions');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [definitions, active, historic] = await Promise.all([
        workflowApi.getProcessDefinitions(),
        workflowApi.getProcessInstances(),
        workflowApi.getHistoricProcessInstances(),
      ]);
      
      setProcessDefinitions(definitions);
      setActiveInstances(active);
      setHistoricInstances(historic);
    } catch (error) {
      console.error('Error loading workflow data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSuspendInstance = async (instanceId: string) => {
    try {
      await workflowApi.suspendProcessInstance(instanceId);
      await loadData();
    } catch (error) {
      console.error('Error suspending instance:', error);
    }
  };

  const handleActivateInstance = async (instanceId: string) => {
    try {
      await workflowApi.activateProcessInstance(instanceId);
      await loadData();
    } catch (error) {
      console.error('Error activating instance:', error);
    }
  };

  const handleDeleteInstance = async (instanceId: string) => {
    if (window.confirm('Are you sure you want to delete this process instance?')) {
      try {
        await workflowApi.deleteProcessInstance(instanceId);
        await loadData();
      } catch (error) {
        console.error('Error deleting instance:', error);
      }
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <Activity className="w-4 h-4 text-green-500" />;
      case 'COMPLETED':
        return <CheckCircle className="w-4 h-4 text-blue-500" />;
      case 'SUSPENDED':
        return <Pause className="w-4 h-4 text-yellow-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatDuration = (durationInMillis?: number) => {
    if (!durationInMillis) return 'N/A';
    const seconds = Math.floor(durationInMillis / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Workflow Management Dashboard</h1>
        <p className="text-gray-600">Manage and monitor your BPMN workflows</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <FileText className="w-8 h-8 text-blue-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Process Definitions</p>
              <p className="text-2xl font-bold text-gray-900">{processDefinitions.length}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Activity className="w-8 h-8 text-green-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Instances</p>
              <p className="text-2xl font-bold text-gray-900">{activeInstances.length}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <CheckCircle className="w-8 h-8 text-blue-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-gray-900">
                {historicInstances.filter(i => i.status === 'COMPLETED').length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Pause className="w-8 h-8 text-yellow-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Suspended</p>
              <p className="text-2xl font-bold text-gray-900">
                {activeInstances.filter(i => i.suspended).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('definitions')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'definitions'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Process Definitions
            </button>
            <button
              onClick={() => setActiveTab('active')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'active'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Active Instances
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'history'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              History
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'definitions' && (
            <div className="space-y-4">
              {processDefinitions.map((definition) => (
                <div key={definition.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{definition.name}</h3>
                      <p className="text-sm text-gray-500">
                        Key: {definition.key} | Version: {definition.version} | Category: {definition.category}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <Link
                        to={`/viewer/${definition.id}`}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        View BPMN
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'active' && (
            <div className="space-y-4">
              {activeInstances.map((instance) => (
                <div key={instance.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        {getStatusIcon(instance.status)}
                        <h3 className="ml-2 text-lg font-medium text-gray-900">
                          {instance.processDefinitionName}
                        </h3>
                        {instance.suspended && (
                          <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            Suspended
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-500 mt-1">
                        ID: {instance.id} | Started: {new Date(instance.startTime).toLocaleString()}
                      </p>
                      {instance.currentActivities.length > 0 && (
                        <p className="text-sm text-blue-600 mt-1">
                          Current Activities: {instance.currentActivities.join(', ')}
                        </p>
                      )}
                    </div>
                    <div className="flex space-x-2">
                      <Link
                        to={`/instance/${instance.id}`}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        Details
                      </Link>
                      {instance.suspended ? (
                        <button
                          onClick={() => handleActivateInstance(instance.id)}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                        >
                          <Play className="w-4 h-4 mr-2" />
                          Activate
                        </button>
                      ) : (
                        <button
                          onClick={() => handleSuspendInstance(instance.id)}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700"
                        >
                          <Pause className="w-4 h-4 mr-2" />
                          Suspend
                        </button>
                      )}
                      <button
                        onClick={() => handleDeleteInstance(instance.id)}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
                      >
                        <Square className="w-4 h-4 mr-2" />
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'history' && (
            <div className="space-y-4">
              {historicInstances.map((instance) => (
                <div key={instance.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        {getStatusIcon(instance.status)}
                        <h3 className="ml-2 text-lg font-medium text-gray-900">
                          {instance.processDefinitionName}
                        </h3>
                        <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          instance.status === 'COMPLETED' 
                            ? 'bg-blue-100 text-blue-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {instance.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">
                        ID: {instance.id} | Started: {new Date(instance.startTime).toLocaleString()}
                        {instance.endTime && ` | Ended: ${new Date(instance.endTime).toLocaleString()}`}
                      </p>
                      <p className="text-sm text-gray-500">
                        Duration: {formatDuration(instance.durationInMillis)}
                        {instance.deleteReason && ` | Reason: ${instance.deleteReason}`}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <Link
                        to={`/instance/${instance.id}`}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        Details
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WorkflowDashboard;