# BPMN Workflow Viewer - Complete Solution

A comprehensive Spring Boot + React + PostgreSQL solution for viewing and managing BPMN workflows with Flowable engine integration.

## 🏗️ Architecture

- **Backend**: Spring Boot 3.x with Flowable BPMN Engine (Port 8080)
- **Frontend**: React 18 with TypeScript and Tailwind CSS (Port 3000)
- **Database**: PostgreSQL 15 (Port 5432)
- **Containerization**: Docker Compose for easy deployment

## ✨ Features

### 📋 Process Definition Management
- View all available BPMN process definitions
- Display process metadata (name, key, version)
- Support for both deployed and resource-based processes

### 🎨 Enhanced BPMN Diagram Viewer
- Interactive BPMN.js-based diagram rendering
- Real-time process instance highlighting
- Active task visualization with color coding
- Zoom controls (in, out, fit viewport)
- Fullscreen mode support
- Download BPMN XML functionality

### 📱 Process Instance Management
- List all process instances with status
- Real-time active task tracking
- Instance details sidebar with task information
- Start new process instances
- Instance filtering and search

### 🔧 Advanced Controls
- Process instance start/stop controls
- Task claiming and completion (ready for extension)
- User authentication integration (ready for extension)
- Multi-diagram support

## 🚀 Quick Start

### Prerequisites
- <PERSON><PERSON> and <PERSON><PERSON> Compose
- Git

### 1. <PERSON><PERSON> and <PERSON>up
```bash
git clone <repository-url>
cd emty
```

### 2. Start All Services
```bash
docker-compose up --build
```

This will start:
- PostgreSQL database on port 5432
- Spring Boot backend on port 8080
- React frontend on port 3000

### 3. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8080/api
- **Database**: localhost:5432 (postgres/admin)

## 📁 Project Structure

```
emty/
├── docker-compose.yml              # Docker orchestration
├── workflow-integration/           # Spring Boot Backend
│   ├── src/main/java/
│   │   └── com/workflowenginee/workflow/
│   │       ├── controller/
│   │       │   └── BpmnResourceController.java
│   │       ├── config/
│   │       │   └── CorsConfig.java
│   │       └── delegate/           # BPMN delegates
│   ├── src/main/resources/
│   │   ├── processes/              # BPMN XML files
│   │   │   ├── check-demo.bpmn20.xml
│   │   │   ├── check-demo1.bpmn20.xml
│   │   │   └── Appeal_Workflow.bpmn20.xml
│   │   └── application.yml
│   └── Dockerfile
├── workflow-bpmn-viewer/           # React Frontend
│   ├── src/
│   │   ├── components/
│   │   │   ├── EnhancedBpmnViewer.tsx
│   │   │   ├── EnhancedWorkflowDashboard.tsx
│   │   │   ├── BpmnViewer.tsx
│   │   │   └── WorkflowDashboard.tsx
│   │   ├── services/
│   │   │   └── workflowApi.ts
│   │   └── types/
│   │       └── workflow.ts
│   ├── package.json
│   ├── Dockerfile
│   └── nginx.conf
└── init-scripts/                   # Database initialization
```

## 🔌 API Endpoints

### Process Definitions
- `GET /api/v1/workflow/bpmn/unified` - Get all workflow data
- `GET /api/v1/workflow/bpmn/definitions` - Get process definitions
- `GET /api/v1/workflow/bpmn/definition/{id}/xml` - Get BPMN XML

### Process Instances
- `GET /api/v1/workflow/bpmn/instances/{key}/with-tasks` - Get instances with active tasks
- `POST /api/v1/workflow/bpmn/start-process/{key}` - Start process instance

## 🎯 Key Components

### Enhanced BPMN Viewer
- **File**: `workflow-bpmn-viewer/src/components/EnhancedBpmnViewer.tsx`
- **Features**: 
  - Interactive diagram with bpmn-js
  - Active task highlighting
  - Instance sidebar with task details
  - Zoom and download controls
  - Fullscreen mode

### Enhanced Workflow Dashboard
- **File**: `workflow-bpmn-viewer/src/components/EnhancedWorkflowDashboard.tsx`
- **Features**:
  - Process definition grid view
  - Statistics dashboard
  - Search and filter functionality
  - Direct process starting
  - Integrated BPMN viewer

### Backend Controller
- **File**: `workflow-integration/src/main/java/.../BpmnResourceController.java`
- **Features**:
  - Unified API endpoints
  - CORS configuration
  - Process instance management
  - Active task tracking

## 🔧 Configuration

### Environment Variables
The application supports the following environment variables:

#### Backend (Spring Boot)
- `POSTGRES_HOST` - Database host (default: localhost)
- `POSTGRES_PORT` - Database port (default: 5432)
- `POSTGRES_DB` - Database name (default: workflow)
- `DB_USERNAME` - Database username (default: postgres)
- `DB_PASSWORD` - Database password (default: admin)

#### Frontend (React)
- `REACT_APP_API_BASE_URL` - Backend API URL (default: http://localhost:8080)

## 🎨 UI Features

### Dashboard View
- 📊 Statistics cards showing process counts
- 🔍 Search functionality for process definitions
- 📋 Grid layout with process cards
- ▶️ Quick start buttons for processes
- 👁️ View buttons to open BPMN diagrams

### BPMN Viewer
- 🎨 Interactive diagram rendering
- 🔴 Red highlighting for active tasks
- 📱 Responsive sidebar with instance details
- 🔧 Zoom controls and fullscreen mode
- 💾 Download BPMN XML functionality
- 🔄 Real-time instance refresh

## 🚀 Development

### Backend Development
```bash
cd workflow-integration
./mvnw spring-boot:run
```

### Frontend Development
```bash
cd workflow-bpmn-viewer
npm install
npm run dev
```

### Database Access
```bash
# Connect to PostgreSQL
docker exec -it workflow-postgres psql -U postgres -d workflow
```

## 🔍 Troubleshooting

### Common Issues

1. **Port Conflicts**: Ensure ports 3000, 5432, and 8080 are available
2. **Database Connection**: Check PostgreSQL container is running
3. **CORS Issues**: Verify CORS configuration in `CorsConfig.java`
4. **BPMN Loading**: Check BPMN files are in `resources/processes/`

### Logs
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f workflow-backend
docker-compose logs -f workflow-frontend
docker-compose logs -f postgres
```

## 🎯 Next Steps

Ready for extension with:
- ✅ Task claiming and completion
- ✅ User authentication (Keycloak ready)
- ✅ Multi-diagram support
- ✅ Process deployment via UI
- ✅ Advanced filtering and search
- ✅ Real-time notifications

## 📝 License

This project is part of a workflow management system demonstration.
