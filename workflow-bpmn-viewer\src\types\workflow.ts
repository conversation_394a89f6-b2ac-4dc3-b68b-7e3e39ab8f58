export interface ProcessDefinition {
  id: string;
  key: string;
  name: string;
  version: number;
  deploymentId: string;
  resourceName: string;
  category: string;
}

export interface ProcessInstance {
  id: string;
  processDefinitionId: string;
  processDefinitionKey: string;
  processDefinitionName: string;
  startTime: string;
  endTime?: string;
  durationInMillis?: number;
  startUserId?: string;
  businessKey?: string;
  suspended?: boolean;
  status: 'ACTIVE' | 'COMPLETED' | 'SUSPENDED';
  currentActivities: string[];
  activeActivityIds?: string[];
  activeTasks?: Task[];
  activeTaskCount?: number;
  variables?: Record<string, any>;
  deleteReason?: string;
}

export interface Task {
  id: string;
  name: string;
  taskDefinitionKey: string;
  assignee?: string;
  createTime: string;
  priority?: number;
}

export interface WorkflowApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}

export interface ProcessInstanceStatus {
  id: string;
  processDefinitionKey: string;
  status: 'ACTIVE' | 'COMPLETED' | 'SUSPENDED';
  startTime: string;
  endTime?: string;
  durationInMillis?: number;
  businessKey?: string;
  currentActivities: string[];
  variables: Record<string, any>;
}