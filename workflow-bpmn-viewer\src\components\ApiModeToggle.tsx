import React from 'react';
import { Settings, Wifi, WifiOff } from 'lucide-react';

interface ApiModeToggleProps {
  mockMode: boolean;
  onToggle: (mockMode: boolean) => void;
}

export const ApiModeToggle: React.FC<ApiModeToggleProps> = ({ mockMode, onToggle }) => {
  return (
    <div className="fixed top-4 right-4 z-50">
      <div className="bg-white rounded-lg shadow-lg border p-3">
        <div className="flex items-center space-x-3">
          <Settings className="h-4 w-4 text-gray-500" />
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">API Mode:</span>
            <button
              onClick={() => onToggle(!mockMode)}
              className={`flex items-center space-x-1 px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                mockMode
                  ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                  : 'bg-green-100 text-green-800 hover:bg-green-200'
              }`}
            >
              {mockMode ? (
                <>
                  <WifiOff className="h-3 w-3" />
                  <span>Mock</span>
                </>
              ) : (
                <>
                  <Wifi className="h-3 w-3" />
                  <span>Live</span>
                </>
              )}
            </button>
          </div>
        </div>
        {mockMode && (
          <div className="mt-2 text-xs text-yellow-600">
            Using mock data. Start backend server and toggle to "Live" for real data.
          </div>
        )}
      </div>
    </div>
  );
};
