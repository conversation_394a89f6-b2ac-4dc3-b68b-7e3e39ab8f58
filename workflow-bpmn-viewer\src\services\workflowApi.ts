import axios from 'axios';
import { ProcessDefinition, ProcessInstance, WorkflowApiResponse, ProcessInstanceStatus } from '../types/workflow';

const API_BASE_URL = 'http://localhost:8080/api/v1/workflow';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
});

// Add request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    if (error.response?.status === 404) {
      throw new Error('Resource not found');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error occurred');
    }
    return Promise.reject(error);
  }
);

export const workflowApi = {
  // Unified endpoint for frontend
  getUnifiedWorkflowData: async () => {
    try {
      const response = await api.get('/bpmn/unified');
      return response.data;
    } catch (error) {
      console.error('Error fetching unified workflow data:', error);
      throw error;
    }
  },

  // Process Definitions
  getProcessDefinitions: async (): Promise<ProcessDefinition[]> => {
    try {
      const response = await api.get('/bpmn/definitions');
      return response.data.data || response.data.processDefinitions || [];
    } catch (error) {
      console.error('Error fetching process definitions:', error);
      throw error;
    }
  },

  getBpmnXml: async (processDefinitionId: string): Promise<string> => {
    try {
      const response = await api.get(`/bpmn/definition/${processDefinitionId}/xml`, {
        headers: {
          'Accept': 'application/xml',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching BPMN XML:', error);
      throw error;
    }
  },

  getProcessDefinitionXml: async (processDefinitionId: string): Promise<string> => {
    return workflowApi.getBpmnXml(processDefinitionId);
  },

  // Process Instances
  getProcessInstances: async (processDefinitionKey?: string): Promise<ProcessInstance[]> => {
    try {
      const url = processDefinitionKey
        ? `/bpmn/instances/${processDefinitionKey}`
        : '/bpmn/instances';
      const response = await api.get(url);
      return response.data.data || response.data.instances || [];
    } catch (error) {
      console.error('Error fetching process instances:', error);
      throw error;
    }
  },

  getProcessInstancesWithTasks: async (processDefinitionKey: string) => {
    try {
      const response = await api.get(`/bpmn/instances/${processDefinitionKey}/with-tasks`);
      return response.data;
    } catch (error) {
      console.error('Error fetching process instances with tasks:', error);
      throw error;
    }
  },

  getHistoricProcessInstances: async (): Promise<ProcessInstance[]> => {
    try {
      const response = await api.get('/bpmn/instances/history');
      return response.data.data || response.data.instances || [];
    } catch (error) {
      console.error('Error fetching historic process instances:', error);
      throw error;
    }
  },

  getProcessInstanceStatus: async (processInstanceId: string): Promise<ProcessInstanceStatus> => {
    const response = await api.get<WorkflowApiResponse<ProcessInstanceStatus>>(`/instance/${processInstanceId}/status`);
    if (!response.data.data) {
      throw new Error(response.data.message || 'Failed to fetch process instance status');
    }
    return response.data.data;
  },

  // Process Instance Management
  suspendProcessInstance: async (processInstanceId: string): Promise<void> => {
    await api.post(`/instance/${processInstanceId}/suspend`);
  },

  activateProcessInstance: async (processInstanceId: string): Promise<void> => {
    await api.post(`/instance/${processInstanceId}/activate`);
  },

  deleteProcessInstance: async (processInstanceId: string, deleteReason?: string): Promise<void> => {
    await api.post(`/instance/${processInstanceId}/delete`, {
      deleteReason: deleteReason || 'Deleted via BPMN Viewer',
    });
  },

  // Start Process
  startProcess: async (processDefinitionKey: string, variables: any = {}): Promise<any> => {
    try {
      const response = await api.post(`/bpmn/start-process/${processDefinitionKey}`, {
        variables
      });
      return response.data;
    } catch (error) {
      console.error('Error starting process:', error);
      throw error;
    }
  },

  // Legacy method for backward compatibility
  startProcessLegacy: async (applicationType: string, applicationNumber: string): Promise<any> => {
    try {
      const response = await api.get(`/start-process/${applicationType}/${applicationNumber}`);
      return response.data;
    } catch (error) {
      console.error('Error starting legacy process:', error);
      throw error;
    }
  },

  // Resume Process
  resumeProcess: async (processInstanceId: string, signalType: string, payload: any): Promise<any> => {
    const response = await api.post(`/resume-process/${processInstanceId}/${signalType}`, payload);
    return response.data;
  },
};