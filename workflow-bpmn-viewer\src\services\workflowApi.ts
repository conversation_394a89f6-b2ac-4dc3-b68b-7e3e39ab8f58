import axios from 'axios';
import { ProcessDefinition, ProcessInstance, WorkflowApiResponse, ProcessInstanceStatus } from '../types/workflow';

// Mock mode will be controlled by context
let MOCK_MODE = true; // Default to mock mode

// Function to set mock mode from context
export const setMockMode = (mockMode: boolean) => {
  MOCK_MODE = mockMode;
  console.log(`API Mode changed to: ${mockMode ? 'Mock' : 'Live'}`);
};

const API_BASE_URL = 'http://localhost:8080/api/v1/workflow';

// Mock data
const mockProcessDefinitions: ProcessDefinition[] = [
  {
    id: 'check-demo',
    key: 'Hrdc_workflow',
    name: 'HRDC Workflow',
    version: 1,
    deploymentId: 'mock-deployment-1',
    resourceName: 'check-demo.bpmn20.xml',
    category: 'workflow',
    description: 'HRDC workflow process',
    suspended: false,
    tenantId: null,
    runningInstances: 2
  },
  {
    id: 'check-demo1',
    key: 'Complaint_Workflow',
    name: 'Complaint Workflow',
    version: 1,
    deploymentId: 'mock-deployment-2',
    resourceName: 'check-demo1.bpmn20.xml',
    category: 'workflow',
    description: 'Complaint handling workflow',
    suspended: false,
    tenantId: null,
    runningInstances: 1
  },
  {
    id: 'Appeal_Workflow',
    key: 'Appeal_Workflow',
    name: 'Appeal Workflow',
    version: 1,
    deploymentId: 'mock-deployment-3',
    resourceName: 'Appeal_Workflow.bpmn20.xml',
    category: 'workflow',
    description: 'Appeal processing workflow',
    suspended: false,
    tenantId: null,
    runningInstances: 0
  }
];

const mockProcessInstances: ProcessInstance[] = [
  {
    id: 'instance-1',
    processDefinitionId: 'check-demo',
    processDefinitionKey: 'Hrdc_workflow',
    processDefinitionName: 'HRDC Workflow',
    startTime: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
    startUserId: 'admin',
    businessKey: 'HRDC-001',
    suspended: false,
    status: 'ACTIVE',
    currentActivities: ['serviceTask_FetchNCBSC'],
    activeActivityIds: ['serviceTask_FetchNCBSC'],
    activeTasks: [
      {
        id: 'task-1',
        name: 'Fetch NCBSC',
        taskDefinitionKey: 'serviceTask_FetchNCBSC',
        assignee: 'admin',
        createTime: new Date(Date.now() - 3600000).toISOString(),
        priority: 50
      }
    ],
    activeTaskCount: 1
  },
  {
    id: 'instance-2',
    processDefinitionId: 'check-demo1',
    processDefinitionKey: 'Complaint_Workflow',
    processDefinitionName: 'Complaint Workflow',
    startTime: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
    startUserId: 'user1',
    businessKey: 'COMP-001',
    suspended: false,
    status: 'ACTIVE',
    currentActivities: ['serviceTask_NotifyAgent'],
    activeActivityIds: ['serviceTask_NotifyAgent'],
    activeTasks: [
      {
        id: 'task-2',
        name: 'Notify Agent',
        taskDefinitionKey: 'serviceTask_NotifyAgent',
        assignee: 'agent1',
        createTime: new Date(Date.now() - 7200000).toISOString(),
        priority: 75
      }
    ],
    activeTaskCount: 1
  }
];

const mockBpmnXml = `<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:flowable="http://flowable.org/bpmn"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC"
             xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             typeLanguage="http://www.w3.org/2001/XMLSchema"
             expressionLanguage="http://www.w3.org/1999/XPath"
             targetNamespace="http://www.flowable.org/processdef">

  <process id="demo_process" name="Demo Process" isExecutable="true">
    <startEvent id="startEvent" name="Start"/>
    <serviceTask id="serviceTask_Demo" name="Demo Task"/>
    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="serviceTask_Demo"/>
    <endEvent id="endEvent" name="End"/>
    <sequenceFlow id="flow2" sourceRef="serviceTask_Demo" targetRef="endEvent"/>
  </process>

  <bpmndi:BPMNDiagram id="BPMNDiagram_demo_process">
    <bpmndi:BPMNPlane bpmnElement="demo_process" id="BPMNPlane_demo_process">
      <bpmndi:BPMNShape bpmnElement="startEvent" id="BPMNShape_startEvent">
        <omgdc:Bounds height="36.0" width="36.0" x="100.0" y="100.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="serviceTask_Demo" id="BPMNShape_serviceTask_Demo">
        <omgdc:Bounds height="80.0" width="100.0" x="200.0" y="78.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="endEvent" id="BPMNShape_endEvent">
        <omgdc:Bounds height="36.0" width="36.0" x="400.0" y="100.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="flow1" id="BPMNEdge_flow1">
        <omgdi:waypoint x="136.0" y="118.0"/>
        <omgdi:waypoint x="200.0" y="118.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow2" id="BPMNEdge_flow2">
        <omgdi:waypoint x="300.0" y="118.0"/>
        <omgdi:waypoint x="400.0" y="118.0"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>`;

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 5000, // Reduced timeout for faster fallback
});

// Add request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    if (error.response?.status === 404) {
      throw new Error('Resource not found');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error occurred');
    }
    return Promise.reject(error);
  }
);

export const workflowApi = {
  // Unified endpoint for frontend
  getUnifiedWorkflowData: async () => {
    try {
      if (MOCK_MODE) {
        console.log('Using mock data for unified workflow data');
        return {
          processDefinitions: mockProcessDefinitions,
          totalCount: mockProcessDefinitions.length,
          deployedCount: mockProcessDefinitions.length,
          resourceCount: mockProcessDefinitions.length
        };
      }

      const response = await api.get('/bpmn/unified');
      return response.data;
    } catch (error) {
      console.error('Error fetching unified workflow data:', error);
      if (MOCK_MODE) {
        console.log('Falling back to mock data for unified workflow data');
        return {
          processDefinitions: mockProcessDefinitions,
          totalCount: mockProcessDefinitions.length,
          deployedCount: mockProcessDefinitions.length,
          resourceCount: mockProcessDefinitions.length
        };
      }
      throw error;
    }
  },

  // Process Definitions
  getProcessDefinitions: async (): Promise<ProcessDefinition[]> => {
    try {
      if (MOCK_MODE) {
        console.log('Using mock data for process definitions');
        return mockProcessDefinitions;
      }

      const response = await api.get('/bpmn/definitions');
      return response.data.data || response.data.processDefinitions || [];
    } catch (error) {
      console.error('Error fetching process definitions:', error);
      if (MOCK_MODE) {
        console.log('Falling back to mock data for process definitions');
        return mockProcessDefinitions;
      }
      throw error;
    }
  },

  getBpmnXml: async (processDefinitionId: string): Promise<string> => {
    try {
      if (MOCK_MODE) {
        console.log(`Using mock BPMN XML for process definition: ${processDefinitionId}`);
        return mockBpmnXml;
      }

      const response = await api.get(`/bpmn/definition/${processDefinitionId}/xml`, {
        headers: {
          'Accept': 'application/xml',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching BPMN XML:', error);
      if (MOCK_MODE) {
        console.log(`Falling back to mock BPMN XML for process definition: ${processDefinitionId}`);
        return mockBpmnXml;
      }
      throw error;
    }
  },

  getProcessDefinitionXml: async (processDefinitionId: string): Promise<string> => {
    return workflowApi.getBpmnXml(processDefinitionId);
  },

  // Process Instances
  getProcessInstances: async (processDefinitionKey?: string): Promise<ProcessInstance[]> => {
    try {
      if (MOCK_MODE) {
        console.log(`Using mock data for process instances${processDefinitionKey ? ` for definition: ${processDefinitionKey}` : ''}`);
        if (processDefinitionKey) {
          return mockProcessInstances.filter(instance =>
            instance.processDefinitionKey === processDefinitionKey
          );
        }
        return mockProcessInstances;
      }

      const url = processDefinitionKey
        ? `/bpmn/instances/${processDefinitionKey}`
        : '/bpmn/instances';
      const response = await api.get(url);
      return response.data.data || response.data.instances || [];
    } catch (error) {
      console.error('Error fetching process instances:', error);
      if (MOCK_MODE) {
        console.log(`Falling back to mock data for process instances${processDefinitionKey ? ` for definition: ${processDefinitionKey}` : ''}`);
        if (processDefinitionKey) {
          return mockProcessInstances.filter(instance =>
            instance.processDefinitionKey === processDefinitionKey
          );
        }
        return mockProcessInstances;
      }
      throw error;
    }
  },

  getProcessInstancesWithTasks: async (processDefinitionKey: string) => {
    try {
      if (MOCK_MODE) {
        console.log(`Using mock data for process instances with tasks for definition: ${processDefinitionKey}`);
        const instances = mockProcessInstances.filter(instance =>
          instance.processDefinitionKey === processDefinitionKey
        );
        return {
          processDefinitionKey,
          instances,
          totalInstances: instances.length
        };
      }

      const response = await api.get(`/bpmn/instances/${processDefinitionKey}/with-tasks`);
      return response.data;
    } catch (error) {
      console.error('Error fetching process instances with tasks:', error);
      if (MOCK_MODE) {
        console.log(`Falling back to mock data for process instances with tasks for definition: ${processDefinitionKey}`);
        const instances = mockProcessInstances.filter(instance =>
          instance.processDefinitionKey === processDefinitionKey
        );
        return {
          processDefinitionKey,
          instances,
          totalInstances: instances.length
        };
      }
      throw error;
    }
  },

  getHistoricProcessInstances: async (): Promise<ProcessInstance[]> => {
    try {
      if (MOCK_MODE) {
        console.log('Using mock data for historic process instances');
        return [];  // No historic instances in mock data
      }

      const response = await api.get('/bpmn/instances/history');
      return response.data.data || response.data.instances || [];
    } catch (error) {
      console.error('Error fetching historic process instances:', error);
      if (MOCK_MODE) {
        console.log('Falling back to mock data for historic process instances');
        return [];  // No historic instances in mock data
      }
      throw error;
    }
  },

  getProcessInstanceStatus: async (processInstanceId: string): Promise<ProcessInstanceStatus> => {
    const response = await api.get<WorkflowApiResponse<ProcessInstanceStatus>>(`/instance/${processInstanceId}/status`);
    if (!response.data.data) {
      throw new Error(response.data.message || 'Failed to fetch process instance status');
    }
    return response.data.data;
  },

  // Process Instance Management
  suspendProcessInstance: async (processInstanceId: string): Promise<void> => {
    await api.post(`/instance/${processInstanceId}/suspend`);
  },

  activateProcessInstance: async (processInstanceId: string): Promise<void> => {
    await api.post(`/instance/${processInstanceId}/activate`);
  },

  deleteProcessInstance: async (processInstanceId: string, deleteReason?: string): Promise<void> => {
    await api.post(`/instance/${processInstanceId}/delete`, {
      deleteReason: deleteReason || 'Deleted via BPMN Viewer',
    });
  },

  // Start Process
  startProcess: async (processDefinitionKey: string, variables: any = {}): Promise<any> => {
    try {
      console.log(`Starting process ${processDefinitionKey}, MOCK_MODE: ${MOCK_MODE}`);

      if (MOCK_MODE) {
        console.log(`Mock: Starting process ${processDefinitionKey} with variables:`, variables);

        // Find the process definition
        const processDefinition = mockProcessDefinitions.find(def => def.key === processDefinitionKey);

        if (!processDefinition) {
          console.error(`Process definition with key ${processDefinitionKey} not found in mock data`);
          return {
            success: false,
            message: `Process definition with key ${processDefinitionKey} not found`
          };
        }

        // Simulate successful process start
        const newInstance: ProcessInstance = {
          id: `instance-${Date.now()}`,
          processDefinitionId: processDefinition.id,
          processDefinitionKey: processDefinition.key,
          processDefinitionName: processDefinition.name,
          startTime: new Date().toISOString(),
          startUserId: 'mock-user',
          businessKey: `MOCK-${Date.now()}`,
          suspended: false,
          status: 'ACTIVE' as const,
          currentActivities: ['startEvent'],
          activeActivityIds: ['startEvent'],
          activeTasks: [
            {
              id: `task-${Date.now()}`,
              name: 'Initial Task',
              taskDefinitionKey: 'startEvent',
              assignee: 'mock-user',
              createTime: new Date().toISOString(),
              priority: 50
            }
          ],
          activeTaskCount: 1
        };

        // Add to mock instances
        mockProcessInstances.push(newInstance);

        console.log('Mock instance created:', newInstance);

        return {
          success: true,
          processInstanceId: newInstance.id,
          message: 'Process started successfully (mock mode)'
        };
      }

      const response = await api.post(`/bpmn/start-process/${processDefinitionKey}`, {
        variables
      });
      return response.data;
    } catch (error) {
      console.error('Error starting process:', error);

      // Always return a valid response in mock mode, even on error
      if (MOCK_MODE) {
        console.log(`Mock: Simulating successful process start for ${processDefinitionKey} after error`);

        // Find the process definition
        const processDefinition = mockProcessDefinitions.find(def => def.key === processDefinitionKey);

        if (!processDefinition) {
          return {
            success: false,
            message: `Process definition with key ${processDefinitionKey} not found`
          };
        }

        // Create a mock instance
        const newInstance: ProcessInstance = {
          id: `instance-${Date.now()}`,
          processDefinitionId: processDefinition.id,
          processDefinitionKey: processDefinition.key,
          processDefinitionName: processDefinition.name,
          startTime: new Date().toISOString(),
          startUserId: 'mock-user',
          businessKey: `MOCK-${Date.now()}`,
          suspended: false,
          status: 'ACTIVE' as const,
          currentActivities: ['startEvent'],
          activeActivityIds: ['startEvent'],
          activeTasks: [],
          activeTaskCount: 0
        };

        // Add to mock instances
        mockProcessInstances.push(newInstance);

        return {
          success: true,
          processInstanceId: newInstance.id,
          message: 'Process started successfully (mock mode - recovery)'
        };
      }

      // In real mode, throw the error
      throw error;
    }
  },

  // Legacy method for backward compatibility
  startProcessLegacy: async (applicationType: string, applicationNumber: string): Promise<any> => {
    try {
      const response = await api.get(`/start-process/${applicationType}/${applicationNumber}`);
      return response.data;
    } catch (error) {
      console.error('Error starting legacy process:', error);
      throw error;
    }
  },

  // Resume Process
  resumeProcess: async (processInstanceId: string, signalType: string, payload: any): Promise<any> => {
    const response = await api.post(`/resume-process/${processInstanceId}/${signalType}`, payload);
    return response.data;
  },
};