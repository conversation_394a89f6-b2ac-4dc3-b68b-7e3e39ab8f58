{"version": 3, "sources": ["../../diagram-js/lib/features/keyboard/KeyboardUtil.js", "../../diagram-js/lib/features/keyboard/Keyboard.js", "../../diagram-js/lib/features/keyboard/KeyboardBindings.js", "../../diagram-js/lib/features/keyboard/index.js", "../../diagram-js/lib/navigation/keyboard-move/KeyboardMove.js", "../../diagram-js/lib/navigation/keyboard-move/index.js", "../../diagram-js/lib/util/Cursor.js", "../../diagram-js/lib/util/ClickTrap.js", "../../diagram-js/lib/util/PositionUtil.js", "../../diagram-js/lib/navigation/movecanvas/MoveCanvas.js", "../../diagram-js/lib/navigation/movecanvas/index.js", "../../diagram-js/lib/util/Math.js", "../../diagram-js/lib/navigation/zoomscroll/ZoomUtil.js", "../../diagram-js/lib/navigation/zoomscroll/ZoomScroll.js", "../../diagram-js/lib/navigation/zoomscroll/index.js", "../../bpmn-js/lib/NavigatedViewer.js"], "sourcesContent": ["import { isArray } from 'min-dash';\r\n\r\nexport var KEYS_COPY = [ 'c', 'C' ];\r\nexport var KEYS_PASTE = [ 'v', 'V' ];\r\nexport var KEYS_REDO = [ 'y', 'Y' ];\r\nexport var KEYS_UNDO = [ 'z', 'Z' ];\r\n\r\n/**\r\n * Returns true if event was triggered with any modifier\r\n * @param {KeyboardEvent} event\r\n */\r\nexport function hasModifier(event) {\r\n  return (event.ctrlKey || event.metaKey || event.shiftKey || event.altKey);\r\n}\r\n\r\n/**\r\n * @param {KeyboardEvent} event\r\n * @return {boolean}\r\n */\r\nexport function isCmd(event) {\r\n\r\n  // ensure we don't react to AltGr\r\n  // (mapped to CTRL + ALT)\r\n  if (event.altKey) {\r\n    return false;\r\n  }\r\n\r\n  return event.ctrlKey || event.metaKey;\r\n}\r\n\r\n/**\r\n * Checks if key pressed is one of provided keys.\r\n *\r\n * @param {string|string[]} keys\r\n * @param {KeyboardEvent} event\r\n * @return {boolean}\r\n */\r\nexport function isKey(keys, event) {\r\n  keys = isArray(keys) ? keys : [ keys ];\r\n\r\n  return keys.indexOf(event.key) !== -1 || keys.indexOf(event.code) !== -1;\r\n}\r\n\r\n/**\r\n * @param {KeyboardEvent} event\r\n */\r\nexport function isShift(event) {\r\n  return event.shiftKey;\r\n}\r\n\r\n/**\r\n * @param {KeyboardEvent} event\r\n */\r\nexport function isCopy(event) {\r\n  return isCmd(event) && isKey(KEYS_COPY, event);\r\n}\r\n\r\n/**\r\n * @param {KeyboardEvent} event\r\n */\r\nexport function isPaste(event) {\r\n  return isCmd(event) && isKey(KEYS_PASTE, event);\r\n}\r\n\r\n/**\r\n * @param {KeyboardEvent} event\r\n */\r\nexport function isUndo(event) {\r\n  return isCmd(event) && !isShift(event) && isKey(KEYS_UNDO, event);\r\n}\r\n\r\n/**\r\n * @param {KeyboardEvent} event\r\n */\r\nexport function isRedo(event) {\r\n  return isCmd(event) && (\r\n    isKey(KEYS_REDO, event) || (\r\n      isKey(KEYS_UNDO, event) && isShift(event)\r\n    )\r\n  );\r\n}\r\n", "import {\n  isFunction\n} from 'min-dash';\n\nimport {\n  closest as domClosest,\n  event as domEvent,\n  matches as domMatches\n} from 'min-dom';\n\nimport {\n  hasModifier,\n  isCmd,\n  isKey,\n  isShift\n} from './KeyboardUtil';\n\n/**\n * @typedef {import('../../core/EventBus').default} EventBus\n *\n * @typedef {({ keyEvent: KeyboardEvent }) => any} Listener\n */\n\nvar KEYDOWN_EVENT = 'keyboard.keydown',\n    KEYUP_EVENT = 'keyboard.keyup';\n\nvar HANDLE_MODIFIER_ATTRIBUTE = 'input-handle-modified-keys';\n\nvar DEFAULT_PRIORITY = 1000;\n\n/**\n * A keyboard abstraction that may be activated and\n * deactivated by users at will, consuming global key events\n * and triggering diagram actions.\n *\n * For keys pressed down, keyboard fires `keyboard.keydown` event.\n * The event context contains one field which is `KeyboardEvent` event.\n *\n * The implementation fires the following key events that allow\n * other components to hook into key handling:\n *\n *  - keyboard.bind\n *  - keyboard.unbind\n *  - keyboard.init\n *  - keyboard.destroy\n *\n * All events contain one field which is node.\n *\n * A default binding for the keyboard may be specified via the\n * `keyboard.bindTo` configuration option.\n *\n * @param {Object} config\n * @param {EventTarget} [config.bindTo]\n * @param {EventBus} eventBus\n */\nexport default function Keyboard(config, eventBus) {\n  var self = this;\n\n  this._config = config || {};\n  this._eventBus = eventBus;\n\n  this._keydownHandler = this._keydownHandler.bind(this);\n  this._keyupHandler = this._keyupHandler.bind(this);\n\n  // properly clean dom registrations\n  eventBus.on('diagram.destroy', function() {\n    self._fire('destroy');\n\n    self.unbind();\n  });\n\n  eventBus.on('diagram.init', function() {\n    self._fire('init');\n  });\n\n  eventBus.on('attach', function() {\n    if (config && config.bindTo) {\n      self.bind(config.bindTo);\n    }\n  });\n\n  eventBus.on('detach', function() {\n    self.unbind();\n  });\n}\n\nKeyboard.$inject = [\n  'config.keyboard',\n  'eventBus'\n];\n\nKeyboard.prototype._keydownHandler = function(event) {\n  this._keyHandler(event, KEYDOWN_EVENT);\n};\n\nKeyboard.prototype._keyupHandler = function(event) {\n  this._keyHandler(event, KEYUP_EVENT);\n};\n\nKeyboard.prototype._keyHandler = function(event, type) {\n  var eventBusResult;\n\n  if (this._isEventIgnored(event)) {\n    return;\n  }\n\n  var context = {\n    keyEvent: event\n  };\n\n  eventBusResult = this._eventBus.fire(type || KEYDOWN_EVENT, context);\n\n  if (eventBusResult) {\n    event.preventDefault();\n  }\n};\n\nKeyboard.prototype._isEventIgnored = function(event) {\n  if (event.defaultPrevented) {\n    return true;\n  }\n\n  return (\n    isInput(event.target) || (\n      isButton(event.target) && isKey([ ' ', 'Enter' ], event)\n    )\n  ) && this._isModifiedKeyIgnored(event);\n};\n\nKeyboard.prototype._isModifiedKeyIgnored = function(event) {\n  if (!isCmd(event)) {\n    return true;\n  }\n\n  var allowedModifiers = this._getAllowedModifiers(event.target);\n  return allowedModifiers.indexOf(event.key) === -1;\n};\n\nKeyboard.prototype._getAllowedModifiers = function(element) {\n  var modifierContainer = domClosest(element, '[' + HANDLE_MODIFIER_ATTRIBUTE + ']', true);\n\n  if (!modifierContainer || (this._node && !this._node.contains(modifierContainer))) {\n    return [];\n  }\n\n  return modifierContainer.getAttribute(HANDLE_MODIFIER_ATTRIBUTE).split(',');\n};\n\n/**\n * Bind keyboard events to the given DOM node.\n *\n * @param {EventTarget} node\n */\nKeyboard.prototype.bind = function(node) {\n\n  // make sure that the keyboard is only bound once to the DOM\n  this.unbind();\n\n  this._node = node;\n\n  // bind key events\n  domEvent.bind(node, 'keydown', this._keydownHandler);\n  domEvent.bind(node, 'keyup', this._keyupHandler);\n\n  this._fire('bind');\n};\n\n/**\n * @return {EventTarget}\n */\nKeyboard.prototype.getBinding = function() {\n  return this._node;\n};\n\nKeyboard.prototype.unbind = function() {\n  var node = this._node;\n\n  if (node) {\n    this._fire('unbind');\n\n    // unbind key events\n    domEvent.unbind(node, 'keydown', this._keydownHandler);\n    domEvent.unbind(node, 'keyup', this._keyupHandler);\n  }\n\n  this._node = null;\n};\n\n/**\n * @param {string} event\n */\nKeyboard.prototype._fire = function(event) {\n  this._eventBus.fire('keyboard.' + event, { node: this._node });\n};\n\n/**\n * Add a listener function that is notified with `KeyboardEvent` whenever\n * the keyboard is bound and the user presses a key. If no priority is\n * provided, the default value of 1000 is used.\n *\n * @param {number} [priority]\n * @param {Listener} listener\n * @param {string} [type='keyboard.keydown']\n */\nKeyboard.prototype.addListener = function(priority, listener, type) {\n  if (isFunction(priority)) {\n    type = listener;\n    listener = priority;\n    priority = DEFAULT_PRIORITY;\n  }\n\n  this._eventBus.on(type || KEYDOWN_EVENT, priority, listener);\n};\n\n/**\n * Remove a listener function.\n *\n * @param {Listener} listener\n * @param {string} [type='keyboard.keydown']\n */\nKeyboard.prototype.removeListener = function(listener, type) {\n  this._eventBus.off(type || KEYDOWN_EVENT, listener);\n};\n\nKeyboard.prototype.hasModifier = hasModifier;\nKeyboard.prototype.isCmd = isCmd;\nKeyboard.prototype.isShift = isShift;\nKeyboard.prototype.isKey = isKey;\n\n\n\n// helpers ///////\n\nfunction isInput(target) {\n  return target && (domMatches(target, 'input, textarea') || target.contentEditable === 'true');\n}\n\nfunction isButton(target) {\n  return target && domMatches(target, 'button, input[type=submit], input[type=button], a[href], [aria-role=button]');\n}", "import {\n  isCmd,\n  is<PERSON><PERSON>,\n  is<PERSON><PERSON>,\n  is<PERSON>aste,\n  isUndo,\n  isRedo,\n  KEYS_COPY,\n  KEYS_PASTE,\n  KEYS_UNDO,\n  KEYS_REDO\n} from './KeyboardUtil';\n\n/**\n * @typedef {import('../editor-actions/EditorActions').default} EditorActions\n * @typedef {import('../../core/EventBus').default} EventBus\n * @typedef {import('./Keyboard').default} Keyboard\n */\n\nexport {\n  KEYS_COPY,\n  KEYS_PASTE,\n  KEYS_UNDO,\n  KEYS_REDO\n};\n\n\nvar LOW_PRIORITY = 500;\n\n\n/**\n * Adds default keyboard bindings.\n *\n * This does not pull in any features will bind only actions that\n * have previously been registered against the editorActions component.\n *\n * @param {EventBus} eventBus\n * @param {Keyboard} keyboard\n */\nexport default function KeyboardBindings(eventBus, keyboard) {\n\n  var self = this;\n\n  eventBus.on('editorActions.init', LOW_PRIORITY, function(event) {\n\n    var editorActions = event.editorActions;\n\n    self.registerBindings(keyboard, editorActions);\n  });\n}\n\nKeyboardBindings.$inject = [\n  'eventBus',\n  'keyboard'\n];\n\n\n/**\n * Register available keyboard bindings.\n *\n * @param {Keyboard} keyboard\n * @param {EditorActions} editorActions\n */\nKeyboardBindings.prototype.registerBindings = function(keyboard, editorActions) {\n\n  /**\n   * Add keyboard binding if respective editor action\n   * is registered.\n   *\n   * @param {string} action name\n   * @param {Function} fn that implements the key binding\n   */\n  function addListener(action, fn) {\n\n    if (editorActions.isRegistered(action)) {\n      keyboard.addListener(fn);\n    }\n  }\n\n\n  // undo\n  // (CTRL|CMD) + Z\n  addListener('undo', function(context) {\n\n    var event = context.keyEvent;\n\n    if (isUndo(event)) {\n      editorActions.trigger('undo');\n\n      return true;\n    }\n  });\n\n  // redo\n  // CTRL + Y\n  // CMD + SHIFT + Z\n  addListener('redo', function(context) {\n\n    var event = context.keyEvent;\n\n    if (isRedo(event)) {\n      editorActions.trigger('redo');\n\n      return true;\n    }\n  });\n\n  // copy\n  // CTRL/CMD + C\n  addListener('copy', function(context) {\n\n    var event = context.keyEvent;\n\n    if (isCopy(event)) {\n      editorActions.trigger('copy');\n\n      return true;\n    }\n  });\n\n  // paste\n  // CTRL/CMD + V\n  addListener('paste', function(context) {\n\n    var event = context.keyEvent;\n\n    if (isPaste(event)) {\n      editorActions.trigger('paste');\n\n      return true;\n    }\n  });\n\n  // zoom in one step\n  // CTRL/CMD + +\n  addListener('stepZoom', function(context) {\n\n    var event = context.keyEvent;\n\n    // quirk: it has to be triggered by `=` as well to work on international keyboard layout\n    // cf: https://github.com/bpmn-io/bpmn-js/issues/1362#issuecomment-722989754\n    if (isKey([ '+', 'Add', '=' ], event) && isCmd(event)) {\n      editorActions.trigger('stepZoom', { value: 1 });\n\n      return true;\n    }\n  });\n\n  // zoom out one step\n  // CTRL + -\n  addListener('stepZoom', function(context) {\n\n    var event = context.keyEvent;\n\n    if (isKey([ '-', 'Subtract' ], event) && isCmd(event)) {\n      editorActions.trigger('stepZoom', { value: -1 });\n\n      return true;\n    }\n  });\n\n  // zoom to the default level\n  // CTRL + 0\n  addListener('zoom', function(context) {\n\n    var event = context.keyEvent;\n\n    if (isKey('0', event) && isCmd(event)) {\n      editorActions.trigger('zoom', { value: 1 });\n\n      return true;\n    }\n  });\n\n  // delete selected element\n  // DEL\n  addListener('removeSelection', function(context) {\n\n    var event = context.keyEvent;\n\n    if (isKey([ 'Backspace', 'Delete', 'Del' ], event)) {\n      editorActions.trigger('removeSelection');\n\n      return true;\n    }\n  });\n};", "import Keyboard from './Keyboard';\nimport KeyboardBindings from './KeyboardBindings';\n\n\n/**\n * @type { import('didi').ModuleDeclaration }\n */\nexport default {\n  __init__: [ 'keyboard', 'keyboardBindings' ],\n  keyboard: [ 'type', Keyboard ],\n  keyboardBindings: [ 'type', KeyboardBindings ]\n};\n", "import { assign } from 'min-dash';\n\n/**\n * @typedef {import('../../core/Canvas').default} Canvas\n * @typedef {import('../../features/keyboard/Keyboard').default} Keyboard\n */\n\nvar DEFAULT_CONFIG = {\n  moveSpeed: 50,\n  moveSpeedAccelerated: 200\n};\n\n\n/**\n * A feature that allows users to move the canvas using the keyboard.\n *\n * @param {Object} config\n * @param {number} [config.moveSpeed=50]\n * @param {number} [config.moveSpeedAccelerated=200]\n * @param {Keyboard} keyboard\n * @param {Canvas} canvas\n */\nexport default function KeyboardMove(\n    config,\n    keyboard,\n    canvas\n) {\n\n  var self = this;\n\n  this._config = assign({}, DEFAULT_CONFIG, config || {});\n\n  keyboard.addListener(arrowsListener);\n\n\n  function arrowsListener(context) {\n\n    var event = context.keyEvent,\n        config = self._config;\n\n    if (!keyboard.isCmd(event)) {\n      return;\n    }\n\n    if (keyboard.isKey([\n      'ArrowLeft', 'Left',\n      'ArrowUp', 'Up',\n      'ArrowDown', 'Down',\n      'ArrowRight', 'Right'\n    ], event)) {\n\n      var speed = (\n        keyboard.isShift(event) ?\n          config.moveSpeedAccelerated :\n          config.moveSpeed\n      );\n\n      var direction;\n\n      switch (event.key) {\n      case 'ArrowLeft':\n      case 'Left':\n        direction = 'left';\n        break;\n      case 'ArrowUp':\n      case 'Up':\n        direction = 'up';\n        break;\n      case 'ArrowRight':\n      case 'Right':\n        direction = 'right';\n        break;\n      case 'ArrowDown':\n      case 'Down':\n        direction = 'down';\n        break;\n      }\n\n      self.moveCanvas({\n        speed: speed,\n        direction: direction\n      });\n\n      return true;\n    }\n  }\n\n  /**\n   * @param {{\n   *   direction: 'up' | 'down' | 'left' | 'right';\n   *   speed: number;\n   * }} options\n   */\n  this.moveCanvas = function(options) {\n\n    var dx = 0,\n        dy = 0,\n        speed = options.speed;\n\n    var actualSpeed = speed / Math.min(Math.sqrt(canvas.viewbox().scale), 1);\n\n    switch (options.direction) {\n    case 'left': // Left\n      dx = actualSpeed;\n      break;\n    case 'up': // Up\n      dy = actualSpeed;\n      break;\n    case 'right': // Right\n      dx = -actualSpeed;\n      break;\n    case 'down': // Down\n      dy = -actualSpeed;\n      break;\n    }\n\n    canvas.scroll({\n      dx: dx,\n      dy: dy\n    });\n  };\n\n}\n\n\nKeyboardMove.$inject = [\n  'config.keyboardMove',\n  'keyboard',\n  'canvas'\n];\n", "import KeyboardModule from '../../features/keyboard';\n\nimport KeyboardMove from './KeyboardMove';\n\n\n/**\n * @type { import('didi').ModuleDeclaration }\n */\nexport default {\n  __depends__: [\n    KeyboardModule\n  ],\n  __init__: [ 'keyboardMove' ],\n  keyboardMove: [ 'type', KeyboardMove ]\n};", "import {\n  classes as domClasses\n} from 'min-dom';\n\nvar CURSOR_CLS_PATTERN = /^djs-cursor-.*$/;\n\n/**\n * @param {string} mode\n */\nexport function set(mode) {\n  var classes = domClasses(document.body);\n\n  classes.removeMatching(CURSOR_CLS_PATTERN);\n\n  if (mode) {\n    classes.add('djs-cursor-' + mode);\n  }\n}\n\nexport function unset() {\n  set(null);\n}\n\n/**\n * @param {string} mode\n *\n * @return {boolean}\n */\nexport function has(mode) {\n  var classes = domClasses(document.body);\n\n  return classes.has('djs-cursor-' + mode);\n}\n", "/**\n * @typedef {import('../core/EventBus').default} EventBus\n */\n\nvar TRAP_PRIORITY = 5000;\n\n/**\n * Installs a click trap that prevents a ghost click following a dragging operation.\n *\n * @param {EventBus} eventBus\n * @param {string} [eventName='element.click']\n *\n * @return {() => void} a function to immediately remove the installed trap.\n */\nexport function install(eventBus, eventName) {\n\n  eventName = eventName || 'element.click';\n\n  function trap() {\n    return false;\n  }\n\n  eventBus.once(eventName, TRAP_PRIORITY, trap);\n\n  return function() {\n    eventBus.off(eventName, trap);\n  };\n}", "/**\n * @typedef {import('../util/Types').Point} Point\n * @typedef {import('../util/Types').Rect} Rect\n */\n\n/**\n * @param {Rect} bounds\n * @return {Point}\n */\nexport function center(bounds) {\n  return {\n    x: bounds.x + (bounds.width / 2),\n    y: bounds.y + (bounds.height / 2)\n  };\n}\n\n\n/**\n * @param {Point} a\n * @param {Point} b\n * @return {Point}\n */\nexport function delta(a, b) {\n  return {\n    x: a.x - b.x,\n    y: a.y - b.y\n  };\n}\n", "import {\n  set as cursorSet,\n  unset as cursorUnset\n} from '../../util/Cursor';\n\nimport {\n  install as installClickTrap\n} from '../../util/ClickTrap';\n\nimport {\n  delta as deltaPos\n} from '../../util/PositionUtil';\n\nimport {\n  event as domEvent,\n  closest as domClosest\n} from 'min-dom';\n\nimport {\n  toPoint\n} from '../../util/Event';\n\n/**\n * @typedef {import('../../core/Canvas').default} Canvas\n * @typedef {import('../../core/EventBus').default} EventBus\n */\n\nvar THRESHOLD = 15;\n\n\n/**\n * Move the canvas via mouse.\n *\n * @param {EventBus} eventBus\n * @param {Canvas} canvas\n */\nexport default function MoveCanvas(eventBus, canvas) {\n\n  var context;\n\n\n  // listen for move on element mouse down;\n  // allow others to hook into the event before us though\n  // (dragging / element moving will do this)\n  eventBus.on('element.mousedown', 500, function(e) {\n    return handleStart(e.originalEvent);\n  });\n\n\n  function handleMove(event) {\n\n    var start = context.start,\n        button = context.button,\n        position = toPoint(event),\n        delta = deltaPos(position, start);\n\n    if (!context.dragging && length(delta) > THRESHOLD) {\n      context.dragging = true;\n\n      if (button === 0) {\n        installClickTrap(eventBus);\n      }\n\n      cursorSet('grab');\n    }\n\n    if (context.dragging) {\n\n      var lastPosition = context.last || context.start;\n\n      delta = deltaPos(position, lastPosition);\n\n      canvas.scroll({\n        dx: delta.x,\n        dy: delta.y\n      });\n\n      context.last = position;\n    }\n\n    // prevent select\n    event.preventDefault();\n  }\n\n\n  function handleEnd(event) {\n    domEvent.unbind(document, 'mousemove', handleMove);\n    domEvent.unbind(document, 'mouseup', handleEnd);\n\n    context = null;\n\n    cursorUnset();\n  }\n\n  function handleStart(event) {\n\n    // event is already handled by '.djs-draggable'\n    if (domClosest(event.target, '.djs-draggable')) {\n      return;\n    }\n\n    var button = event.button;\n\n    // reject right mouse button or modifier key\n    if (button >= 2 || event.ctrlKey || event.shiftKey || event.altKey) {\n      return;\n    }\n\n    context = {\n      button: button,\n      start: toPoint(event)\n    };\n\n    domEvent.bind(document, 'mousemove', handleMove);\n    domEvent.bind(document, 'mouseup', handleEnd);\n\n    // we've handled the event\n    return true;\n  }\n\n  this.isActive = function() {\n    return !!context;\n  };\n\n}\n\n\nMoveCanvas.$inject = [\n  'eventBus',\n  'canvas'\n];\n\n\n\n// helpers ///////\n\nfunction length(point) {\n  return Math.sqrt(Math.pow(point.x, 2) + Math.pow(point.y, 2));\n}\n", "import MoveCanvas from './MoveCanvas';\n\n\n/**\n * @type { import('didi').ModuleDeclaration }\n */\nexport default {\n  __init__: [ 'moveCanvas' ],\n  moveCanvas: [ 'type', MoveCanvas ]\n};", "/**\n * Get the logarithm of x with base 10.\n *\n * @param {number} x\n */\nexport function log10(x) {\n  return Math.log(x) / Math.log(10);\n}\n\nexport { delta as substract } from './PositionUtil';\n", "import {\n  log10\n} from '../../util/Math';\n\n/**\n * Get step size for given range and number of steps.\n *\n * @param {Object} range\n * @param {number} range.min\n * @param {number} range.max\n * @param {number} steps\n */\nexport function getStepSize(range, steps) {\n\n  var minLinearRange = log10(range.min),\n      maxLinearRange = log10(range.max);\n\n  var absoluteLinearRange = Math.abs(minLinearRange) + Math.abs(maxLinearRange);\n\n  return absoluteLinearRange / steps;\n}\n\n/**\n * @param {Object} range\n * @param {number} range.min\n * @param {number} range.max\n * @param {number} scale\n */\nexport function cap(range, scale) {\n  return Math.max(range.min, Math.min(range.max, scale));\n}\n", "import {\n  event as domEvent,\n  closest as domClosest\n} from 'min-dom';\n\nimport {\n  getStepSize,\n  cap\n} from './ZoomUtil';\n\nimport {\n  log10\n} from '../../util/Math';\n\nimport {\n  isMac\n} from '../../util/Platform';\n\nimport {\n  bind\n} from 'min-dash';\n\n/**\n * @typedef {import('../../core/Canvas').default} Canvas\n * @typedef {import('../../core/EventBus').default} EventBus\n *\n * @typedef {import('../../util/Types').Point} Point\n * @typedef {import('../../util/Types').ScrollDelta} ScrollDelta\n */\n\nvar sign = Math.sign || function(n) {\n  return n >= 0 ? 1 : -1;\n};\n\nvar RANGE = { min: 0.2, max: 4 },\n    NUM_STEPS = 10;\n\nvar DELTA_THRESHOLD = 0.1;\n\nvar DEFAULT_SCALE = 0.75;\n\n/**\n * An implementation of zooming and scrolling within the\n * {@link Canvas} via the mouse wheel.\n *\n * Mouse wheel zooming / scrolling may be disabled using\n * the {@link toggle(enabled)} method.\n *\n * @param {Object} [config]\n * @param {boolean} [config.enabled=true] default enabled state\n * @param {number} [config.scale=.75] scroll sensivity\n * @param {EventBus} eventBus\n * @param {Canvas} canvas\n */\nexport default function ZoomScroll(config, eventBus, canvas) {\n\n  config = config || {};\n\n  this._enabled = false;\n\n  this._canvas = canvas;\n  this._container = canvas._container;\n\n  this._handleWheel = bind(this._handleWheel, this);\n\n  this._totalDelta = 0;\n  this._scale = config.scale || DEFAULT_SCALE;\n\n  var self = this;\n\n  eventBus.on('canvas.init', function(e) {\n    self._init(config.enabled !== false);\n  });\n}\n\nZoomScroll.$inject = [\n  'config.zoomScroll',\n  'eventBus',\n  'canvas'\n];\n\n/**\n * @param {ScrollDelta} delta\n */\nZoomScroll.prototype.scroll = function scroll(delta) {\n  this._canvas.scroll(delta);\n};\n\n\nZoomScroll.prototype.reset = function reset() {\n  this._canvas.zoom('fit-viewport');\n};\n\n/**\n * Zoom depending on delta.\n *\n * @param {number} delta\n * @param {Point} position\n */\nZoomScroll.prototype.zoom = function zoom(delta, position) {\n\n  // zoom with half the step size of stepZoom\n  var stepSize = getStepSize(RANGE, NUM_STEPS * 2);\n\n  // add until threshold reached\n  this._totalDelta += delta;\n\n  if (Math.abs(this._totalDelta) > DELTA_THRESHOLD) {\n    this._zoom(delta, position, stepSize);\n\n    // reset\n    this._totalDelta = 0;\n  }\n};\n\n\nZoomScroll.prototype._handleWheel = function handleWheel(event) {\n\n  // event is already handled by '.djs-scrollable'\n  if (domClosest(event.target, '.djs-scrollable', true)) {\n    return;\n  }\n\n  var element = this._container;\n\n  event.preventDefault();\n\n  // pinch to zoom is mapped to wheel + ctrlKey = true\n  // in modern browsers (!)\n\n  var isZoom = event.ctrlKey || (isMac() && event.metaKey);\n\n  var isHorizontalScroll = event.shiftKey;\n\n  var factor = -1 * this._scale,\n      delta;\n\n  if (isZoom) {\n    factor *= event.deltaMode === 0 ? 0.020 : 0.32;\n  } else {\n    factor *= event.deltaMode === 0 ? 1.0 : 16.0;\n  }\n\n  if (isZoom) {\n    var elementRect = element.getBoundingClientRect();\n\n    var offset = {\n      x: event.clientX - elementRect.left,\n      y: event.clientY - elementRect.top\n    };\n\n    delta = (\n      Math.sqrt(\n        Math.pow(event.deltaY, 2) +\n        Math.pow(event.deltaX, 2)\n      ) * sign(event.deltaY) * factor\n    );\n\n    // zoom in relative to diagram {x,y} coordinates\n    this.zoom(delta, offset);\n  } else {\n\n    if (isHorizontalScroll) {\n      delta = {\n        dx: factor * event.deltaY,\n        dy: 0\n      };\n    } else {\n      delta = {\n        dx: factor * event.deltaX,\n        dy: factor * event.deltaY\n      };\n    }\n\n    this.scroll(delta);\n  }\n};\n\n/**\n * Zoom with fixed step size.\n *\n * @param {number} delta Zoom delta (1 for zooming in, -1 for zooming out).\n * @param {Point} [position]\n */\nZoomScroll.prototype.stepZoom = function stepZoom(delta, position) {\n\n  var stepSize = getStepSize(RANGE, NUM_STEPS);\n\n  this._zoom(delta, position, stepSize);\n};\n\n\n/**\n * Zoom in/out given a step size.\n *\n * @param {number} delta\n * @param {Point} [position]\n * @param {number} stepSize\n */\nZoomScroll.prototype._zoom = function(delta, position, stepSize) {\n  var canvas = this._canvas;\n\n  var direction = delta > 0 ? 1 : -1;\n\n  var currentLinearZoomLevel = log10(canvas.zoom());\n\n  // snap to a proximate zoom step\n  var newLinearZoomLevel = Math.round(currentLinearZoomLevel / stepSize) * stepSize;\n\n  // increase or decrease one zoom step in the given direction\n  newLinearZoomLevel += stepSize * direction;\n\n  // calculate the absolute logarithmic zoom level based on the linear zoom level\n  // (e.g. 2 for an absolute x2 zoom)\n  var newLogZoomLevel = Math.pow(10, newLinearZoomLevel);\n\n  canvas.zoom(cap(RANGE, newLogZoomLevel), position);\n};\n\n\n/**\n * Toggle the zoom scroll ability via mouse wheel.\n *\n * @param {boolean} [newEnabled] new enabled state\n */\nZoomScroll.prototype.toggle = function toggle(newEnabled) {\n\n  var element = this._container;\n  var handleWheel = this._handleWheel;\n\n  var oldEnabled = this._enabled;\n\n  if (typeof newEnabled === 'undefined') {\n    newEnabled = !oldEnabled;\n  }\n\n  // only react on actual changes\n  if (oldEnabled !== newEnabled) {\n\n    // add or remove wheel listener based on\n    // changed enabled state\n    domEvent[newEnabled ? 'bind' : 'unbind'](element, 'wheel', handleWheel, false);\n  }\n\n  this._enabled = newEnabled;\n\n  return newEnabled;\n};\n\n\nZoomScroll.prototype._init = function(newEnabled) {\n  this.toggle(newEnabled);\n};\n", "import ZoomScroll from './ZoomScroll';\n\n\n/**\n * @type { import('didi').ModuleDeclaration }\n */\nexport default {\n  __init__: [ 'zoomScroll' ],\n  zoomScroll: [ 'type', ZoomScroll ]\n};", "import inherits from 'inherits-browser';\n\nimport Viewer from './Viewer';\n\nimport KeyboardMoveModule from 'diagram-js/lib/navigation/keyboard-move';\nimport MoveCanvasModule from 'diagram-js/lib/navigation/movecanvas';\nimport ZoomScrollModule from 'diagram-js/lib/navigation/zoomscroll';\n\n/**\n * @typedef { import('./BaseViewer').BaseViewerOptions } BaseViewerOptions\n */\n\n/**\n * A viewer with mouse and keyboard navigation features.\n *\n * @template [ServiceMap=null]\n *\n * @extends Viewer<ServiceMap>\n *\n * @param {BaseViewerOptions} [options]\n */\nexport default function NavigatedViewer(options) {\n  Viewer.call(this, options);\n}\n\ninherits(NavigatedViewer, Viewer);\n\n\nNavigatedViewer.prototype._navigationModules = [\n  KeyboardMoveModule,\n  MoveCanvasModule,\n  ZoomScrollModule\n];\n\nNavigatedViewer.prototype._modules = [].concat(\n  Viewer.prototype._modules,\n  NavigatedViewer.prototype._navigationModules\n);"], "mappings": ";;;;;;;;;;;;;;;;;AAEO,IAAI,YAAY,CAAE,KAAK,GAAI;AAC3B,IAAI,aAAa,CAAE,KAAK,GAAI;AAC5B,IAAI,YAAY,CAAE,KAAK,GAAI;AAC3B,IAAI,YAAY,CAAE,KAAK,GAAI;AAM3B,SAAS,YAAYA,QAAO;AACjC,SAAQA,OAAM,WAAWA,OAAM,WAAWA,OAAM,YAAYA,OAAM;AACpE;AAMO,SAAS,MAAMA,QAAO;AAI3B,MAAIA,OAAM,QAAQ;AAChB,WAAO;AAAA,EACT;AAEA,SAAOA,OAAM,WAAWA,OAAM;AAChC;AASO,SAAS,MAAM,MAAMA,QAAO;AACjC,SAAO,QAAQ,IAAI,IAAI,OAAO,CAAE,IAAK;AAErC,SAAO,KAAK,QAAQA,OAAM,GAAG,MAAM,MAAM,KAAK,QAAQA,OAAM,IAAI,MAAM;AACxE;AAKO,SAAS,QAAQA,QAAO;AAC7B,SAAOA,OAAM;AACf;AAKO,SAAS,OAAOA,QAAO;AAC5B,SAAO,MAAMA,MAAK,KAAK,MAAM,WAAWA,MAAK;AAC/C;AAKO,SAAS,QAAQA,QAAO;AAC7B,SAAO,MAAMA,MAAK,KAAK,MAAM,YAAYA,MAAK;AAChD;AAKO,SAAS,OAAOA,QAAO;AAC5B,SAAO,MAAMA,MAAK,KAAK,CAAC,QAAQA,MAAK,KAAK,MAAM,WAAWA,MAAK;AAClE;AAKO,SAAS,OAAOA,QAAO;AAC5B,SAAO,MAAMA,MAAK,MAChB,MAAM,WAAWA,MAAK,KACpB,MAAM,WAAWA,MAAK,KAAK,QAAQA,MAAK;AAG9C;;;ACzDA,IAAI,gBAAgB;AAApB,IACI,cAAc;AAElB,IAAI,4BAA4B;AAEhC,IAAI,mBAAmB;AA2BR,SAAR,SAA0B,QAAQ,UAAU;AACjD,MAAI,OAAO;AAEX,OAAK,UAAU,UAAU,CAAC;AAC1B,OAAK,YAAY;AAEjB,OAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AACrD,OAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AAGjD,WAAS,GAAG,mBAAmB,WAAW;AACxC,SAAK,MAAM,SAAS;AAEpB,SAAK,OAAO;AAAA,EACd,CAAC;AAED,WAAS,GAAG,gBAAgB,WAAW;AACrC,SAAK,MAAM,MAAM;AAAA,EACnB,CAAC;AAED,WAAS,GAAG,UAAU,WAAW;AAC/B,QAAI,UAAU,OAAO,QAAQ;AAC3B,WAAK,KAAK,OAAO,MAAM;AAAA,IACzB;AAAA,EACF,CAAC;AAED,WAAS,GAAG,UAAU,WAAW;AAC/B,SAAK,OAAO;AAAA,EACd,CAAC;AACH;AAEA,SAAS,UAAU;AAAA,EACjB;AAAA,EACA;AACF;AAEA,SAAS,UAAU,kBAAkB,SAASC,QAAO;AACnD,OAAK,YAAYA,QAAO,aAAa;AACvC;AAEA,SAAS,UAAU,gBAAgB,SAASA,QAAO;AACjD,OAAK,YAAYA,QAAO,WAAW;AACrC;AAEA,SAAS,UAAU,cAAc,SAASA,QAAO,MAAM;AACrD,MAAI;AAEJ,MAAI,KAAK,gBAAgBA,MAAK,GAAG;AAC/B;AAAA,EACF;AAEA,MAAI,UAAU;AAAA,IACZ,UAAUA;AAAA,EACZ;AAEA,mBAAiB,KAAK,UAAU,KAAK,QAAQ,eAAe,OAAO;AAEnE,MAAI,gBAAgB;AAClB,IAAAA,OAAM,eAAe;AAAA,EACvB;AACF;AAEA,SAAS,UAAU,kBAAkB,SAASA,QAAO;AACnD,MAAIA,OAAM,kBAAkB;AAC1B,WAAO;AAAA,EACT;AAEA,UACE,QAAQA,OAAM,MAAM,KAClB,SAASA,OAAM,MAAM,KAAK,MAAM,CAAE,KAAK,OAAQ,GAAGA,MAAK,MAEtD,KAAK,sBAAsBA,MAAK;AACvC;AAEA,SAAS,UAAU,wBAAwB,SAASA,QAAO;AACzD,MAAI,CAAC,MAAMA,MAAK,GAAG;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,mBAAmB,KAAK,qBAAqBA,OAAM,MAAM;AAC7D,SAAO,iBAAiB,QAAQA,OAAM,GAAG,MAAM;AACjD;AAEA,SAAS,UAAU,uBAAuB,SAAS,SAAS;AAC1D,MAAI,oBAAoB,QAAW,SAAS,MAAM,4BAA4B,KAAK,IAAI;AAEvF,MAAI,CAAC,qBAAsB,KAAK,SAAS,CAAC,KAAK,MAAM,SAAS,iBAAiB,GAAI;AACjF,WAAO,CAAC;AAAA,EACV;AAEA,SAAO,kBAAkB,aAAa,yBAAyB,EAAE,MAAM,GAAG;AAC5E;AAOA,SAAS,UAAU,OAAO,SAAS,MAAM;AAGvC,OAAK,OAAO;AAEZ,OAAK,QAAQ;AAGb,QAAS,KAAK,MAAM,WAAW,KAAK,eAAe;AACnD,QAAS,KAAK,MAAM,SAAS,KAAK,aAAa;AAE/C,OAAK,MAAM,MAAM;AACnB;AAKA,SAAS,UAAU,aAAa,WAAW;AACzC,SAAO,KAAK;AACd;AAEA,SAAS,UAAU,SAAS,WAAW;AACrC,MAAI,OAAO,KAAK;AAEhB,MAAI,MAAM;AACR,SAAK,MAAM,QAAQ;AAGnB,UAAS,OAAO,MAAM,WAAW,KAAK,eAAe;AACrD,UAAS,OAAO,MAAM,SAAS,KAAK,aAAa;AAAA,EACnD;AAEA,OAAK,QAAQ;AACf;AAKA,SAAS,UAAU,QAAQ,SAASA,QAAO;AACzC,OAAK,UAAU,KAAK,cAAcA,QAAO,EAAE,MAAM,KAAK,MAAM,CAAC;AAC/D;AAWA,SAAS,UAAU,cAAc,SAAS,UAAU,UAAU,MAAM;AAClE,MAAI,WAAW,QAAQ,GAAG;AACxB,WAAO;AACP,eAAW;AACX,eAAW;AAAA,EACb;AAEA,OAAK,UAAU,GAAG,QAAQ,eAAe,UAAU,QAAQ;AAC7D;AAQA,SAAS,UAAU,iBAAiB,SAAS,UAAU,MAAM;AAC3D,OAAK,UAAU,IAAI,QAAQ,eAAe,QAAQ;AACpD;AAEA,SAAS,UAAU,cAAc;AACjC,SAAS,UAAU,QAAQ;AAC3B,SAAS,UAAU,UAAU;AAC7B,SAAS,UAAU,QAAQ;AAM3B,SAAS,QAAQ,QAAQ;AACvB,SAAO,WAAW,QAAW,QAAQ,iBAAiB,KAAK,OAAO,oBAAoB;AACxF;AAEA,SAAS,SAAS,QAAQ;AACxB,SAAO,UAAU,QAAW,QAAQ,6EAA6E;AACnH;;;ACpNA,IAAI,eAAe;AAYJ,SAAR,iBAAkC,UAAU,UAAU;AAE3D,MAAI,OAAO;AAEX,WAAS,GAAG,sBAAsB,cAAc,SAASC,QAAO;AAE9D,QAAI,gBAAgBA,OAAM;AAE1B,SAAK,iBAAiB,UAAU,aAAa;AAAA,EAC/C,CAAC;AACH;AAEA,iBAAiB,UAAU;AAAA,EACzB;AAAA,EACA;AACF;AASA,iBAAiB,UAAU,mBAAmB,SAAS,UAAU,eAAe;AAS9E,WAAS,YAAY,QAAQ,IAAI;AAE/B,QAAI,cAAc,aAAa,MAAM,GAAG;AACtC,eAAS,YAAY,EAAE;AAAA,IACzB;AAAA,EACF;AAKA,cAAY,QAAQ,SAAS,SAAS;AAEpC,QAAIA,SAAQ,QAAQ;AAEpB,QAAI,OAAOA,MAAK,GAAG;AACjB,oBAAc,QAAQ,MAAM;AAE5B,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AAKD,cAAY,QAAQ,SAAS,SAAS;AAEpC,QAAIA,SAAQ,QAAQ;AAEpB,QAAI,OAAOA,MAAK,GAAG;AACjB,oBAAc,QAAQ,MAAM;AAE5B,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AAID,cAAY,QAAQ,SAAS,SAAS;AAEpC,QAAIA,SAAQ,QAAQ;AAEpB,QAAI,OAAOA,MAAK,GAAG;AACjB,oBAAc,QAAQ,MAAM;AAE5B,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AAID,cAAY,SAAS,SAAS,SAAS;AAErC,QAAIA,SAAQ,QAAQ;AAEpB,QAAI,QAAQA,MAAK,GAAG;AAClB,oBAAc,QAAQ,OAAO;AAE7B,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AAID,cAAY,YAAY,SAAS,SAAS;AAExC,QAAIA,SAAQ,QAAQ;AAIpB,QAAI,MAAM,CAAE,KAAK,OAAO,GAAI,GAAGA,MAAK,KAAK,MAAMA,MAAK,GAAG;AACrD,oBAAc,QAAQ,YAAY,EAAE,OAAO,EAAE,CAAC;AAE9C,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AAID,cAAY,YAAY,SAAS,SAAS;AAExC,QAAIA,SAAQ,QAAQ;AAEpB,QAAI,MAAM,CAAE,KAAK,UAAW,GAAGA,MAAK,KAAK,MAAMA,MAAK,GAAG;AACrD,oBAAc,QAAQ,YAAY,EAAE,OAAO,GAAG,CAAC;AAE/C,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AAID,cAAY,QAAQ,SAAS,SAAS;AAEpC,QAAIA,SAAQ,QAAQ;AAEpB,QAAI,MAAM,KAAKA,MAAK,KAAK,MAAMA,MAAK,GAAG;AACrC,oBAAc,QAAQ,QAAQ,EAAE,OAAO,EAAE,CAAC;AAE1C,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AAID,cAAY,mBAAmB,SAAS,SAAS;AAE/C,QAAIA,SAAQ,QAAQ;AAEpB,QAAI,MAAM,CAAE,aAAa,UAAU,KAAM,GAAGA,MAAK,GAAG;AAClD,oBAAc,QAAQ,iBAAiB;AAEvC,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;;;ACnLA,IAAO,mBAAQ;AAAA,EACb,UAAU,CAAE,YAAY,kBAAmB;AAAA,EAC3C,UAAU,CAAE,QAAQ,QAAS;AAAA,EAC7B,kBAAkB,CAAE,QAAQ,gBAAiB;AAC/C;;;ACJA,IAAI,iBAAiB;AAAA,EACnB,WAAW;AAAA,EACX,sBAAsB;AACxB;AAYe,SAAR,aACH,QACA,UACA,QACF;AAEA,MAAI,OAAO;AAEX,OAAK,UAAU,OAAO,CAAC,GAAG,gBAAgB,UAAU,CAAC,CAAC;AAEtD,WAAS,YAAY,cAAc;AAGnC,WAAS,eAAe,SAAS;AAE/B,QAAIC,SAAQ,QAAQ,UAChBC,UAAS,KAAK;AAElB,QAAI,CAAC,SAAS,MAAMD,MAAK,GAAG;AAC1B;AAAA,IACF;AAEA,QAAI,SAAS,MAAM;AAAA,MACjB;AAAA,MAAa;AAAA,MACb;AAAA,MAAW;AAAA,MACX;AAAA,MAAa;AAAA,MACb;AAAA,MAAc;AAAA,IAChB,GAAGA,MAAK,GAAG;AAET,UAAI,QACF,SAAS,QAAQA,MAAK,IACpBC,QAAO,uBACPA,QAAO;AAGX,UAAI;AAEJ,cAAQD,OAAM,KAAK;AAAA,QACnB,KAAK;AAAA,QACL,KAAK;AACH,sBAAY;AACZ;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,sBAAY;AACZ;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,sBAAY;AACZ;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,sBAAY;AACZ;AAAA,MACF;AAEA,WAAK,WAAW;AAAA,QACd;AAAA,QACA;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAAA,EACF;AAQA,OAAK,aAAa,SAAS,SAAS;AAElC,QAAI,KAAK,GACL,KAAK,GACL,QAAQ,QAAQ;AAEpB,QAAI,cAAc,QAAQ,KAAK,IAAI,KAAK,KAAK,OAAO,QAAQ,EAAE,KAAK,GAAG,CAAC;AAEvE,YAAQ,QAAQ,WAAW;AAAA,MAC3B,KAAK;AACH,aAAK;AACL;AAAA,MACF,KAAK;AACH,aAAK;AACL;AAAA,MACF,KAAK;AACH,aAAK,CAAC;AACN;AAAA,MACF,KAAK;AACH,aAAK,CAAC;AACN;AAAA,IACF;AAEA,WAAO,OAAO;AAAA,MACZ;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEF;AAGA,aAAa,UAAU;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AACF;;;ACzHA,IAAO,wBAAQ;AAAA,EACb,aAAa;AAAA,IACX;AAAA,EACF;AAAA,EACA,UAAU,CAAE,cAAe;AAAA,EAC3B,cAAc,CAAE,QAAQ,YAAa;AACvC;;;ACVA,IAAI,qBAAqB;AAKlB,SAAS,IAAI,MAAM;AACxB,MAAIE,WAAU,QAAW,SAAS,IAAI;AAEtC,EAAAA,SAAQ,eAAe,kBAAkB;AAEzC,MAAI,MAAM;AACR,IAAAA,SAAQ,IAAI,gBAAgB,IAAI;AAAA,EAClC;AACF;AAEO,SAAS,QAAQ;AACtB,MAAI,IAAI;AACV;;;ACjBA,IAAI,gBAAgB;AAUb,SAAS,QAAQ,UAAU,WAAW;AAE3C,cAAY,aAAa;AAEzB,WAAS,OAAO;AACd,WAAO;AAAA,EACT;AAEA,WAAS,KAAK,WAAW,eAAe,IAAI;AAE5C,SAAO,WAAW;AAChB,aAAS,IAAI,WAAW,IAAI;AAAA,EAC9B;AACF;;;ACLO,SAAS,MAAM,GAAG,GAAG;AAC1B,SAAO;AAAA,IACL,GAAG,EAAE,IAAI,EAAE;AAAA,IACX,GAAG,EAAE,IAAI,EAAE;AAAA,EACb;AACF;;;ACAA,IAAI,YAAY;AASD,SAAR,WAA4B,UAAU,QAAQ;AAEnD,MAAI;AAMJ,WAAS,GAAG,qBAAqB,KAAK,SAASC,IAAG;AAChD,WAAO,YAAYA,GAAE,aAAa;AAAA,EACpC,CAAC;AAGD,WAAS,WAAWC,QAAO;AAEzB,QAAI,QAAQ,QAAQ,OAChB,SAAS,QAAQ,QACjB,WAAW,QAAQA,MAAK,GACxBC,SAAQ,MAAS,UAAU,KAAK;AAEpC,QAAI,CAAC,QAAQ,YAAY,OAAOA,MAAK,IAAI,WAAW;AAClD,cAAQ,WAAW;AAEnB,UAAI,WAAW,GAAG;AAChB,gBAAiB,QAAQ;AAAA,MAC3B;AAEA,UAAU,MAAM;AAAA,IAClB;AAEA,QAAI,QAAQ,UAAU;AAEpB,UAAI,eAAe,QAAQ,QAAQ,QAAQ;AAE3C,MAAAA,SAAQ,MAAS,UAAU,YAAY;AAEvC,aAAO,OAAO;AAAA,QACZ,IAAIA,OAAM;AAAA,QACV,IAAIA,OAAM;AAAA,MACZ,CAAC;AAED,cAAQ,OAAO;AAAA,IACjB;AAGA,IAAAD,OAAM,eAAe;AAAA,EACvB;AAGA,WAAS,UAAUA,QAAO;AACxB,UAAS,OAAO,UAAU,aAAa,UAAU;AACjD,UAAS,OAAO,UAAU,WAAW,SAAS;AAE9C,cAAU;AAEV,UAAY;AAAA,EACd;AAEA,WAAS,YAAYA,QAAO;AAG1B,QAAI,QAAWA,OAAM,QAAQ,gBAAgB,GAAG;AAC9C;AAAA,IACF;AAEA,QAAI,SAASA,OAAM;AAGnB,QAAI,UAAU,KAAKA,OAAM,WAAWA,OAAM,YAAYA,OAAM,QAAQ;AAClE;AAAA,IACF;AAEA,cAAU;AAAA,MACR;AAAA,MACA,OAAO,QAAQA,MAAK;AAAA,IACtB;AAEA,UAAS,KAAK,UAAU,aAAa,UAAU;AAC/C,UAAS,KAAK,UAAU,WAAW,SAAS;AAG5C,WAAO;AAAA,EACT;AAEA,OAAK,WAAW,WAAW;AACzB,WAAO,CAAC,CAAC;AAAA,EACX;AAEF;AAGA,WAAW,UAAU;AAAA,EACnB;AAAA,EACA;AACF;AAMA,SAAS,OAAO,OAAO;AACrB,SAAO,KAAK,KAAK,KAAK,IAAI,MAAM,GAAG,CAAC,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC,CAAC;AAC9D;;;ACpIA,IAAO,qBAAQ;AAAA,EACb,UAAU,CAAE,YAAa;AAAA,EACzB,YAAY,CAAE,QAAQ,UAAW;AACnC;;;ACJO,SAAS,MAAM,GAAG;AACvB,SAAO,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;AAClC;;;ACKO,SAAS,YAAY,OAAO,OAAO;AAExC,MAAI,iBAAiB,MAAM,MAAM,GAAG,GAChC,iBAAiB,MAAM,MAAM,GAAG;AAEpC,MAAI,sBAAsB,KAAK,IAAI,cAAc,IAAI,KAAK,IAAI,cAAc;AAE5E,SAAO,sBAAsB;AAC/B;AAQO,SAAS,IAAI,OAAO,OAAO;AAChC,SAAO,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI,MAAM,KAAK,KAAK,CAAC;AACvD;;;ACAA,IAAI,OAAO,KAAK,QAAQ,SAAS,GAAG;AAClC,SAAO,KAAK,IAAI,IAAI;AACtB;AAEA,IAAI,QAAQ,EAAE,KAAK,KAAK,KAAK,EAAE;AAA/B,IACI,YAAY;AAEhB,IAAI,kBAAkB;AAEtB,IAAI,gBAAgB;AAeL,SAAR,WAA4B,QAAQ,UAAU,QAAQ;AAE3D,WAAS,UAAU,CAAC;AAEpB,OAAK,WAAW;AAEhB,OAAK,UAAU;AACf,OAAK,aAAa,OAAO;AAEzB,OAAK,eAAe,KAAK,KAAK,cAAc,IAAI;AAEhD,OAAK,cAAc;AACnB,OAAK,SAAS,OAAO,SAAS;AAE9B,MAAI,OAAO;AAEX,WAAS,GAAG,eAAe,SAASE,IAAG;AACrC,SAAK,MAAM,OAAO,YAAY,KAAK;AAAA,EACrC,CAAC;AACH;AAEA,WAAW,UAAU;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AACF;AAKA,WAAW,UAAU,SAAS,SAAS,OAAOC,QAAO;AACnD,OAAK,QAAQ,OAAOA,MAAK;AAC3B;AAGA,WAAW,UAAU,QAAQ,SAAS,QAAQ;AAC5C,OAAK,QAAQ,KAAK,cAAc;AAClC;AAQA,WAAW,UAAU,OAAO,SAAS,KAAKA,QAAO,UAAU;AAGzD,MAAI,WAAW,YAAY,OAAO,YAAY,CAAC;AAG/C,OAAK,eAAeA;AAEpB,MAAI,KAAK,IAAI,KAAK,WAAW,IAAI,iBAAiB;AAChD,SAAK,MAAMA,QAAO,UAAU,QAAQ;AAGpC,SAAK,cAAc;AAAA,EACrB;AACF;AAGA,WAAW,UAAU,eAAe,SAAS,YAAYC,QAAO;AAG9D,MAAI,QAAWA,OAAM,QAAQ,mBAAmB,IAAI,GAAG;AACrD;AAAA,EACF;AAEA,MAAI,UAAU,KAAK;AAEnB,EAAAA,OAAM,eAAe;AAKrB,MAAI,SAASA,OAAM,WAAY,MAAM,KAAKA,OAAM;AAEhD,MAAI,qBAAqBA,OAAM;AAE/B,MAAI,SAAS,KAAK,KAAK,QACnBD;AAEJ,MAAI,QAAQ;AACV,cAAUC,OAAM,cAAc,IAAI,OAAQ;AAAA,EAC5C,OAAO;AACL,cAAUA,OAAM,cAAc,IAAI,IAAM;AAAA,EAC1C;AAEA,MAAI,QAAQ;AACV,QAAI,cAAc,QAAQ,sBAAsB;AAEhD,QAAI,SAAS;AAAA,MACX,GAAGA,OAAM,UAAU,YAAY;AAAA,MAC/B,GAAGA,OAAM,UAAU,YAAY;AAAA,IACjC;AAEA,IAAAD,SACE,KAAK;AAAA,MACH,KAAK,IAAIC,OAAM,QAAQ,CAAC,IACxB,KAAK,IAAIA,OAAM,QAAQ,CAAC;AAAA,IAC1B,IAAI,KAAKA,OAAM,MAAM,IAAI;AAI3B,SAAK,KAAKD,QAAO,MAAM;AAAA,EACzB,OAAO;AAEL,QAAI,oBAAoB;AACtB,MAAAA,SAAQ;AAAA,QACN,IAAI,SAASC,OAAM;AAAA,QACnB,IAAI;AAAA,MACN;AAAA,IACF,OAAO;AACL,MAAAD,SAAQ;AAAA,QACN,IAAI,SAASC,OAAM;AAAA,QACnB,IAAI,SAASA,OAAM;AAAA,MACrB;AAAA,IACF;AAEA,SAAK,OAAOD,MAAK;AAAA,EACnB;AACF;AAQA,WAAW,UAAU,WAAW,SAAS,SAASA,QAAO,UAAU;AAEjE,MAAI,WAAW,YAAY,OAAO,SAAS;AAE3C,OAAK,MAAMA,QAAO,UAAU,QAAQ;AACtC;AAUA,WAAW,UAAU,QAAQ,SAASA,QAAO,UAAU,UAAU;AAC/D,MAAI,SAAS,KAAK;AAElB,MAAI,YAAYA,SAAQ,IAAI,IAAI;AAEhC,MAAI,yBAAyB,MAAM,OAAO,KAAK,CAAC;AAGhD,MAAI,qBAAqB,KAAK,MAAM,yBAAyB,QAAQ,IAAI;AAGzE,wBAAsB,WAAW;AAIjC,MAAI,kBAAkB,KAAK,IAAI,IAAI,kBAAkB;AAErD,SAAO,KAAK,IAAI,OAAO,eAAe,GAAG,QAAQ;AACnD;AAQA,WAAW,UAAU,SAAS,SAAS,OAAO,YAAY;AAExD,MAAI,UAAU,KAAK;AACnB,MAAIE,eAAc,KAAK;AAEvB,MAAI,aAAa,KAAK;AAEtB,MAAI,OAAO,eAAe,aAAa;AACrC,iBAAa,CAAC;AAAA,EAChB;AAGA,MAAI,eAAe,YAAY;AAI7B,UAAS,aAAa,SAAS,QAAQ,EAAE,SAAS,SAASA,cAAa,KAAK;AAAA,EAC/E;AAEA,OAAK,WAAW;AAEhB,SAAO;AACT;AAGA,WAAW,UAAU,QAAQ,SAAS,YAAY;AAChD,OAAK,OAAO,UAAU;AACxB;;;ACtPA,IAAO,qBAAQ;AAAA,EACb,UAAU,CAAE,YAAa;AAAA,EACzB,YAAY,CAAE,QAAQ,UAAW;AACnC;;;ACYe,SAAR,gBAAiC,SAAS;AAC/C,SAAO,KAAK,MAAM,OAAO;AAC3B;AAEA,EAAS,iBAAiB,MAAM;AAGhC,gBAAgB,UAAU,qBAAqB;AAAA,EAC7C;AAAA,EACA;AAAA,EACA;AACF;AAEA,gBAAgB,UAAU,WAAW,CAAC,EAAE;AAAA,EACtC,OAAO,UAAU;AAAA,EACjB,gBAAgB,UAAU;AAC5B;", "names": ["event", "event", "event", "event", "config", "classes", "e", "event", "delta", "e", "delta", "event", "handleWheel"]}