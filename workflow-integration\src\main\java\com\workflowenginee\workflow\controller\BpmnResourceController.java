package com.workflowenginee.workflow.controller;

import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.ProcessDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/workflow/bpmn")
@CrossOrigin(origins = "*")
@Slf4j
public class BpmnResourceController {

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private org.flowable.engine.RuntimeService runtimeService;

    /**
     * Get all process definitions from both deployed processes and resource files
     */
    @GetMapping("/definitions")
    public ResponseEntity<?> getProcessDefinitions() {
        try {
            System.out.println("Fetching all process definitions");
            
            // Get deployed process definitions from Flowable
            List<ProcessDefinition> deployedDefinitions = repositoryService.createProcessDefinitionQuery()
                    .latestVersion()
                    .list();

            // Get BPMN files from resources/processes folder
            List<Map<String, Object>> resourceDefinitions = getBpmnResourceFiles();

            // Combine both sources
            List<Map<String, Object>> allDefinitions = new ArrayList<>();
            
            // Add deployed definitions
            for (ProcessDefinition def : deployedDefinitions) {
                Map<String, Object> definition = new HashMap<>();
                definition.put("id", def.getId());
                definition.put("key", def.getKey());
                definition.put("name", def.getName() != null ? def.getName() : def.getKey());
                definition.put("version", def.getVersion());
                definition.put("deploymentId", def.getDeploymentId());
                definition.put("category", def.getCategory());
                definition.put("description", def.getDescription());
                definition.put("suspended", def.isSuspended());
                definition.put("tenantId", def.getTenantId());
                definition.put("source", "deployed");
                allDefinitions.add(definition);
            }
            
            // Add resource definitions (not yet deployed)
            allDefinitions.addAll(resourceDefinitions);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", allDefinitions);
            response.put("total", allDefinitions.size());
            response.put("deployed", deployedDefinitions.size());
            response.put("resources", resourceDefinitions.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("Error fetching process definitions: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error fetching process definitions: " + e.getMessage()
            ));
        }
    }

    /**
     * Get BPMN XML for a specific process definition
     */
    @GetMapping("/definition/{processDefinitionId}/xml")
    public ResponseEntity<?> getProcessDefinitionXml(@PathVariable String processDefinitionId) {
        try {
            System.out.println("Fetching BPMN XML for process definition: " + processDefinitionId);

            String bpmnXml = null;

            // First try to get from deployed processes
            try {
                ProcessDefinition processDefinition = repositoryService.getProcessDefinition(processDefinitionId);
                if (processDefinition != null) {
                    InputStream xmlStream = repositoryService.getResourceAsStream(
                        processDefinition.getDeploymentId(), 
                        processDefinition.getResourceName()
                    );
                    bpmnXml = new String(xmlStream.readAllBytes(), StandardCharsets.UTF_8);
                    System.out.println("Found BPMN XML from deployed process");
                }
            } catch (Exception e) {
                System.out.println("Process definition not found in deployed processes: " + processDefinitionId);
            }

            // If not found in deployed processes, try resource files
            if (bpmnXml == null) {
                bpmnXml = getBpmnXmlFromResources(processDefinitionId);
            }

            if (bpmnXml != null) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_XML);
                return ResponseEntity.ok()
                    .headers(headers)
                    .body(bpmnXml);
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of(
                    "success", false,
                    "message", "BPMN XML not found for process definition: " + processDefinitionId
                ));
            }

        } catch (Exception e) {
            System.err.println("Error fetching BPMN XML for process definition: " + processDefinitionId + " - " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error fetching BPMN XML: " + e.getMessage()
            ));
        }
    }

    /**
     * Get BPMN XML for a specific process definition (alternative endpoint)
     */
    @GetMapping("/definitions/{processDefinitionId}/xml")
    public ResponseEntity<?> getProcessDefinitionXmlAlternative(@PathVariable String processDefinitionId) {
        return getProcessDefinitionXml(processDefinitionId);
    }

    /**
     * Get all BPMN resource files from the processes folder
     */
    @GetMapping("/resources")
    public ResponseEntity<?> getBpmnResources() {
        try {
            List<Map<String, Object>> resourceFiles = getBpmnResourceFiles();
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", resourceFiles,
                "total", resourceFiles.size()
            ));

        } catch (Exception e) {
            System.err.println("Error fetching BPMN resource files: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error fetching BPMN resource files: " + e.getMessage()
            ));
        }
    }

    /**
     * Deploy a BPMN resource file to Flowable engine
     */
    @PostMapping("/deploy-resource/{fileName}")
    public ResponseEntity<?> deployBpmnResource(@PathVariable String fileName) {
        try {
            System.out.println("Deploying BPMN resource file: " + fileName);

            String resourcePath = "processes/" + fileName;
            Resource resource = new ClassPathResource(resourcePath);

            if (resource.exists()) {
                try (InputStream inputStream = resource.getInputStream()) {
                    String bpmnXml = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);

                    // Deploy to Flowable
                    org.flowable.engine.repository.Deployment deployment = repositoryService.createDeployment()
                        .name("Deployment of " + fileName)
                        .addString(fileName, bpmnXml)
                        .deploy();

                    // Get the deployed process definition
                    ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                        .deploymentId(deployment.getId())
                        .singleResult();

                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("message", "BPMN resource deployed successfully");
                    response.put("deploymentId", deployment.getId());
                    response.put("processDefinitionId", processDefinition.getId());
                    response.put("processDefinitionKey", processDefinition.getKey());
                    response.put("fileName", fileName);

                    System.out.println("Successfully deployed BPMN resource: " + fileName + " with deployment ID: " + deployment.getId());
                    return ResponseEntity.ok(response);
                }
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of(
                    "success", false,
                    "message", "BPMN resource file not found: " + fileName
                ));
            }

        } catch (Exception e) {
            System.err.println("Error deploying BPMN resource file: " + fileName + " - " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error deploying BPMN resource: " + e.getMessage()
            ));
        }
    }

    /**
     * Get BPMN XML content from a resource file
     */
    @GetMapping("/resource/{fileName}/xml")
    public ResponseEntity<?> getBpmnResourceXml(@PathVariable String fileName) {
        try {
            System.out.println("Fetching BPMN XML from resource file: " + fileName);
            
            String resourcePath = "processes/" + fileName;
            Resource resource = new ClassPathResource(resourcePath);
            
            if (resource.exists()) {
                try (InputStream inputStream = resource.getInputStream()) {
                    String bpmnXml = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
                    
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_XML);
                    return ResponseEntity.ok()
                        .headers(headers)
                        .body(bpmnXml);
                }
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of(
                    "success", false,
                    "message", "BPMN resource file not found: " + fileName
                ));
            }

        } catch (Exception e) {
            System.err.println("Error fetching BPMN XML from resource file: " + fileName + " - " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error fetching BPMN XML from resource: " + e.getMessage()
            ));
        }
    }

    /**
     * Start a new process instance
     */
    @PostMapping("/start-instance/{processDefinitionKey}")
    public ResponseEntity<?> startProcessInstance(
            @PathVariable String processDefinitionKey,
            @RequestBody(required = false) Map<String, Object> variables) {
        try {
            System.out.println("Starting process instance for: " + processDefinitionKey);

            if (variables == null) {
                variables = new HashMap<>();
            }

            // Add some default variables for your workflows
            if ("Hrdc_workflow".equals(processDefinitionKey)) {
                variables.putIfAbsent("applicationNumber", "APP" + System.currentTimeMillis());
                variables.putIfAbsent("applicationType", "HRDC");
                variables.putIfAbsent("role", "Agent_lead");
            } else if ("Complaint_Workflow".equals(processDefinitionKey)) {
                variables.putIfAbsent("complaintNumber", "COMP" + System.currentTimeMillis());
                variables.putIfAbsent("complaintType", "Service");
            } else if ("Appeal_Workflow".equals(processDefinitionKey)) {
                variables.putIfAbsent("appealNumber", "APP" + System.currentTimeMillis());
                variables.putIfAbsent("appealType", "Decision Review");
            }

            org.flowable.engine.runtime.ProcessInstance processInstance = runtimeService
                .startProcessInstanceByKey(processDefinitionKey, variables);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Process instance started successfully");
            response.put("processInstanceId", processInstance.getId());
            response.put("processDefinitionKey", processDefinitionKey);
            response.put("businessKey", processInstance.getBusinessKey());
            response.put("variables", variables);

            System.out.println("Successfully started process instance: " + processInstance.getId());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("Error starting process instance: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error starting process instance: " + e.getMessage()
            ));
        }
    }









    /**
     * Helper method to get BPMN files from resources/processes folder
     */
    private List<Map<String, Object>> getBpmnResourceFiles() {
        List<Map<String, Object>> resourceFiles = new ArrayList<>();
        
        try {
            // List of known BPMN files in resources/processes
            String[] bpmnFiles = {
                "check-demo.bpmn20.xml",
                "check-demo1.bpmn20.xml", 
                "Appeal_Workflow.bpmn20.xml"
            };
            
            for (String fileName : bpmnFiles) {
                String resourcePath = "processes/" + fileName;
                Resource resource = new ClassPathResource(resourcePath);
                
                if (resource.exists()) {
                    try (InputStream inputStream = resource.getInputStream()) {
                        String content = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
                        
                        // Extract process information from BPMN XML
                        Map<String, Object> fileInfo = extractProcessInfoFromXml(content, fileName);
                        if (fileInfo != null) {
                            fileInfo.put("source", "resource");
                            fileInfo.put("fileName", fileName);
                            fileInfo.put("resourcePath", resourcePath);
                            resourceFiles.add(fileInfo);
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            System.err.println("Error reading BPMN resource files: " + e.getMessage());
        }
        
        return resourceFiles;
    }

    /**
     * Helper method to get BPMN XML from resources by process definition ID or key
     */
    private String getBpmnXmlFromResources(String processDefinitionId) {
        try {
            String[] bpmnFiles = {
                "check-demo.bpmn20.xml",
                "check-demo1.bpmn20.xml",
                "Appeal_Workflow.bpmn20.xml"
            };

            for (String fileName : bpmnFiles) {
                String resourcePath = "processes/" + fileName;
                Resource resource = new ClassPathResource(resourcePath);

                if (resource.exists()) {
                    try (InputStream inputStream = resource.getInputStream()) {
                        String content = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);

                        // Check if this file contains the requested process
                        // Match by process definition ID, filename, or extracted process key
                        String fileNameWithoutExtension = fileName.replace(".bpmn20.xml", "");
                        if (content.contains("id=\"" + processDefinitionId + "\"") ||
                            fileName.equals(processDefinitionId) ||
                            fileNameWithoutExtension.equals(processDefinitionId) ||
                            processDefinitionId.contains(fileNameWithoutExtension)) {
                            System.out.println("Found matching BPMN XML in resource file: " + fileName);
                            return content;
                        }

                        // Also try to match by process key extracted from XML
                        String processKey = extractXmlAttribute(content, "process", "id");
                        if (processKey != null && processKey.equals(processDefinitionId)) {
                            System.out.println("Found matching BPMN XML by process key: " + processKey + " in file: " + fileName);
                            return content;
                        }
                    }
                }
            }

            System.out.println("No matching BPMN XML found in resources for process definition: " + processDefinitionId);

        } catch (Exception e) {
            System.err.println("Error reading BPMN XML from resources: " + e.getMessage());
        }

        return null;
    }

    /**
     * Helper method to extract process information from BPMN XML
     */
    private Map<String, Object> extractProcessInfoFromXml(String xmlContent, String fileName) {
        try {
            Map<String, Object> processInfo = new HashMap<>();
            
            // Extract process ID
            String processId = extractXmlAttribute(xmlContent, "process", "id");
            String processName = extractXmlAttribute(xmlContent, "process", "name");
            
            if (processId != null) {
                processInfo.put("id", fileName.replace(".bpmn20.xml", "")); // Use filename as ID for resources
                processInfo.put("key", processId);
                processInfo.put("name", processName != null ? processName : processId);
                processInfo.put("version", 1);
                processInfo.put("deploymentId", null);
                processInfo.put("category", null);
                processInfo.put("description", "BPMN process from resources folder");
                processInfo.put("suspended", false);
                processInfo.put("tenantId", null);
                
                return processInfo;
            }
            
        } catch (Exception e) {
            System.err.println("Error extracting process info from XML: " + fileName + " - " + e.getMessage());
        }
        
        return null;
    }

    /**
     * Helper method to extract XML attributes
     */
    private String extractXmlAttribute(String xmlContent, String elementName, String attributeName) {
        try {
            String pattern = "<" + elementName + "\\s+[^>]*" + attributeName + "=\"([^\"]+)\"";
            java.util.regex.Pattern regex = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher matcher = regex.matcher(xmlContent);
            
            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            System.out.println("Error extracting XML attribute: " + attributeName + " from element: " + elementName);
        }
        
        return null;
    }
}
